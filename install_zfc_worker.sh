#!/usr/bin/env bash
#--------------------------------------------
# 首次安装 zf-worker 的脚本
# 功能:
#   1) 生成 /root/zfc/config.json
#   2) 从控制端获取最新版本信息并下载 zf-worker-$version
#   3) 创建软链接 /root/zfc/zf-worker.new
#   4) 生成/更新 /etc/systemd/system/zfc.service
#   5) 启动并设置开机自启
#
# 依赖:
#   - curl
#   - jq (用于解析 JSON)
#
# 参数: 
#   - INTERFACE_NAME
#--------------------------------------------

set -e
apt update
apt-get -y install jq curl
if [ $# -lt 1 ]; then
  echo "用法: $0<INTERFACE_NAME>"
  exit 1
fi

ARRANGER_URL={ARRANGER_URL_STR}
MGMT_ARRANGER_PUBLIC_KEY={MGMT_ARRANGER_PUBLIC_KEY_STR}
PRIVATE_KEY={PRIVATE_KEY_STR}
INTERFACE_NAME="$1"
PORT_START={PORT_START_STR}
PORT_END={PORT_END_STR}
MGMT_HAMMER_PROXY={MGMT_HAMMER_PROXY_STR}
OUTBOUND_PROXY_ADDR={OUTBOUND_PROXY_ADDR_STR}
INSTALL_DIR="/root/zf"
SERVICE_NAME="zf"
SYSTEMD_SERVICE="/etc/systemd/system/${SERVICE_NAME}.service"

# 检查 ARRANGER_URL 是否以 http:// 或 https:// 开头
if ! [[ "$ARRANGER_URL" =~ ^https?:// ]]; then
  echo "错误：ARRANGER_URL 必须以 http:// 或 https:// 开头"
  exit 1
fi

# 去除 ARRANGER_URL 尾部可能存在的 '/' 字符
ARRANGER_URL="${ARRANGER_URL%/}"

echo "=== 开始安装 zf-worker ==="

#--------------------------------------------
# 检查依赖: curl, jq
#--------------------------------------------
if ! command -v curl &>/dev/null; then
  echo "未安装 curl，请先安装后重试。"
  exit 1
fi

if ! command -v jq &>/dev/null; then
  echo "未安装 jq，请先安装后重试。"
  exit 1
fi

# 设置时间同步
apt update
apt install -y systemd-timesyncd
systemctl enable systemd-timesyncd
systemctl start systemd-timesyncd

#--------------------------------------------
# 为 mgmt_ws_endpoint 生成 ws/wss 协议的地址
# 根据 ARRANGER_URL 的开头自动替换 http->ws, https->wss
#--------------------------------------------
MGMT_WS_ENDPOINT="$(echo "$ARRANGER_URL" | sed 's/^https:/ws:/; s/^http:/ws:/'):3100/w"

#--------------------------------------------
# 创建安装目录 /root/zfc (如果不存在)
#--------------------------------------------
mkdir -p "${INSTALL_DIR}"

CONFIG_FILE="${INSTALL_DIR}/config.json"

if [ -f "$CONFIG_FILE" ]; then
  echo "检测到已安装的 zf-worker，执行增量安装..."
  
  # 解析现有配置文件
  EXISTING_CONFIG=$(jq '.' "$CONFIG_FILE")
  
  # 添加新的 backends 入口
  NEW_BACKEND="{
    \"iface\": \"${INTERFACE_NAME}\",
    \"mgmt_priv_key\": \"${PRIVATE_KEY}\",
    \"port_start\": ${PORT_START},
    \"port_end\": ${PORT_END},
    \"outbound_proxy_addr\": \"${MGMT_HAMMER_PROXY}\",
    \"traffic_filter\": {PROTOCOL_FILTERS_JSON}
  }"
  
  UPDATED_CONFIG=$(echo "$EXISTING_CONFIG" | jq ".backends += [$NEW_BACKEND]")
  echo "$UPDATED_CONFIG" > "$CONFIG_FILE"
  echo "已更新 ${CONFIG_FILE}，增加新接口 ${INTERFACE_NAME}"
  systemctl restart "${SERVICE_NAME}"
  exit 0
fi

#--------------------------------------------
# 生成 /root/zfc/config.json 配置文件
#--------------------------------------------
cat > "${INSTALL_DIR}/config.json" <<EOF
{
  "tc_path": "tc",
  "mgmt_arranger_public_key": "${MGMT_ARRANGER_PUBLIC_KEY}",
  "mgmt_upgrade_url": "${ARRANGER_URL}",
  "mgmt_ws_endpoint": "${MGMT_WS_ENDPOINT}",
  "mgmt_hammer_proxy": "${MGMT_HAMMER_PROXY}",
  "stats": {
    "enable": true,
    "report_interval": 3
  },
  "backends": [
    {
      "iface": "${INTERFACE_NAME}",
      "mgmt_priv_key": "${PRIVATE_KEY}",
      "port_start": ${PORT_START},
      "port_end": ${PORT_END},
      "outbound_proxy_addr": "${OUTBOUND_PROXY_ADDR}",
      "traffic_filter": {PROTOCOL_FILTERS_JSON}
    }
  ]
}
EOF

echo "已生成 ${INSTALL_DIR}/config.json 文件:"
cat "${INSTALL_DIR}/config.json"
echo

#--------------------------------------------
# 从控制端获取最新版本信息:
#   格式 { "version":"...", "download_url":"..." }
#--------------------------------------------
VERSION_API="${ARRANGER_URL}/api/worker/version"
echo "尝试从 ${VERSION_API} 获取版本信息..."
VERSION_JSON="$(curl -s -S -H "worker-pubkey: {WORKER_PUBKEY}" "${VERSION_API}")" || {
  echo "获取版本信息失败，请检查 ARRANGER_URL 是否可连通: ${VERSION_API}"
  exit 1
}
echo "VERSION_JSON: ${VERSION_JSON}"

# 解析 version 和 download_url
VERSION="$(echo "${VERSION_JSON}" | jq -r '.version')"
DOWNLOAD_URL="$(echo "${VERSION_JSON}" | jq -r '.download_url // empty')"

if [ -z "${DOWNLOAD_URL}" ] || [ "${DOWNLOAD_URL}" = "null" ]; then
  echo "版本信息中不存在 download_url, 无法继续安装。VERSION_JSON=${VERSION_JSON}"
  exit 1
fi

if [ -z "${VERSION}" ] || [ "${VERSION}" = "null" ]; then
  echo "版本信息中不存在 version, 无法继续安装。VERSION_JSON=${VERSION_JSON}"
  exit 1
fi

echo "获取到版本: ${VERSION}"
echo "下载地址:   ${DOWNLOAD_URL}"
echo

#--------------------------------------------
# 下载最新版本 zf-worker-${VERSION}
#--------------------------------------------
TARGET_BIN="${INSTALL_DIR}/zf-worker-${VERSION}"
echo "开始下载 zf-worker-${VERSION} 到 ${TARGET_BIN} ..."
curl -H "worker-pubkey: {WORKER_PUBKEY}" -L "${DOWNLOAD_URL}" -o "${TARGET_BIN}"
chmod +x "${TARGET_BIN}"

echo "下载完成。"

#--------------------------------------------
# 创建/更新软链接 zf-worker.new -> zf-worker-${VERSION}
#--------------------------------------------
ln -sf "zf-worker-${VERSION}" "${INSTALL_DIR}/zf-worker.new"
echo "已创建软链接 ${INSTALL_DIR}/zf-worker.new -> zf-worker-${VERSION}"

#--------------------------------------------
# 创建/更新 systemd service 文件
# 注意 ExecStart 指向 /root/zfc/zf-worker.new
#--------------------------------------------
cat > "${SYSTEMD_SERVICE}" <<EOF
[Unit]
Description=zf-worker
After=network.target

[Service]
User=root
WorkingDirectory=${INSTALL_DIR}
ExecStart=${INSTALL_DIR}/zf-worker.new
Restart=always
LimitNOFILE=524288

[Install]
WantedBy=multi-user.target
EOF

echo "已生成/更新 systemd 服务文件: ${SYSTEMD_SERVICE}"

#--------------------------------------------
# 使 systemd 单元生效并启动
#--------------------------------------------
systemctl daemon-reload
systemctl enable "${SERVICE_NAME}"
systemctl restart "${SERVICE_NAME}"

echo
echo "=== zf-worker 首次安装完成 ==="
echo "Service文件: ${SYSTEMD_SERVICE}"
echo "安装目录: ${INSTALL_DIR}"
echo "当前版本: ${VERSION}"
echo
echo "你可以使用 systemctl status ${SERVICE_NAME} 查看服务状态。"