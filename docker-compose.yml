version: '3.8'

services:
  # PostgreSQL 数据库 - 主数据库
  postgres:
    image: postgres:16-alpine
    container_name: zfc-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-zfc}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - zfc-network
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-zfc}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL 数据库 - 认证服务数据库
  postgres-auth:
    image: postgres:16-alpine
    container_name: zfc-postgres-auth
    environment:
      POSTGRES_DB: ${AUTH_POSTGRES_DB:-zfc_auth}
      POSTGRES_USER: ${AUTH_POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${AUTH_POSTGRES_PASSWORD}
    volumes:
      - postgres_auth_data:/var/lib/postgresql/data
    networks:
      - zfc-network
    ports:
      - "${AUTH_POSTGRES_PORT:-5433}:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${AUTH_POSTGRES_USER:-postgres} -d ${AUTH_POSTGRES_DB:-zfc_auth}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: zfc-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - zfc-network
    ports:
      - "${REDIS_PORT:-6379}:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # TDengine 时序数据库
  tdengine:
    image: tdengine/tdengine:latest
    container_name: zfc-tdengine
    environment:
      TAOS_FQDN: tdengine
      TAOS_FIRST_EP: tdengine:6030
      TAOS_SERVER_PORT: 6030
      TAOS_HTTP_PORT: 6041
      TAOS_LOG_DIR: /var/log/taos
      TAOS_DATA_DIR: /var/lib/taos
    volumes:
      - tdengine_data:/var/lib/taos
      - tdengine_log:/var/log/taos
      - ./init-tdengine.sql:/docker-entrypoint-initdb.d/init-tdengine.sql:ro
    networks:
      - zfc-network
    ports:
      - "${TDENGINE_PORT:-6041}:6041"  # HTTP REST API port
      - "${TDENGINE_TCP_PORT:-6030}:6030"  # TCP port for native connections
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6041/rest/sql/zfc", "-u", "${TDENGINE_USER:-zfcuser}:${TDENGINE_PASSWORD}", "-d", "show databases"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ZF 认证服务器
  zf-auth-server:
    build:
      context: .
      dockerfile: Dockerfile.auth-server
    container_name: zfc-auth-server
    environment:
      ZF_AUTH_DATABASE_URL: postgresql://${AUTH_POSTGRES_USER:-postgres}:${AUTH_POSTGRES_PASSWORD}@postgres-auth:5432/${AUTH_POSTGRES_DB:-zfc_auth}
      ZF_AUTH_JWT_SECRET: ${JWT_SECRET}
      ZF_AUTH_BIND_ADDRESS: 0.0.0.0:8080
      ZF_AUTH_LOG_LEVEL: ${LOG_LEVEL:-info}
      RUST_LOG: ${RUST_LOG:-info}
    depends_on:
      postgres-auth:
        condition: service_healthy
    networks:
      - zfc-network
    ports:
      - "${AUTH_SERVER_PORT:-8080}:8080"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ZF 控制器
  zf-controller:
    build:
      context: .
      dockerfile: Dockerfile.controller
    container_name: zfc-controller
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-zfc}
      REDIS_URL: redis://default:${REDIS_PASSWORD}@redis:6379
      TDENGINE_URL: http://tdengine:6041
      TDENGINE_USER: ${TDENGINE_USER:-zfcuser}
      TDENGINE_PASSWORD: ${TDENGINE_PASSWORD}
      TDENGINE_DB: ${TDENGINE_DB:-zfc}
      ZF_AUTH_SERVER_URL: http://zf-auth-server:8080
      CONTROLLER_BIND_ADDRESS: 0.0.0.0:3000
      RUST_LOG: ${RUST_LOG:-info}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      tdengine:
        condition: service_healthy
      zf-auth-server:
        condition: service_healthy
    networks:
      - zfc-network
    ports:
      - "${CONTROLLER_PORT:-3000}:3000"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ZF Web 服务 (嵌入前端版本)
  zf-web:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        CARGO_FEATURES: embed
    container_name: zfc-web
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-zfc}
      REDIS_URL: redis://default:${REDIS_PASSWORD}@redis:6379
      ZF_CONTROLLER_URL: http://zf-controller:3000
      ZF_AUTH_SERVER_URL: http://zf-auth-server:8080
      WEB_BIND_ADDRESS: 0.0.0.0:3030
      WEB_DOMAIN: ${WEB_DOMAIN}
      CORS_ORIGINS: ${CORS_ORIGINS:-*}
      RUST_LOG: ${RUST_LOG:-info}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      zf-controller:
        condition: service_healthy
      zf-auth-server:
        condition: service_healthy
    networks:
      - zfc-network
    ports:
      - "${WEB_PORT:-3030}:3030"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3030/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  zfc-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  postgres_auth_data:
    driver: local
  redis_data:
    driver: local
  tdengine_data:
    driver: local
  tdengine_log:
    driver: local