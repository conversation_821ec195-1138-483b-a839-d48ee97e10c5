# ZFC Docker Compose 环境配置示例
# 复制此文件为 .env 并根据您的环境修改相应值

# =============================================================================
# 必填配置项 - 请务必修改这些值
# =============================================================================

# PostgreSQL 主数据库密码
POSTGRES_PASSWORD=your_secure_postgres_password_here

# PostgreSQL 认证服务数据库密码
AUTH_POSTGRES_PASSWORD=your_secure_auth_postgres_password_here

# Redis 密码
REDIS_PASSWORD=your_secure_redis_password_here

# InfluxDB 管理员密码
INFLUXDB_PASSWORD=your_secure_influxdb_password_here

# JWT 密钥 (用于认证服务)
JWT_SECRET=your_jwt_secret_key_here

# Web 服务域名 (用于 CORS 和服务配置)
WEB_DOMAIN=your-domain.com

# =============================================================================
# 可选配置项 - 可根据需要调整
# =============================================================================

# 数据库配置
POSTGRES_DB=zfc
POSTGRES_USER=postgres
POSTGRES_PORT=5432

AUTH_POSTGRES_DB=zfc_auth
AUTH_POSTGRES_USER=postgres
AUTH_POSTGRES_PORT=5433

# Redis 配置
REDIS_PORT=6379

# InfluxDB 配置  
INFLUXDB_PORT=8086
INFLUXDB_USERNAME=admin
INFLUXDB_DB=metrics
INFLUXDB_USER=zfc
INFLUXDB_USER_PASSWORD=your_influxdb_user_password_here

# 服务端口配置
AUTH_SERVER_PORT=8080
CONTROLLER_PORT=3000
WEB_PORT=3030

# 应用配置
LOG_LEVEL=info
RUST_LOG=info
CORS_ORIGINS=*

# =============================================================================
# 配置说明
# =============================================================================

# POSTGRES_PASSWORD: 主数据库的 PostgreSQL 密码
# AUTH_POSTGRES_PASSWORD: 认证服务数据库的 PostgreSQL 密码
# REDIS_PASSWORD: Redis 缓存服务的密码
# INFLUXDB_PASSWORD: InfluxDB 时序数据库的管理员密码
# INFLUXDB_USER_PASSWORD: InfluxDB 应用用户密码
# JWT_SECRET: JWT 令牌签名密钥，请使用长度不少于 32 字符的随机字符串
# WEB_DOMAIN: 您的域名，用于配置 CORS 和服务访问

# 端口说明:
# - 5432: PostgreSQL 主数据库
# - 5433: PostgreSQL 认证数据库  
# - 6379: Redis 缓存
# - 8086: InfluxDB 时序数据库
# - 8080: ZF 认证服务器
# - 3000: ZF 控制器
# - 3030: ZF Web 服务 (包含嵌入的前端)

# 安全建议:
# 1. 请为所有密码使用强密码 (至少 16 字符，包含大小写字母、数字和特殊字符)
# 2. JWT_SECRET 应该是一个长的随机字符串
# 3. 在生产环境中，建议修改默认端口
# 4. 根据需要配置 CORS_ORIGINS，避免使用 * 通配符

# InfluxDB 数据保留说明:
# - 数据将自动在7天后删除以节省存储空间
# - 如需调整保留期，请修改 influxdb.conf 中的保留策略配置