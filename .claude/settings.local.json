{"permissions": {"allow": ["mcp__graphiti-memory__search_memory_nodes", "mcp__graphiti-memory__add_memory", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run dev:*)", "Bash(cp:*)", "Bash(cargo build:*)", "mcp__graphiti-memory__search_memory_facts", "Bash(cargo check:*)", "Bash(cargo prisma:*)", "Bash(cargo run:*)", "Bash(rm:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(npm install:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": []}