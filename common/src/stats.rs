use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

// Redis缓存用的结构体
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CachedNetSpeedItem {
    pub rx: u64,       // unit: byte/s
    pub tx: u64,       // unit: byte/s
    pub total_rx: u64, // unit: byte
    pub total_tx: u64, // unit: byte
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CachedSystemStatsItem {
    pub cpu_usage: f32,    // percentage
    pub memory_total: u64, // unit: byte
    pub memory_used: u64,  // unit: byte
    pub uptime: u64,       // unit: second
    pub tcp_connections: u32, // total TCP connections count
    pub udp_connections: u32, // total UDP connections count
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CachedUserLineSpeedItem {
    pub subscription_id: i32,
    pub line_id: i32,
    pub line_name: Option<String>,
    pub upload_speed: f64,   // unit: byte/s
    pub download_speed: f64, // unit: byte/s
    pub client_ips_count: i32, // count of client_ips
    pub connection_count: i32, // count of connection
}

// TDengine measurement structures
// Tags are now regular fields and will be handled in SQL INSERT statements
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetcardSpeedMeasurement {
    pub time: DateTime<Utc>,
    pub interface: String, // tag field in TDengine
    pub agent_id: i32,     // tag field in TDengine
    pub tx: f64,           // unit: Byte/s
    pub rx: f64,           // unit: Byte/s
    pub total_tx: u64,     // unit: Byte
    pub total_rx: u64,     // unit: Byte
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemStatsMeasurement {
    pub time: DateTime<Utc>,
    pub agent_id: i32,        // tag field in TDengine
    pub cpu_usage: f32,       // percentage
    pub memory_total: u64,    // unit: byte
    pub memory_used: u64,     // unit: byte
    pub uptime: u64,          // unit: second
    pub tcp_connections: u32, // total TCP connections count
    pub udp_connections: u32, // total UDP connections count
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserLineInfoMeasurement {
    pub time: DateTime<Utc>,
    pub subscription_id: i32,  // tag field in TDengine
    pub line_id: i32,          // tag field in TDengine
    pub traffic_inc: u64,      // unit: Byte
    pub total_speed: f64,      // unit: Byte/s
    pub client_ips: String,    // json string Vec<SocketAddr>
    pub client_ips_count: i32, // count of client_ips
    pub connection_count: i32, // count of connection
}

// TDengine helper implementations
impl NetcardSpeedMeasurement {
    pub fn measurement_name() -> &'static str {
        "netcard_speed"
    }

    pub fn to_insert_sql(&self) -> String {
        let table_name = format!(
            "netcard_speed_{}_{}",
            self.agent_id,
            self.interface.replace('-', "_").replace(' ', "_")
        );
        format!(
            "INSERT INTO {} USING netcard_speed TAGS ({}, '{}') VALUES ('{}', {}, {}, {}, {})",
            table_name,
            self.agent_id,
            self.interface,
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.tx,
            self.rx,
            self.total_tx,
            self.total_rx
        )
    }

    pub fn to_values_part(&self) -> String {
        format!(
            "('{}', {}, {}, {}, {})",
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.tx,
            self.rx,
            self.total_tx,
            self.total_rx
        )
    }

    pub fn table_key(&self) -> String {
        format!("{}_{}", self.agent_id, self.interface)
    }

    pub fn get_table_sql(&self) -> String {
        let table_name = format!(
            "netcard_speed_{}_{}",
            self.agent_id,
            self.interface.replace('-', "_").replace(' ', "_")
        );
        format!(
            "INSERT INTO {} USING netcard_speed TAGS ({}, '{}')",
            table_name,
            self.agent_id,
            self.interface
        )
    }
}

impl SystemStatsMeasurement {
    pub fn measurement_name() -> &'static str {
        "system_stats"
    }

    pub fn to_insert_sql(&self) -> String {
        let table_name = format!("system_stats_{}", self.agent_id);
        format!(
            "INSERT INTO {} USING system_stats TAGS ({}) VALUES ('{}', {}, {}, {}, {}, {}, {})",
            table_name,
            self.agent_id,
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.cpu_usage,
            self.memory_total,
            self.memory_used,
            self.uptime,
            self.tcp_connections,
            self.udp_connections
        )
    }

    pub fn to_values_part(&self) -> String {
        format!(
            "('{}', {}, {}, {}, {}, {}, {})",
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.cpu_usage,
            self.memory_total,
            self.memory_used,
            self.uptime,
            self.tcp_connections,
            self.udp_connections
        )
    }

    pub fn table_key(&self) -> String {
        format!("{}", self.agent_id)
    }

    pub fn get_table_sql(&self) -> String {
        let table_name = format!("system_stats_{}", self.agent_id);
        format!(
            "INSERT INTO {} USING system_stats TAGS ({})",
            table_name,
            self.agent_id
        )
    }
}

impl UserLineInfoMeasurement {
    pub fn measurement_name() -> &'static str {
        "user_line_info"
    }

    pub fn to_insert_sql(&self) -> String {
        let table_name = format!("user_line_info_{}_{}", self.subscription_id, self.line_id);
        format!(
            "INSERT INTO {} USING user_line_info TAGS ({}, {}) VALUES ('{}', {}, {}, '{}', {}, {})",
            table_name,
            self.subscription_id,
            self.line_id,
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.traffic_inc,
            self.total_speed,
            self.client_ips,
            self.client_ips_count,
            self.connection_count
        )
    }

    pub fn to_values_part(&self) -> String {
        format!(
            "('{}', {}, {}, '{}', {}, {})",
            self.time.format("%Y-%m-%d %H:%M:%S%.3f"),
            self.traffic_inc,
            self.total_speed,
            self.client_ips,
            self.client_ips_count,
            self.connection_count
        )
    }

    pub fn table_key(&self) -> String {
        format!("{}_{}", self.subscription_id, self.line_id)
    }

    pub fn get_table_sql(&self) -> String {
        let table_name = format!("user_line_info_{}_{}", self.subscription_id, self.line_id);
        format!(
            "INSERT INTO {} USING user_line_info TAGS ({}, {})",
            table_name,
            self.subscription_id,
            self.line_id
        )
    }
}
