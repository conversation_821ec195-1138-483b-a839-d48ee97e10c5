use anyhow::{bail, Result};
use chacha20poly1305::{
    aead::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, KeyInit, OsRng},
    ChaCha20Poly1305, <PERSON>, Nonce,
};
use chrono::{DateTime, SecondsFormat, Utc};
use k256::{
    ecdh::{diffie_hellman, SharedSecret},
    ecdsa::{signature::Signer, signature::Verifier, Signature, SigningKey, VerifyingKey},
    sha2::Sha256,
    <PERSON>Key, SecretKey,
};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, net::SocketAddr, str::FromStr};

use crate::AddressPort;

#[derive(Clone, Deserialize, Serialize, Debug)]
pub enum Protocol {
    Hammer { config: String },
    Tot { config: String },
}

#[derive(Debug, Clone, Copy, serde::Serialize, serde::Deserialize, Hash, PartialEq, Eq)]
pub enum BalanceMode {
    DomainFollow,
    RoundR<PERSON><PERSON>,
    <PERSON>,
}

impl Into<u32> for BalanceMode {
    fn into(self) -> u32 {
        match self {
            BalanceMode::DomainFollow => 0,
            BalanceMode::RoundRobin => 1,
            BalanceMode::Random => 2,
        }
    }
}
impl From<private_tun::snell_impl_ver::server_provider::BalanceMode> for BalanceMode {
    fn from(value: private_tun::snell_impl_ver::server_provider::BalanceMode) -> Self {
        match value {
            private_tun::snell_impl_ver::server_provider::BalanceMode::DomainFollow => {
                BalanceMode::DomainFollow
            }
            private_tun::snell_impl_ver::server_provider::BalanceMode::RoundRobin => {
                BalanceMode::RoundRobin
            }
            private_tun::snell_impl_ver::server_provider::BalanceMode::Random => {
                BalanceMode::Random
            }
        }
    }
}

#[derive(
    Debug, Clone, Copy, serde::Serialize, serde::Deserialize, Hash, PartialEq, Eq, Default,
)]
pub enum Mode {
    #[default]
    BestLatency,
    FallBack,
    Balance {
        balance_mode: BalanceMode,
    },
}
impl Into<u32> for Mode {
    fn into(self) -> u32 {
        match self {
            Mode::BestLatency => 0,
            Mode::FallBack => 1,
            Mode::Balance { balance_mode } => 2 as u32 + Into::<u32>::into(balance_mode),
        }
    }
}
impl TryFrom<u32> for Mode {
    type Error = anyhow::Error;
    fn try_from(value: u32) -> Result<Self, Self::Error> {
        match value {
            0 => Ok(Mode::BestLatency),
            1 => Ok(Mode::FallBack),
            2 => Ok(Mode::Balance {
                balance_mode: BalanceMode::DomainFollow,
            }),
            3 => Ok(Mode::Balance {
                balance_mode: BalanceMode::RoundRobin,
            }),
            4 => Ok(Mode::Balance {
                balance_mode: BalanceMode::Random,
            }),
            _ => bail!("invalid mode"),
        }
    }
}

impl TryFrom<private_tun::snell_impl_ver::server_provider::Mode> for Mode {
    type Error = anyhow::Error;
    fn try_from(
        value: private_tun::snell_impl_ver::server_provider::Mode,
    ) -> Result<Self, Self::Error> {
        match value {
            private_tun::snell_impl_ver::server_provider::Mode::BestLatency => {
                Ok(Mode::BestLatency)
            }
            private_tun::snell_impl_ver::server_provider::Mode::Fallback => Ok(Mode::FallBack),
            private_tun::snell_impl_ver::server_provider::Mode::Balance { balance_mode } => {
                Ok(Mode::Balance {
                    balance_mode: BalanceMode::from(balance_mode),
                })
            }
            _ => Err(anyhow::anyhow!("invalid mode")),
        }
    }
}

#[derive(
    Debug, Clone, Copy, serde::Serialize, serde::Deserialize, Hash, Default, PartialEq, Eq,
)]
pub enum LatencyTestMethod {
    #[default]
    Tcpping,
    Icmp,
}

impl Into<u32> for LatencyTestMethod {
    fn into(self) -> u32 {
        match self {
            LatencyTestMethod::Tcpping => 0,
            LatencyTestMethod::Icmp => 1,
        }
    }
}

impl TryFrom<u32> for LatencyTestMethod {
    type Error = anyhow::Error;
    fn try_from(value: u32) -> Result<Self, Self::Error> {
        match value {
            0 => Ok(LatencyTestMethod::Tcpping),
            1 => Ok(LatencyTestMethod::Icmp),
            _ => bail!("invalid latency test method"),
        }
    }
}

impl FromStr for LatencyTestMethod {
    type Err = anyhow::Error;
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "tcpping" => Ok(LatencyTestMethod::Tcpping),
            "icmp" => Ok(LatencyTestMethod::Icmp),
            _ => bail!("invalid latency test method: {}", s),
        }
    }
}

impl FromStr for Mode {
    type Err = anyhow::Error;
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "best_latency" => Ok(Mode::BestLatency),
            "fall_back" => Ok(Mode::FallBack),
            "balance_domain_follow" => Ok(Mode::Balance {
                balance_mode: BalanceMode::DomainFollow,
            }),
            "balance_round_robin" => Ok(Mode::Balance {
                balance_mode: BalanceMode::RoundRobin,
            }),
            "balance_random" => Ok(Mode::Balance {
                balance_mode: BalanceMode::Random,
            }),
            _ => bail!("invalid mode: {}", s),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PingResult {
    pub ip: String,
    pub rtt: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum TotProxyType {
    Direct(AddressPort),
    Hammer(String),
}

#[derive(Debug, Serialize, Deserialize, Hash, Eq, PartialEq, Clone)]
pub struct TotClientConfig {
    pub fwd_config: Vec<String>, // forwarder list support direct or hammer proxy
    pub server_list: Vec<String>, // tot server list
    pub test_method: LatencyTestMethod, // test server's latency
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TotServerConfig {}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TestLatencyInfo {
    pub test_type: TestLatencyType,
    pub test_method: LatencyTestMethod,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WorkerTestLatencyRequest {
    pub worker_id: i32,
    pub fwd_configs: Vec<Protocol>,
    pub info: Vec<TestLatencyInfo>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkerTestLatencyResponse {
    pub latencies: Vec<u64>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WorkerFetchGlobalLatestConfigResponse {
    pub proxy_config: Option<String>,
    pub interface_name: String,
    pub use_forward_as_tun: Option<bool>,
    pub traffic_filter: Option<Vec<String>>,
}

#[derive(Clone, Deserialize, Serialize)]
pub struct PortConfig {
    pub port: u16,
    pub subscription_id: i32,
    pub bandwidth: Option<u16>,
    pub avail_traffics: u128,
    pub target_addr_list: Vec<SocketAddr>,
    pub mode: Option<Mode>,
    pub protocol: Option<Protocol>,
    pub latency_test_method: Option<LatencyTestMethod>,
    pub allow_ip_num: Option<i32>,
    pub allow_conn_num: Option<i32>,
}

pub type PortMap = HashMap<u16, PortConfig>;

#[derive(Clone, Deserialize, Serialize, Debug)]
pub struct PortTrafficUsage {
    pub port: u16,
    pub used: u128,
    pub client_ip: Option<SocketAddr>,
}

#[derive(Clone, Deserialize, Serialize)]
pub enum AppQueryRequest {
    Encrypted(EncryptedMessage),
    Error(String),
}

#[derive(Clone, Deserialize, Serialize)]
pub enum AppQueryResponse {
    Encrypted(EncryptedMessage),
    Error(String),
}

#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct StatsReport {
    pub netcard_speed: HashMap<String, (u64, u64, u64, u64)>, // interface -> (rx, tx, total_rx, total_tx) unit: byte/s
    pub cpu_usage: f32,                                       // unit: %
    pub memory_usage: (u64, u64),                             // (used, total) unit: byte
    pub uptime: u64,                                          // unit: second
    pub tcp_connections: u32,                                 // total TCP connections count
    pub udp_connections: u32,                                 // total UDP connections count
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct StatData {
    pub port: u16,
    pub client_ip: Option<SocketAddr>,
    pub forward_endpoint: String,
    pub traffic: u64,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum TestLatencyType {
    ToFwdServer {
        fwd_name: String,
    },
    FwdServerToRemote {
        fwd_name: String,
        remote: SocketAddr,
    },
    DirectRemote {
        remote: SocketAddr,
    },
    // Direct to remote over tot stream
    TotDirectRemote {
        remote: SocketAddr,
    },
}

#[derive(Clone, Deserialize, Serialize)]
pub enum AppWsRequest {
    Encrypted(EncryptedMessage),
    Error(String),
    Pong,
    PortTrafficUsage(Vec<PortTrafficUsage>),
    ShouldDumpPortMap,
    StatsReport(StatsReport),
    TestLatencyResp {
        request_id: u64,
        latencies: Vec<u64>,
    },
    ForwardEndpointTrafficReport(Vec<StatData>),
}

#[derive(Clone, Deserialize, Serialize)]
pub enum AppWsResponse {
    Encrypted(EncryptedMessage),
    Error(String),
    Ping,
    PortMapUpdate(PortMap),
    ShouldReportPortTrafficUsage,
    TestLatencyReq {
        request_id: u64,
        fwd_configs: Vec<Protocol>,
        info: Vec<TestLatencyInfo>,
    },
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkerVersionReportRequest {
    pub version: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkerVersionInfo {
    pub version: String,
    pub download_url: Option<String>,
    pub hash: Option<String>,
}

#[derive(Clone, Deserialize, Serialize)]
pub enum AppError {
    PlainError(String),
    EncryptedError(Vec<u8>),
}

#[derive(Clone, Deserialize, Serialize)]
pub struct EncryptedMessage {
    buf: Vec<u8>,
    nonce: Vec<u8>,
}

impl EncryptedMessage {
    pub fn create(buf: Vec<u8>, key: &SharedSecret) -> Result<Self> {
        let mut okm = [0u8; 32];
        if let Err(e) = key.extract::<Sha256>(None).expand(&[], &mut okm) {
            bail!(e.to_string())
        }
        let key = Key::from_slice(&okm);
        let cipher = ChaCha20Poly1305::new(&key);
        let nonce = ChaCha20Poly1305::generate_nonce(&mut OsRng);
        match cipher.encrypt(&nonce, buf.as_slice()) {
            Ok(buf) => {
                return Ok(Self {
                    buf,
                    nonce: nonce.to_vec(),
                })
            }
            Err(e) => bail!(e.to_string()),
        }
    }
    pub fn decrypt(&self, key: &SharedSecret) -> Result<Vec<u8>> {
        let mut okm = [0u8; 32];
        if let Err(e) = key.extract::<Sha256>(None).expand(&[], &mut okm) {
            bail!(e.to_string())
        }
        let key = Key::from_slice(&okm);
        let cipher = ChaCha20Poly1305::new(&key);
        let nonce = Nonce::from_slice(self.nonce.as_slice());
        match cipher.decrypt(&nonce, self.buf.as_slice()) {
            Ok(buf) => return Ok(buf),
            Err(e) => bail!(e.to_string()),
        }
    }
}

pub fn sign_message<K: Into<SigningKey>>(
    key: K,
    message: Vec<u8>,
) -> Result<(Vec<u8>, DateTime<Utc>)> {
    let key: SigningKey = key.into();
    let time = Utc::now();
    let ts = time.to_rfc3339_opts(SecondsFormat::Millis, true);
    let ts = ts.as_bytes();
    let message = message.as_ref();
    let message = [ts, message].concat();

    let signature: Signature = key.sign(message.as_slice());
    Ok((signature.to_vec(), time))
}

pub fn check_signature<K: Into<VerifyingKey>>(
    key: K,
    message: Vec<u8>,
    time: DateTime<Utc>,
    singature: Vec<u8>,
    ignore_time_difference: bool,
) -> Result<bool> {
    let key: VerifyingKey = key.into();
    let now = Utc::now();

    let td = now - time;
    let td = td.num_seconds();

    if !ignore_time_difference && !(td < 15 && td > -15) {
        println!(
            "Time difference check did't pass: now={:?} signed_on={:?} Δ={:?}",
            &now, &time, td
        );
        return Ok(false);
    }

    let ts = time.to_rfc3339_opts(SecondsFormat::Millis, true);
    let ts = ts.as_bytes();
    let message = message.as_ref();
    let message = [ts, message].concat();
    let singature: Signature = Signature::from_slice(singature.as_ref())?;

    Ok(key.verify(message.as_slice(), &singature).is_ok())
}

pub fn get_shared_key(k1: &SecretKey, k2: &PublicKey) -> Result<SharedSecret> {
    Ok(diffie_hellman(k1.to_nonzero_scalar(), k2.as_affine()))
}
