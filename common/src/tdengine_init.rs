use std::{ops::Deref, str::FromStr};

use anyhow::Result;
use log::{error, info};
use mdsn::Dsn;
use taos::{AsyncQueryable, AsyncT<PERSON><PERSON>er, TaosBuilder, TaosPool};

pub struct TaosClient {
    pool: TaosPool,
}

impl TaosClient {
    pub async fn new(pool: TaosPool) -> Result<Self> {
        Ok(Self { pool })
    }
}

impl Deref for TaosClient {
    type Target = TaosPool;
    fn deref(&self) -> &Self::Target {
        &self.pool
    }
}

pub async fn setup_tdengine(url: &str, db_name: &str) -> Result<TaosClient> {
    let url_parsed = mdsn::Dsn::from_str(url)?;
    let db_name = url_parsed
        .subject
        .as_ref()
        .cloned()
        .unwrap_or(db_name.to_string());
    info!("Using database: {}", db_name);
    // 检查url 是否指定数据库
    if let Err(e) = initialize_tdengine_schema(&url_parsed, &db_name).await {
        error!("Failed to initialize TDengine schema: {}", e);
        return Err(e);
    }
    let taos = TaosBuilder::from_dsn(url)?.pool()?;
    Ok(TaosClient::new(taos).await?)
}
/// Initialize TDengine database schema
/// Creates the database and required super tables if they don't exist
async fn initialize_tdengine_schema(dsn: &Dsn, db_name: &str) -> Result<()> {
    info!("Initializing TDengine database and tables...");

    let mut mdsn_no_db = dsn.clone();
    mdsn_no_db.subject = None;
    let taos = TaosBuilder::from_dsn(mdsn_no_db)?.build().await?;

    // Create database if not exists
    let create_db_sql = format!(
        "CREATE DATABASE IF NOT EXISTS {} DURATION 1d KEEP 7d",
        db_name
    );
    info!("Executing SQL: {}", create_db_sql);

    if let Err(e) = taos.exec(&create_db_sql).await {
        error!("Failed to create database {}: {}", db_name, e);
        taos.create_database(db_name).await?;
        return Err(anyhow::anyhow!("Failed to create database: {}. This might indicate TDengine server is not properly configured or the REST API endpoint is incorrect.", e));
    }
    info!("Database {} created or already exists", db_name);

    // Use the database
    let use_db_sql = format!("USE {}", db_name);
    if let Err(e) = taos.exec(&use_db_sql).await {
        error!("Failed to use database {}: {}", db_name, e);
        return Err(anyhow::anyhow!("Failed to use database: {}", e));
    }

    // Create super tables
    let tables = vec![
        // netcard_speed super table
        r#"CREATE STABLE IF NOT EXISTS netcard_speed (
            ts TIMESTAMP,
            tx DOUBLE,
            rx DOUBLE,
            total_tx BIGINT,
            total_rx BIGINT
        ) TAGS (
            agent_id INT,
            interface NCHAR(50)
        )"#,
        // system_stats super table
        r#"CREATE STABLE IF NOT EXISTS system_stats (
            ts TIMESTAMP,
            cpu_usage FLOAT,
            memory_total BIGINT,
            memory_used BIGINT,
            uptime BIGINT,
            tcp_connections INT,
            udp_connections INT
        ) TAGS (
            agent_id INT
        )"#,
        // user_line_info super table
        r#"CREATE STABLE IF NOT EXISTS user_line_info (
            ts TIMESTAMP,
            traffic_inc BIGINT,
            total_speed DOUBLE,
            client_ips NCHAR(2000),
            client_ips_count INT,
            connection_count INT
        ) TAGS (
            subscription_id INT,
            line_id INT
        )"#,
    ];

    for (i, table_sql) in tables.into_iter().enumerate() {
        if let Err(e) = taos.exec(table_sql).await {
            error!("Failed to create table {}: {}", i + 1, e);
            return Err(anyhow::anyhow!("Failed to create table: {}", e));
        }
    }

    info!("TDengine schema initialized successfully");
    Ok(())
}
