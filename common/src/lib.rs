pub mod app_message;
pub mod crypto;

use std::{
    collections::HashMap,
    hash::Hash,
    net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddr},
    ops::Deref,
    sync::Arc,
};

use app_message::{AppWsRequest, AppWsResponse, EncryptedMessage, LatencyTestMethod, Mode};
pub use chrono;
pub use dotenv;
pub use hex;
use k256::ecdh::SharedSecret;
use log::error;
use private_tun::{address::Address, snell_impl_ver::config::ClientConfig};
use redis_lock::{DistributedLock, RedisLockGuard};
use reset_traffic_task::RedisPool;
pub use rmp_serde;
use serde::{Deserialize, Serialize};
use sha2::{Digest, Sha256};
use socket2::{Domain, Protocol, SockAddr, Socket, Type};
pub use tcp_over_multi_tcp_client::Config as TotGlobalConfig;
use tokio::{net::TcpListener, sync::Mutex};
pub use tokio_util;
pub mod cache;
pub mod constant;
pub mod hammer_message;
pub mod increment_stat;
#[allow(warnings)]
#[path = "prisma.generated.rs"]
pub mod prisma;
pub mod prisma_tx_guard;
pub mod rechecker;
pub mod redis_lock;
pub mod reset_traffic_task;
pub mod retry_queue;
pub mod server_provider;
pub mod stats;
pub mod tdengine_init;
pub mod batch_writer;

#[derive(Clone)]
pub struct PingClient {
    v4_client: Arc<std::sync::LazyLock<surge_ping::Client>>,
    v6_client: Arc<std::sync::LazyLock<surge_ping::Client>>,
}

impl PingClient {
    pub fn new() -> Self {
        Self {
            v4_client: Arc::new(std::sync::LazyLock::new(|| {
                let mut config = surge_ping::Config::default();
                config.kind = surge_ping::ICMP::V4;
                surge_ping::Client::new(&config).unwrap()
            })),
            v6_client: Arc::new(std::sync::LazyLock::new(|| {
                let mut config = surge_ping::Config::default();
                config.kind = surge_ping::ICMP::V6;
                surge_ping::Client::new(&config).unwrap()
            })),
        }
    }
    pub fn v4_client(&self) -> surge_ping::Client {
        self.v4_client.deref().deref().clone()
    }
    pub fn v6_client(&self) -> surge_ping::Client {
        self.v6_client.deref().deref().clone()
    }
}

#[derive(Debug, Clone, Hash, Serialize, Deserialize)]
pub struct TotConfig {
    pub fwd_config: ClientConfig, // forwarder list support direct or hammer proxy
    pub tot_server_list: ClientConfig, // tot server list
    pub tot_server_select_mode: Mode,
    pub tot_server_test_method: LatencyTestMethod,
    pub tot_config: TotGlobalConfig,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum AddressPort {
    Domain(String, u16),
    SocketAddr(SocketAddr),
}

pub fn parse_target(addr: &str, port: u16) -> anyhow::Result<AddressPort> {
    let ip_addr = match addr.parse::<IpAddr>() {
        Ok(ip) => ip,
        Err(_e) => {
            return Ok(AddressPort::Domain(addr.to_owned(), port));
        }
    };
    Ok(AddressPort::SocketAddr(SocketAddr::new(ip_addr, port)))
}

pub fn replace_placeholders(template: &str, replacements: &HashMap<&str, &str>) -> String {
    let mut result = template.to_string();
    for (placeholder, value) in replacements {
        // 假设占位符是 `{placeholder}` 这种写法
        let key = format!("{{{}}}", placeholder);
        result = result.replace(&key, value);
    }
    result
}

pub async fn calculate_hash(version: String, content: &[u8]) -> anyhow::Result<String> {
    let mut hash = Sha256::new();
    hash.update(version.as_bytes());
    hash.update(content);
    Ok(hex::encode(hash.finalize()))
}

pub fn encode_tx_message(msg: &AppWsRequest, key: &SharedSecret) -> Result<Vec<u8>, anyhow::Error> {
    let buf = rmp_serde::to_vec(msg)?;
    let enc = EncryptedMessage::create(buf, key)?;
    let msg = AppWsRequest::Encrypted(enc);
    Ok(rmp_serde::to_vec(&msg)?)
}

pub fn encode_rx_message(
    msg: &AppWsResponse,
    key: &SharedSecret,
) -> Result<Vec<u8>, anyhow::Error> {
    let buf = rmp_serde::to_vec(msg)?;
    let enc = EncryptedMessage::create(buf, key)?;
    let msg = AppWsResponse::Encrypted(enc);
    Ok(rmp_serde::to_vec(&msg)?)
}

pub fn normalize_ip(ip: IpAddr) -> IpAddr {
    match ip {
        IpAddr::V6(ipv6) => ipv6.to_ipv4().map(IpAddr::V4).unwrap_or(IpAddr::V6(ipv6)),
        other => other,
    }
}

pub async fn into_socket_addr(addr: &Address) -> anyhow::Result<SocketAddr> {
    match addr {
        Address::Socket(addr) => Ok(addr.clone()),
        Address::Domain(addr, port) => {
            if let Ok(ip) = addr.parse::<IpAddr>() {
                return Ok(SocketAddr::new(ip, *port));
            }
            let mut ip_list = match tokio::net::lookup_host((addr.to_string(), *port)).await {
                Ok(v) => v,
                Err(e) => {
                    log::error!("lookup host: {} failed: {}", addr.to_string(), e);
                    return Err(anyhow::anyhow!(
                        "lookup host: {} failed: {}",
                        addr.to_string(),
                        e
                    ));
                }
            };
            if let Some(addr) = ip_list.next() {
                Ok(SocketAddr::new(addr.ip(), *port))
            } else {
                Err(anyhow::anyhow!(
                    "lookup host: {} but no ip found",
                    addr.to_string()
                ))
            }
        }
    }
}

pub fn calc_hash_id(
    remote_list: &Vec<Address>,
    mode: &Mode,
    latency_test_method: &LatencyTestMethod,
) -> String {
    let mut hasher = Sha256::new();
    hasher.update(serde_json::to_vec(remote_list).unwrap().as_slice());
    hasher.update(serde_json::to_vec(mode).unwrap().as_slice());
    hasher.update(serde_json::to_vec(latency_test_method).unwrap().as_slice());
    hex::encode(hasher.finalize())
}

pub fn replace_address_port(addr: &Address, port: u16) -> Address {
    match addr {
        Address::Socket(addr) => Address::Socket(SocketAddr::new(addr.ip(), port)),
        Address::Domain(addr, _) => Address::Domain(addr.clone(), port),
    }
}
pub type RedisLineLock = Arc<Mutex<HashMap<i32, Arc<DistributedLock>>>>;
pub async fn get_line_lock(
    redis: &RedisPool,
    redis_line_lock: &RedisLineLock,
    line_id: i32,
) -> Result<RedisLockGuard, anyhow::Error> {
    match redis_line_lock
        .lock()
        .await
        .entry(line_id)
        .or_insert_with(|| {
            Arc::new(DistributedLock::new(
                redis.clone(),
                &format!("traffic_line:{}", line_id),
                5000,
            ))
        })
        .clone()
        .acquire()
        .await
    {
        Ok(g) => Ok(g),
        Err(_e) => {
            error!(
                "line_id:{} forward endpoint traffic usage get line lock failed",
                line_id
            );
            return Err(anyhow::anyhow!(
                "line_id:{} forward endpoint traffic usage get line lock failed",
                line_id
            ));
        }
    }
}

pub fn is_ipv6_supported() -> bool {
    let test_addr = SocketAddr::from((Ipv6Addr::UNSPECIFIED, 0)); // Port 0 lets the OS choose an available port
    let socket_addr = socket2::SockAddr::from(test_addr);
    let Ok(socket) = Socket::new(Domain::IPV6, Type::DGRAM, Some(Protocol::UDP)) else {
        return false;
    };
    socket.bind(&socket_addr).is_ok()
}

pub fn get_bind_addr_for_port(port: u16) -> SocketAddr {
    if is_ipv6_supported() {
        SocketAddr::new(IpAddr::V6(Ipv6Addr::UNSPECIFIED), port)
    } else {
        SocketAddr::new(IpAddr::V4(Ipv4Addr::UNSPECIFIED), port)
    }
}

pub fn get_bind_addr() -> SocketAddr {
    if is_ipv6_supported() {
        SocketAddr::new(IpAddr::V6(Ipv6Addr::UNSPECIFIED), 0)
    } else {
        SocketAddr::new(IpAddr::V4(Ipv4Addr::UNSPECIFIED), 0)
    }
}

pub fn create_tcp_listener(bind_addr: SocketAddr) -> std::io::Result<TcpListener> {
    let socket = if bind_addr.ip().is_ipv4() {
        socket2::Socket::new(
            socket2::Domain::IPV4,
            socket2::Type::STREAM,
            Some(socket2::Protocol::TCP),
        )?
    } else {
        socket2::Socket::new(
            socket2::Domain::IPV6,
            socket2::Type::STREAM,
            Some(socket2::Protocol::TCP),
        )?
    };
    socket.set_reuse_port(true)?;
    socket.set_tcp_nodelay(true)?;
    socket.bind(&SockAddr::from(bind_addr))?;
    socket.listen(i32::MAX)?;
    socket.set_nonblocking(true)?;
    Ok(TcpListener::from_std(socket.into())?)
}
