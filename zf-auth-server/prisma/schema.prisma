// This is your Prisma schema file for zf-auth-server
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "cargo prisma"
  output   = "../src/prisma.generated.rs"
}

datasource db {
  provider = "postgresql"
  url      = env("ZF_AUTH_DATABASE_URL")
}

model ControllerInstance {
  id               String    @id @default(cuid())
  name             String    @unique
  licenseExpiresAt DateTime
  
  /// API Key for instance-level authentication
  apiKey           String    @unique @default(cuid())
  
  /// Stores all feature flags and limits for this instance.
  /// Example: { "max_workers": 10, "enable_feature_x": true }
  entitlements     Json      @default("{}")

  // -- Fields for Concurrency Control --
  /// The IP address of the last client to successfully perform a heartbeat.
  lastSeenIp       String?
  /// The timestamp of the last successful heartbeat.
  lastSeenAt       DateTime?
  /// A unique ID for the current "active" session/lease.
  currentSessionId String?   @unique

  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  
  renewalCodes     RenewalCode[]

  @@index([name])
  @@index([licenseExpiresAt])
  @@index([currentSessionId])
  @@index([apiKey])
}

model RenewalCode {
  id                 String    @id @default(cuid())
  duration           Int       // License extension in days
  createdAt          DateTime  @default(now())
  usedAt             DateTime?
  usedByInstanceId   String?
  usedByInstance     ControllerInstance? @relation(fields: [usedByInstanceId], references: [id])

  @@index([usedAt])
  @@index([usedByInstanceId])
}

model AdminUser {
  id             String    @id @default(cuid())
  username       String    @unique
  passwordHash   String
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  lastLoginAt    DateTime?

  @@index([username])
}