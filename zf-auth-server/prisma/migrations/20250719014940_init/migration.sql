-- CreateTable
CREATE TABLE "ControllerInstance" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "licenseExpiresAt" TIMESTAMP(3) NOT NULL,
    "apiKey" TEXT NOT NULL,
    "entitlements" JSONB NOT NULL DEFAULT '{}',
    "lastSeenIp" TEXT,
    "lastSeenAt" TIMESTAMP(3),
    "currentSessionId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ControllerInstance_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RenewalCode" (
    "id" TEXT NOT NULL,
    "duration" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "usedAt" TIMESTAMP(3),
    "usedByInstanceId" TEXT,

    CONSTRAINT "RenewalCode_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AdminUser" (
    "id" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "passwordHash" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastLoginAt" TIMESTAMP(3),

    CONSTRAINT "AdminUser_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ControllerInstance_name_key" ON "ControllerInstance"("name");

-- CreateIndex
CREATE UNIQUE INDEX "ControllerInstance_apiKey_key" ON "ControllerInstance"("apiKey");

-- CreateIndex
CREATE UNIQUE INDEX "ControllerInstance_currentSessionId_key" ON "ControllerInstance"("currentSessionId");

-- CreateIndex
CREATE INDEX "ControllerInstance_name_idx" ON "ControllerInstance"("name");

-- CreateIndex
CREATE INDEX "ControllerInstance_licenseExpiresAt_idx" ON "ControllerInstance"("licenseExpiresAt");

-- CreateIndex
CREATE INDEX "ControllerInstance_currentSessionId_idx" ON "ControllerInstance"("currentSessionId");

-- CreateIndex
CREATE INDEX "ControllerInstance_apiKey_idx" ON "ControllerInstance"("apiKey");

-- CreateIndex
CREATE INDEX "RenewalCode_usedAt_idx" ON "RenewalCode"("usedAt");

-- CreateIndex
CREATE INDEX "RenewalCode_usedByInstanceId_idx" ON "RenewalCode"("usedByInstanceId");

-- CreateIndex
CREATE UNIQUE INDEX "AdminUser_username_key" ON "AdminUser"("username");

-- CreateIndex
CREATE INDEX "AdminUser_username_idx" ON "AdminUser"("username");

-- AddForeignKey
ALTER TABLE "RenewalCode" ADD CONSTRAINT "RenewalCode_usedByInstanceId_fkey" FOREIGN KEY ("usedByInstanceId") REFERENCES "ControllerInstance"("id") ON DELETE SET NULL ON UPDATE CASCADE;
