use anyhow::Result;
use chrono::{Duration, Utc};
use serde_json::json;
use std::sync::Arc;
use zf_auth_server::{
    auth::AuthService,
    crypto::KeyManager,
    models::HeartbeatRequest,
    prisma::PrismaClient,
};

#[tokio::test]
async fn test_heartbeat_flow() -> Result<()> {
    // Setup
    let db = Arc::new(PrismaClient::_builder()
        .with_url("sqlite://test.db".to_string())
        .build()
        .await?);
    
    let key_manager = Arc::new(KeyManager::new()?);
    let auth_service = Arc::new(AuthService::new(db.clone(), key_manager, 24, 24));
    
    // Create test instance
    let instance_name = "test-instance";
    let expires_at = Utc::now() + Duration::days(30);
    let entitlements = json!({
        "max_workers": 5,
        "feature_udp_forwarding_enabled": true
    });
    
    let instance_id = auth_service
        .create_instance(instance_name.to_string(), expires_at, entitlements)
        .await?;
    
    // Test initial heartbeat
    let request = HeartbeatRequest {
        instance_id: instance_id.id.clone(),
    };
    
    let token1 = auth_service
        .heartbeat(request.clone(), "127.0.0.1".to_string(), None)
        .await?;
    
    assert!(!token1.is_empty());
    
    // Test JWT verification with RSA public key
    let claims = auth_service.key_manager.verify_jwt(&token1)?;
    assert_eq!(claims.sub, instance_id.id);
    
    // Test heartbeat with existing token
    let token2 = auth_service
        .heartbeat(request.clone(), "127.0.0.1".to_string(), Some(token1))
        .await?;
    
    assert!(!token2.is_empty());
    
    // Test heartbeat from different IP (should fail due to concurrency control)
    let result = auth_service
        .heartbeat(request, "***********".to_string(), None)
        .await;
    
    assert!(result.is_err());
    
    Ok(())
}

#[tokio::test]
async fn test_renewal_code_flow() -> Result<()> {
    // Setup
    let db = Arc::new(PrismaClient::_builder()
        .with_url("sqlite://test_renewal.db".to_string())
        .build()
        .await?);
    
    let key_manager = Arc::new(KeyManager::new()?);
    let auth_service = Arc::new(AuthService::new(db.clone(), key_manager, 24, 24));
    
    // Create test instance
    let instance_name = "test-renewal-instance";
    let expires_at = Utc::now() + Duration::days(1); // Short expiry
    let entitlements = json!({
        "max_workers": 3
    });
    
    let instance_id = auth_service
        .create_instance(instance_name.to_string(), expires_at, entitlements)
        .await?;
    
    // Create renewal code
    let renewal_code_id = auth_service
        .create_renewal_code(30) // 30 days
        .await?;
    
    // Apply renewal code
    auth_service
        .apply_renewal_code(instance_id.id, renewal_code_id)
        .await?;
    
    // Verify instance expiry was extended
    let instances = auth_service.get_all_instances().await?;
    let updated_instance = instances.into_iter()
        .find(|i| i.name == instance_name)
        .unwrap();
    
    assert!(updated_instance.license_expires_at > expires_at + Duration::days(25));
    
    Ok(())
}

#[tokio::test]
async fn test_rsa_public_key_endpoint() -> Result<()> {
    // Setup
    let key_manager = Arc::new(KeyManager::new()?);
    
    // Test that we can get the public key in PEM format
    let public_key_pem = key_manager.get_rsa_public_key_pem()?;
    assert!(public_key_pem.starts_with("-----BEGIN PUBLIC KEY-----"));
    assert!(public_key_pem.ends_with("-----END PUBLIC KEY-----\n"));
    
    // Test that we can create and verify a JWT using the RSA keys
    let claims = zf_auth_server::crypto::JwtClaims {
        sub: "test-instance".to_string(),
        sid: "test-session".to_string(),
        exp: (chrono::Utc::now() + Duration::hours(1)).timestamp() as u64,
        iat: chrono::Utc::now().timestamp() as u64,
        entitlements: json!({"max_workers": 5}),
    };
    
    let token = key_manager.create_jwt(&claims)?;
    let verified_claims = key_manager.verify_jwt(&token)?;
    
    assert_eq!(verified_claims.sub, claims.sub);
    assert_eq!(verified_claims.sid, claims.sid);
    
    Ok(())
}