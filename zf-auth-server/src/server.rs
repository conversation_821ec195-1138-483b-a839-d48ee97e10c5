use crate::{
    auth::AuthService,
    config::Config,
    crypto::KeyManager,
    handlers,
    prisma::PrismaClient,
};
use axum::{
    middleware,
    routing::{delete, get, post, put},
    Router,
};
use std::sync::Arc;
use tower_http::cors::CorsLayer;

pub async fn start_server(config: Config) -> anyhow::Result<()> {
    // Initialize database
    let db = Arc::new(PrismaClient::_builder()
        .with_url(config.database_url.clone())
        .build()
        .await?);

    // Initialize key manager with RSA keys for JWT signing
    let key_manager = Arc::new(KeyManager::new()?);

    // Initialize auth service
    let auth_service = Arc::new(AuthService::new(
        db,
        key_manager,
        config.jwt_expiry_hours,
        config.session_timeout_hours,
    ));

    // Protected admin routes
    let protected_admin_routes = Router::new()
        .route("/admin/me", get(handlers::get_current_admin))
        .route("/admin/dashboard/stats", get(handlers::get_dashboard_stats))
        .route("/admin/instances", post(handlers::create_instance))
        .route("/admin/instances/batch", post(handlers::batch_create_instances))
        .route("/admin/instances", get(handlers::get_instances))
        .route("/admin/instances/:id", put(handlers::update_instance))
        .route("/admin/instances/:id", delete(handlers::delete_instance))
        .route("/admin/instances/:id/force-expire", post(handlers::force_expire_session))
        .route("/admin/renewal-codes", post(handlers::generate_renewal_codes))
        .route("/admin/renewal-codes", get(handlers::get_renewal_codes))
        .route("/admin/renewal-codes/:id", delete(handlers::delete_renewal_code))
        .route("/admin/instances/:id/renew", post(handlers::apply_renewal_code))
        .layer(middleware::from_fn_with_state(
            auth_service.clone(),
            handlers::admin_auth,
        ));

    // Instance-level routes (for software instances to query their own info)
    let instance_routes = Router::new()
        .route("/instance/:id/heartbeat", post(handlers::heartbeat))
        .route("/instance/:id/status", get(handlers::get_instance_status))
        .route("/instance/:id/renewal-history", get(handlers::get_instance_renewal_history))
        .route("/instance/:id/renew", post(handlers::apply_renewal_code_for_instance))
        .layer(middleware::from_fn_with_state(
            auth_service.clone(),
            handlers::instance_auth,
        ))
        .with_state(auth_service.clone());

    // Build the application
    let app = Router::new()
        // Public admin initialization endpoints
        .route("/admin/init/check", get(handlers::check_initialization))
        .route("/admin/init/setup", post(handlers::create_admin_user))
        .route("/admin/login", post(handlers::admin_login))
        // Public key endpoint for JWT verification
        .route("/public-key", get(handlers::get_public_key))
        // Merge protected routes
        .merge(protected_admin_routes)
        .merge(instance_routes)
        .with_state(auth_service)
        .layer(CorsLayer::permissive());

    // Start the server
    let listener = tokio::net::TcpListener::bind(&config.bind_address).await?;
    log::info!("ZF Auth Server listening on {}", config.bind_address);
    
    axum::serve(listener, app).await?;
    
    Ok(())
}