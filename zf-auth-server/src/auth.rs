use crate::{
    crypto::{Jw<PERSON><PERSON><PERSON><PERSON>, KeyManager},
    errors::AuthError,
    models::HeartbeatRequest,
    prisma::{admin_user, controller_instance, renewal_code, PrismaClient},
};
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::{Duration, Utc};
use prisma_client_rust::operator::*;
use std::sync::Arc;
use uuid::Uuid;

pub struct AuthService {
    db: Arc<PrismaClient>,
    pub key_manager: Arc<KeyManager>,
    jwt_expiry_hours: u64,
    session_timeout_hours: u64,
}

impl AuthService {
    pub fn new(
        db: Arc<PrismaClient>,
        key_manager: Arc<KeyManager>,
        jwt_expiry_hours: u64,
        session_timeout_hours: u64,
    ) -> Self {
        Self {
            db,
            key_manager,
            jwt_expiry_hours,
            session_timeout_hours,
        }
    }

    pub async fn heartbeat(
        &self,
        request: HeartbeatRequest,
        client_ip: String,
        existing_jwt: Option<String>,
    ) -> Result<String, AuthError> {
        let now = Utc::now();

        // Find the instance
        let instance = self
            .db
            .controller_instance()
            .find_unique(controller_instance::id::equals(request.instance_id.clone()))
            .exec()
            .await?
            .ok_or(AuthError::InstanceNotFound)?;

        // Check if license is expired
        if instance.license_expires_at < now {
            return Err(AuthError::LicenseExpired);
        }

        // Extract session ID from existing JWT if present
        let existing_session_id = if let Some(jwt) = existing_jwt {
            match self.key_manager.verify_jwt(&jwt) {
                Ok(claims) => Some(claims.sid),
                Err(_) => None, // Invalid JWT, treat as new session
            }
        } else {
            None
        };

        // Concurrency check
        if let Some(current_session_id) = &instance.current_session_id {
            if let Some(last_seen_at) = instance.last_seen_at {
                let session_timeout = Duration::hours(self.session_timeout_hours as i64);
                let is_session_active = now.signed_duration_since(last_seen_at) < session_timeout;

                if is_session_active {
                    // Session is still active, check if this is the same client
                    if let Some(ref existing_sid) = existing_session_id {
                        if existing_sid != current_session_id {
                            return Err(AuthError::LicenseInUse);
                        }
                    } else {
                        // No existing session ID provided, but there's an active session
                        return Err(AuthError::LicenseInUse);
                    }
                }
            }
        }

        // Generate new session ID if needed
        let session_id = existing_session_id.unwrap_or_else(|| Uuid::new_v4().to_string());

        // Update instance with new session information
        let updated_instance = self
            .db
            .controller_instance()
            .update(
                controller_instance::id::equals(instance.id.clone()),
                vec![
                    controller_instance::last_seen_ip::set(Some(client_ip)),
                    controller_instance::last_seen_at::set(Some(now.into())),
                    controller_instance::current_session_id::set(Some(session_id.clone())),
                ],
            )
            .exec()
            .await?;

        // Create JWT
        let exp = (now + Duration::hours(self.jwt_expiry_hours as i64)).timestamp() as u64;
        let claims = JwtClaims {
            sub: instance.id,
            sid: session_id,
            exp,
            iat: now.timestamp() as u64,
            entitlements: updated_instance.entitlements,
        };

        let token = self.key_manager.create_jwt(&claims)?;
        Ok(token)
    }

    pub async fn create_instance(
        &self,
        name: String,
        license_expires_at: chrono::DateTime<Utc>,
        entitlements: serde_json::Value,
    ) -> Result<crate::prisma::controller_instance::Data, AuthError> {
        let instance = self
            .db
            .controller_instance()
            .create(
                name,
                license_expires_at.into(),
                vec![controller_instance::entitlements::set(entitlements)],
            )
            .exec()
            .await?;

        Ok(instance)
    }

    pub async fn get_all_instances(
        &self,
    ) -> Result<Vec<crate::prisma::controller_instance::Data>, AuthError> {
        let instances = self
            .db
            .controller_instance()
            .find_many(vec![])
            .exec()
            .await?;

        Ok(instances)
    }

    pub async fn batch_create_instances(
        &self,
        name_template: String,
        initial_duration: i32,
        entitlements: serde_json::Value,
        quantity: i32,
    ) -> Result<Vec<crate::prisma::controller_instance::Data>, AuthError> {
        let now = chrono::Utc::now();
        let license_expires_at = now + chrono::Duration::days(initial_duration as i64);
        let mut instances = Vec::new();

        for i in 1..=quantity {
            let instance_name = if name_template.contains("{{index}}") {
                // If template contains {{index}}, replace it with the current index
                name_template.replace("{{index}}", &i.to_string())
            } else {
                // If template doesn't contain {{index}}, append the index to ensure uniqueness
                format!("{}-{}", name_template, i)
            };

            let instance = self
                .db
                .controller_instance()
                .create(
                    instance_name,
                    license_expires_at.into(),
                    vec![controller_instance::entitlements::set(entitlements.clone())],
                )
                .exec()
                .await?;
            instances.push(instance);
        }

        Ok(instances)
    }

    pub async fn get_instances_paginated(
        &self,
        page: u32,
        page_size: u32,
        search: Option<String>,
    ) -> Result<(Vec<crate::prisma::controller_instance::Data>, u32), AuthError> {
        let skip = ((page - 1) * page_size) as i64;
        let take = page_size as i64;

        // Build the where clause for search
        let where_clause = if let Some(search_term) = search {
            vec![or(vec![
                controller_instance::name::contains(search_term.clone()),
                controller_instance::id::contains(search_term),
            ])]
        } else {
            vec![]
        };

        // Get the total count first
        let total_count = self
            .db
            .controller_instance()
            .count(where_clause.clone())
            .exec()
            .await? as u32;

        // Get the paginated instances
        let instances = self
            .db
            .controller_instance()
            .find_many(where_clause)
            .skip(skip)
            .take(take)
            .exec()
            .await?;

        Ok((instances, total_count))
    }

    pub async fn update_instance(
        &self,
        id: String,
        name: Option<String>,
        entitlements: Option<serde_json::Value>,
    ) -> Result<crate::prisma::controller_instance::Data, AuthError> {
        let mut updates = vec![];

        if let Some(name) = name {
            updates.push(controller_instance::name::set(name));
        }

        if let Some(entitlements) = entitlements {
            updates.push(controller_instance::entitlements::set(entitlements));
        }

        if updates.is_empty() {
            // If no updates, fetch and return current instance
            let instance = self
                .db
                .controller_instance()
                .find_unique(controller_instance::id::equals(id))
                .exec()
                .await?
                .ok_or_else(|| AuthError::InstanceNotFound)?;
            return Ok(instance);
        }

        let updated_instance = self
            .db
            .controller_instance()
            .update(controller_instance::id::equals(id), updates)
            .exec()
            .await?;

        Ok(updated_instance)
    }

    pub async fn delete_instance(&self, id: String) -> Result<(), AuthError> {
        self.db
            .controller_instance()
            .delete(controller_instance::id::equals(id))
            .exec()
            .await?;
        Ok(())
    }

    pub async fn force_expire_session(&self, id: String) -> Result<(), AuthError> {
        self.db
            .controller_instance()
            .update(
                controller_instance::id::equals(id),
                vec![controller_instance::current_session_id::set(None)],
            )
            .exec()
            .await?;
        Ok(())
    }

    pub async fn create_renewal_code(&self, duration: i32) -> Result<String, AuthError> {
        let code = self
            .db
            .renewal_code()
            .create(duration, vec![])
            .exec()
            .await?;

        Ok(code.id)
    }

    pub async fn generate_renewal_codes(
        &self,
        duration: i32,
        quantity: i32,
    ) -> Result<Vec<crate::prisma::renewal_code::Data>, AuthError> {
        let mut codes = Vec::new();
        for _ in 0..quantity {
            let code = self
                .db
                .renewal_code()
                .create(duration, vec![])
                .exec()
                .await?;
            codes.push(code);
        }
        Ok(codes)
    }

    pub async fn delete_renewal_code(&self, id: String) -> Result<(), AuthError> {
        self.db
            .renewal_code()
            .delete(renewal_code::id::equals(id))
            .exec()
            .await?;
        Ok(())
    }

    pub async fn apply_renewal_code(
        &self,
        instance_id: String,
        renewal_code_id: String,
    ) -> Result<(), AuthError> {
        // Get the renewal code
        let renewal_code = self
            .db
            .renewal_code()
            .find_unique(crate::prisma::renewal_code::id::equals(
                renewal_code_id.clone(),
            ))
            .exec()
            .await?
            .ok_or_else(|| AuthError::BadRequest("Renewal code not found".to_string()))?;

        // Check if already used
        if renewal_code.used_at.is_some() {
            return Err(AuthError::BadRequest(
                "Renewal code already used".to_string(),
            ));
        }

        // Get the instance
        let instance = self
            .db
            .controller_instance()
            .find_unique(controller_instance::id::equals(instance_id.clone()))
            .exec()
            .await?
            .ok_or(AuthError::InstanceNotFound)?;

        // Calculate new expiry date
        let new_expiry = instance.license_expires_at + Duration::days(renewal_code.duration as i64);

        // Update both the instance and the renewal code
        let now = Utc::now();

        // Update instance
        self.db
            .controller_instance()
            .update(
                controller_instance::id::equals(instance_id.clone()),
                vec![controller_instance::license_expires_at::set(new_expiry)],
            )
            .exec()
            .await?;

        // Mark renewal code as used
        self.db
            .renewal_code()
            .update(
                crate::prisma::renewal_code::id::equals(renewal_code_id),
                vec![
                    crate::prisma::renewal_code::used_at::set(Some(now.into())),
                    crate::prisma::renewal_code::used_by_instance_id::set(Some(instance_id)),
                ],
            )
            .exec()
            .await?;

        Ok(())
    }

    pub async fn get_all_renewal_codes(
        &self,
    ) -> Result<Vec<crate::prisma::renewal_code::Data>, AuthError> {
        let codes = self.db.renewal_code().find_many(vec![]).exec().await?;

        Ok(codes)
    }

    pub async fn get_all_renewal_codes_available(
        &self,
    ) -> Result<Vec<crate::prisma::renewal_code::Data>, AuthError> {
        let codes = self
            .db
            .renewal_code()
            .find_many(vec![renewal_code::used_at::equals(None)])
            .exec()
            .await?;

        Ok(codes)
    }

    pub async fn get_renewal_codes_paginated(
        &self,
        page: u32,
        page_size: u32,
        status: Option<String>,
    ) -> Result<(Vec<crate::prisma::renewal_code::Data>, u32), AuthError> {
        let skip = ((page - 1) * page_size) as i64;
        let take = page_size as i64;

        // Build the where clause for status filter
        let where_clause = if let Some(status_filter) = status {
            match status_filter.as_str() {
                "available" => vec![renewal_code::used_at::equals(None)],
                "used" => vec![renewal_code::used_at::not(None)],
                _ => vec![], // Invalid status, return all
            }
        } else {
            vec![]
        };

        // Get the total count first
        let total_count = self
            .db
            .renewal_code()
            .count(where_clause.clone())
            .exec()
            .await? as u32;

        // Get the paginated renewal codes
        let codes = self
            .db
            .renewal_code()
            .find_many(where_clause)
            .skip(skip)
            .take(take)
            .exec()
            .await?;

        Ok((codes, total_count))
    }

    // Admin user management methods
    pub async fn is_initialized(&self) -> Result<bool, AuthError> {
        let admin_count = self.db.admin_user().count(vec![]).exec().await?;

        Ok(admin_count > 0)
    }

    pub async fn create_admin_user(
        &self,
        username: String,
        password: String,
    ) -> Result<String, AuthError> {
        // Check if any admin user already exists
        if self.is_initialized().await? {
            return Err(AuthError::BadRequest(
                "Admin user already exists".to_string(),
            ));
        }

        // Hash the password
        let password_hash = hash(password, DEFAULT_COST)
            .map_err(|_| AuthError::BadRequest("Failed to hash password".to_string()))?;

        // Create the admin user
        let admin_user = self
            .db
            .admin_user()
            .create(username, password_hash, vec![])
            .exec()
            .await?;

        Ok(admin_user.id)
    }

    pub async fn admin_login(
        &self,
        username: String,
        password: String,
    ) -> Result<String, AuthError> {
        // Find the admin user
        let admin_user = self
            .db
            .admin_user()
            .find_unique(admin_user::username::equals(username))
            .exec()
            .await?
            .ok_or(AuthError::Unauthorized)?;

        // Verify password
        if !verify(&password, &admin_user.password_hash)
            .map_err(|_| AuthError::BadRequest("Failed to verify password".to_string()))?
        {
            return Err(AuthError::Unauthorized);
        }

        // Update last login time
        let now = Utc::now();
        self.db
            .admin_user()
            .update(
                admin_user::id::equals(admin_user.id.clone()),
                vec![admin_user::last_login_at::set(Some(now.into()))],
            )
            .exec()
            .await?;

        // Create admin JWT
        let exp = (now + Duration::hours(24)).timestamp() as u64; // 24 hour expiry for admin tokens
        let claims = JwtClaims {
            sub: admin_user.id,
            sid: Uuid::new_v4().to_string(),
            exp,
            iat: now.timestamp() as u64,
            entitlements: serde_json::json!({"admin": true}),
        };

        let token = self.key_manager.create_jwt(&claims)?;
        Ok(token)
    }

    pub async fn verify_admin_token(&self, token: &str) -> Result<bool, AuthError> {
        let claims = self.key_manager.verify_jwt(token)?;

        // Check if the token has admin entitlements
        if let Some(admin) = claims.entitlements.get("admin") {
            if admin.as_bool() == Some(true) {
                return Ok(true);
            }
        }

        Ok(false)
    }

    pub async fn get_admin_user(
        &self,
        id: String,
    ) -> Result<crate::prisma::admin_user::Data, AuthError> {
        let admin_user = self
            .db
            .admin_user()
            .find_unique(admin_user::id::equals(id))
            .exec()
            .await?
            .ok_or(AuthError::BadRequest("Admin user not found".to_string()))?;

        Ok(admin_user)
    }

    pub async fn get_dashboard_stats(
        &self,
    ) -> Result<crate::models::DashboardStatsResponse, AuthError> {
        let now = Utc::now();
        let near_expiration_threshold = now + Duration::days(7);

        // Get all instances
        let instances = self.get_all_instances().await?;

        let mut active_instances = 0;
        let mut expired_instances = 0;
        let mut near_expiration = 0;

        for instance in instances {
            if instance.license_expires_at < now {
                expired_instances += 1;
            } else if instance.license_expires_at < near_expiration_threshold {
                near_expiration += 1;
            } else {
                active_instances += 1;
            }
        }

        // Get available renewal codes
        let renewal_codes = self.get_all_renewal_codes_available().await?;
        let available_codes = renewal_codes.len() as u64;

        Ok(crate::models::DashboardStatsResponse {
            active_instances,
            expired_instances,
            near_expiration,
            available_codes,
        })
    }

    // Get instance by ID (for instance-level API)
    pub async fn get_instance_by_id(
        &self,
        instance_id: String,
    ) -> Result<crate::prisma::controller_instance::Data, AuthError> {
        let instance = self
            .db
            .controller_instance()
            .find_unique(crate::prisma::controller_instance::id::equals(instance_id))
            .exec()
            .await?
            .ok_or_else(|| AuthError::BadRequest("Instance not found".to_string()))?;
        Ok(instance)
    }

    // Get renewal history for a specific instance
    pub async fn get_instance_renewal_history(
        &self,
        instance_id: String,
    ) -> Result<Vec<crate::models::RenewalHistoryItem>, AuthError> {
        // Query renewal codes that have been used by this instance
        let used_codes = self
            .db
            .renewal_code()
            .find_many(vec![
                crate::prisma::renewal_code::used_by_instance_id::equals(Some(instance_id.clone())),
                crate::prisma::renewal_code::used_at::not(None),
            ])
            .exec()
            .await?;

        // Convert to RenewalHistoryItem format
        let mut history = Vec::new();
        for code in used_codes {
            if let Some(used_at) = code.used_at {
                // We need to calculate the new expiry date by querying the instance
                // and subtracting the duration from the current expiry
                let instance = self.get_instance_by_id(instance_id.clone()).await?;
                let new_expiry_date = instance.license_expires_at;

                history.push(crate::models::RenewalHistoryItem {
                    date: used_at.into(),
                    code_id: code.id,
                    extended_days: code.duration,
                    new_expiry_date: new_expiry_date.into(),
                    status: "applied".to_string(),
                });
            }
        }

        // Sort by date (newest first)
        history.sort_by(|a, b| b.date.cmp(&a.date));

        Ok(history)
    }

    // Verify instance API key
    pub async fn verify_instance_api_key(
        &self,
        instance_id: &str,
        api_key: &str,
    ) -> Result<(), AuthError> {
        // Get the instance from database
        let instance = self
            .db
            .controller_instance()
            .find_unique(crate::prisma::controller_instance::id::equals(
                instance_id.to_string(),
            ))
            .exec()
            .await?
            .ok_or_else(|| AuthError::BadRequest("Instance not found".to_string()))?;

        // Verify the API key matches the stored one
        if api_key != instance.api_key {
            return Err(AuthError::Unauthorized);
        }

        Ok(())
    }
}
