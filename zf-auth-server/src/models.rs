use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

// 统一的API响应结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
        }
    }
    
    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: Some(message),
            error: None,
        }
    }
    
    pub fn error(error_message: String) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error_message),
        }
    }
}

// 分页响应结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub page: u32,
    #[serde(rename = "pageSize")]
    pub page_size: u32,
    pub total: u32,
    #[serde(rename = "totalPages")]
    pub total_pages: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeartbeatRequest {
    pub instance_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HeartbeatResponse {
    pub token: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateInstanceRequest {
    pub name: String,
    #[serde(rename = "initialDuration")]
    pub initial_duration: i32, // days
    pub entitlements: serde_json::Value,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateInstanceRequest {
    pub name: Option<String>,
    pub entitlements: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateRenewalCodeRequest {
    pub duration: i32, // days
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GenerateCodesRequest {
    pub duration: i32, // days
    pub quantity: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BatchCreateInstancesRequest {
    #[serde(rename = "nameTemplate")]
    pub name_template: String,
    #[serde(rename = "initialDuration")]
    pub initial_duration: i32, // days
    pub entitlements: serde_json::Value,
    pub quantity: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationQuery {
    pub page: Option<u32>,
    #[serde(rename = "pageSize")]
    pub page_size: Option<u32>,
    pub search: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RenewalCodeQuery {
    pub page: Option<u32>,
    #[serde(rename = "pageSize")]
    pub page_size: Option<u32>,
    pub status: Option<String>, // "available" or "used"
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApplyRenewalCodeRequest {
    pub renewal_code_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InstanceResponse {
    pub id: String,
    pub name: String,
    #[serde(rename = "licenseExpiresAt")]
    pub license_expires_at: DateTime<Utc>,
    pub entitlements: serde_json::Value,
    #[serde(rename = "lastSeenIp")]
    pub last_seen_ip: Option<String>,
    #[serde(rename = "lastSeenAt")]
    pub last_seen_at: Option<DateTime<Utc>>,
    #[serde(rename = "currentSessionId")]
    pub current_session_id: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminInstanceResponse {
    pub id: String,
    pub name: String,
    #[serde(rename = "licenseExpiresAt")]
    pub license_expires_at: DateTime<Utc>,
    #[serde(rename = "apiKey")]
    pub api_key: String,
    pub entitlements: serde_json::Value,
    #[serde(rename = "lastSeenIp")]
    pub last_seen_ip: Option<String>,
    #[serde(rename = "lastSeenAt")]
    pub last_seen_at: Option<DateTime<Utc>>,
    #[serde(rename = "currentSessionId")]
    pub current_session_id: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RenewalCodeResponse {
    pub id: String,
    pub code: String,
    pub duration: i32,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "usedAt")]
    pub used_at: Option<DateTime<Utc>>,
    #[serde(rename = "usedBy")]
    pub used_by: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateAdminUserRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminLoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminLoginResponse {
    pub token: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AdminUserResponse {
    pub id: String,
    pub username: String,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "lastLoginAt")]
    pub last_login_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CheckInitializationResponse {
    pub is_initialized: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DashboardStatsResponse {
    #[serde(rename = "activeInstances")]
    pub active_instances: u64,
    #[serde(rename = "expiredInstances")]
    pub expired_instances: u64,
    #[serde(rename = "nearExpirationInstances")]
    pub near_expiration: u64,
    #[serde(rename = "availableRenewalCodes")]
    pub available_codes: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RenewalHistoryItem {
    pub date: chrono::DateTime<chrono::Utc>,
    pub code_id: String,
    pub extended_days: i32,
    pub new_expiry_date: chrono::DateTime<chrono::Utc>,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PublicKeyResponse {
    #[serde(rename = "publicKey")]
    pub public_key: String,
    pub algorithm: String,
}