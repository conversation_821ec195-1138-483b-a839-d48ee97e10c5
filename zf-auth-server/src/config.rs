use clap::Parse<PERSON>;

#[derive(Parse<PERSON>, Debug, <PERSON><PERSON>)]
#[command(author, version, about, long_about = None)]
pub struct Config {
    #[arg(long, env = "ZF_AUTH_DATABASE_URL")]
    pub database_url: String,

    #[arg(long, env = "ZF_AUTH_BIND_ADDRESS", default_value = "0.0.0.0:8080")]
    pub bind_address: String,


    #[arg(long, env = "ZF_AUTH_JWT_EXPIRY_HOURS", default_value = "24")]
    pub jwt_expiry_hours: u64,

    #[arg(long, env = "ZF_AUTH_SESSION_TIMEOUT_HOURS", default_value = "24")]
    pub session_timeout_hours: u64,
}

impl Config {
    pub fn from_env() -> anyhow::Result<Self> {
        let config = Self::parse();
        
        // Validate required fields
        if config.database_url.is_empty() {
            return Err(anyhow::anyhow!("ZF_AUTH_DATABASE_URL is required"));
        }

        Ok(config)
    }
}