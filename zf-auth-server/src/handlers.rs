use crate::{auth::AuthService, errors::AuthError, models::*};
use axum::{
    extract::{Path, Query, State},
    http::{HeaderMap, StatusCode},
    response::<PERSON><PERSON>,
};
use std::sync::Arc;

pub async fn heartbeat(
    State(auth_service): State<Arc<AuthService>>,
    Path(instance_id): Path<String>,
    headers: HeaderMap,
    Json(_request): Json<HeartbeatRequest>,
) -> Result<Json<HeartbeatResponse>, AuthError> {
    // Extract client IP (in real deployment, this would come from a load balancer header)
    let client_ip = headers
        .get("x-forwarded-for")
        .and_then(|v| v.to_str().ok())
        .or_else(|| headers.get("x-real-ip").and_then(|v| v.to_str().ok()))
        .unwrap_or("127.0.0.1")
        .to_string();

    // Extract existing JWT from Authorization header
    let existing_jwt = headers
        .get("authorization")
        .and_then(|v| v.to_str().ok())
        .and_then(|auth| auth.strip_prefix("Bearer "))
        .map(|token| token.to_string());

    // Create a heartbeat request with the instance_id from the path
    let heartbeat_request = HeartbeatRequest {
        instance_id: instance_id,
    };

    let token = auth_service
        .heartbeat(heartbeat_request, client_ip, existing_jwt)
        .await?;

    Ok(Json(HeartbeatResponse { token }))
}

pub async fn create_instance(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<CreateInstanceRequest>,
) -> Result<(StatusCode, Json<ApiResponse<AdminInstanceResponse>>), AuthError> {
    // Calculate license expiration time
    let now = chrono::Utc::now();
    let license_expires_at = now + chrono::Duration::days(request.initial_duration as i64);

    let instance = auth_service
        .create_instance(request.name, license_expires_at, request.entitlements)
        .await?;

    let response = AdminInstanceResponse {
        id: instance.id,
        name: instance.name,
        license_expires_at: instance.license_expires_at.into(),
        api_key: instance.api_key,
        entitlements: instance.entitlements,
        last_seen_ip: instance.last_seen_ip,
        last_seen_at: instance.last_seen_at.map(|dt| dt.into()),
        current_session_id: instance.current_session_id,
        created_at: instance.created_at.into(),
        updated_at: instance.updated_at.into(),
    };

    Ok((StatusCode::CREATED, Json(ApiResponse::success(response))))
}

pub async fn batch_create_instances(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<BatchCreateInstancesRequest>,
) -> Result<Json<ApiResponse<Vec<AdminInstanceResponse>>>, AuthError> {
    let instances = auth_service
        .batch_create_instances(
            request.name_template,
            request.initial_duration,
            request.entitlements,
            request.quantity,
        )
        .await?;

    let instances_response: Vec<AdminInstanceResponse> = instances
        .into_iter()
        .map(|instance| AdminInstanceResponse {
            id: instance.id,
            name: instance.name,
            license_expires_at: instance.license_expires_at.into(),
            api_key: instance.api_key,
            entitlements: instance.entitlements,
            last_seen_ip: instance.last_seen_ip,
            last_seen_at: instance.last_seen_at.map(|dt| dt.into()),
            current_session_id: instance.current_session_id,
            created_at: instance.created_at.into(),
            updated_at: instance.updated_at.into(),
        })
        .collect();

    Ok(Json(ApiResponse::success(instances_response)))
}

pub async fn get_instances(
    State(auth_service): State<Arc<AuthService>>,
    Query(pagination): Query<PaginationQuery>,
) -> Result<Json<ApiResponse<PaginatedResponse<AdminInstanceResponse>>>, AuthError> {
    let page = pagination.page.unwrap_or(1);
    let page_size = pagination.page_size.unwrap_or(20);
    let search = pagination.search;

    // Use optimized pagination at database level
    let (instances, total) = auth_service
        .get_instances_paginated(page, page_size, search)
        .await?;

    let total_pages = (total + page_size - 1) / page_size;

    let instances_response: Vec<AdminInstanceResponse> = instances
        .into_iter()
        .map(|instance| AdminInstanceResponse {
            id: instance.id,
            name: instance.name,
            license_expires_at: instance.license_expires_at.into(),
            api_key: instance.api_key,
            entitlements: instance.entitlements,
            last_seen_ip: instance.last_seen_ip,
            last_seen_at: instance.last_seen_at.map(|dt| dt.into()),
            current_session_id: instance.current_session_id,
            created_at: instance.created_at.into(),
            updated_at: instance.updated_at.into(),
        })
        .collect();

    let paginated_response = PaginatedResponse {
        data: instances_response,
        page,
        page_size,
        total,
        total_pages,
    };

    Ok(Json(ApiResponse::success(paginated_response)))
}

pub async fn update_instance(
    State(auth_service): State<Arc<AuthService>>,
    Path(id): Path<String>,
    Json(request): Json<UpdateInstanceRequest>,
) -> Result<Json<ApiResponse<AdminInstanceResponse>>, AuthError> {
    let instance = auth_service
        .update_instance(id, request.name, request.entitlements)
        .await?;

    let response = AdminInstanceResponse {
        id: instance.id,
        name: instance.name,
        license_expires_at: instance.license_expires_at.into(),
        api_key: instance.api_key,
        entitlements: instance.entitlements,
        last_seen_ip: instance.last_seen_ip,
        last_seen_at: instance.last_seen_at.map(|dt| dt.into()),
        current_session_id: instance.current_session_id,
        created_at: instance.created_at.into(),
        updated_at: instance.updated_at.into(),
    };

    Ok(Json(ApiResponse::success(response)))
}

pub async fn delete_instance(
    State(auth_service): State<Arc<AuthService>>,
    Path(id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, AuthError> {
    auth_service.delete_instance(id).await?;
    Ok(Json(ApiResponse::success(serde_json::json!({}))))
}

pub async fn force_expire_session(
    State(auth_service): State<Arc<AuthService>>,
    Path(id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, AuthError> {
    auth_service.force_expire_session(id).await?;
    Ok(Json(ApiResponse::success(serde_json::json!({}))))
}

pub async fn create_renewal_code(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<CreateRenewalCodeRequest>,
) -> Result<(StatusCode, Json<ApiResponse<serde_json::Value>>), AuthError> {
    let id = auth_service.create_renewal_code(request.duration).await?;

    Ok((
        StatusCode::CREATED,
        Json(ApiResponse::success(serde_json::json!({ "id": id }))),
    ))
}

pub async fn apply_renewal_code(
    State(auth_service): State<Arc<AuthService>>,
    Path(instance_id): Path<String>,
    Json(request): Json<ApplyRenewalCodeRequest>,
) -> Result<Json<ApiResponse<serde_json::Value>>, AuthError> {
    auth_service
        .apply_renewal_code(instance_id, request.renewal_code_id)
        .await?;

    Ok(Json(ApiResponse::success(serde_json::json!({}))))
}

pub async fn get_renewal_codes(
    State(auth_service): State<Arc<AuthService>>,
    Query(query): Query<RenewalCodeQuery>,
) -> Result<Json<ApiResponse<PaginatedResponse<RenewalCodeResponse>>>, AuthError> {
    let page = query.page.unwrap_or(1);
    let page_size = query.page_size.unwrap_or(20);
    let status = query.status;

    // Use optimized pagination at database level
    let (codes, total) = auth_service
        .get_renewal_codes_paginated(page, page_size, status)
        .await?;

    let total_pages = (total + page_size - 1) / page_size;

    let codes_response: Vec<RenewalCodeResponse> = codes
        .into_iter()
        .map(|code| RenewalCodeResponse {
            id: code.id.clone(),
            code: code.id, // Using id as code for now, or you might want to add a separate code field
            duration: code.duration,
            created_at: code.created_at.into(),
            used_at: code.used_at.map(|dt| dt.into()),
            used_by: code.used_by_instance_id,
        })
        .collect();

    let paginated_response = PaginatedResponse {
        data: codes_response,
        page,
        page_size,
        total,
        total_pages,
    };

    Ok(Json(ApiResponse::success(paginated_response)))
}

pub async fn generate_renewal_codes(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<GenerateCodesRequest>,
) -> Result<Json<ApiResponse<Vec<RenewalCodeResponse>>>, AuthError> {
    let codes = auth_service
        .generate_renewal_codes(request.duration, request.quantity)
        .await?;

    let codes_response: Vec<RenewalCodeResponse> = codes
        .into_iter()
        .map(|code| RenewalCodeResponse {
            id: code.id.clone(),
            code: code.id,
            duration: code.duration,
            created_at: code.created_at.into(),
            used_at: code.used_at.map(|dt| dt.into()),
            used_by: code.used_by_instance_id,
        })
        .collect();

    Ok(Json(ApiResponse::success(codes_response)))
}

pub async fn delete_renewal_code(
    State(auth_service): State<Arc<AuthService>>,
    Path(id): Path<String>,
) -> Result<Json<ApiResponse<serde_json::Value>>, AuthError> {
    auth_service.delete_renewal_code(id).await?;
    Ok(Json(ApiResponse::success(serde_json::json!({}))))
}

// Check if the system is initialized (has admin user)
pub async fn check_initialization(
    State(auth_service): State<Arc<AuthService>>,
) -> Result<Json<ApiResponse<CheckInitializationResponse>>, AuthError> {
    let is_initialized = auth_service.is_initialized().await?;
    let response_data = CheckInitializationResponse { is_initialized };
    Ok(Json(ApiResponse::success(response_data)))
}

// Create the first admin user (only works if no admin user exists)
pub async fn create_admin_user(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<CreateAdminUserRequest>,
) -> Result<(StatusCode, Json<ApiResponse<serde_json::Value>>), AuthError> {
    let id = auth_service
        .create_admin_user(request.username, request.password)
        .await?;

    Ok((
        StatusCode::CREATED,
        Json(ApiResponse::success(serde_json::json!({ "id": id }))),
    ))
}

// Admin login
pub async fn admin_login(
    State(auth_service): State<Arc<AuthService>>,
    Json(request): Json<AdminLoginRequest>,
) -> Result<Json<ApiResponse<AdminLoginResponse>>, AuthError> {
    let token = auth_service
        .admin_login(request.username, request.password)
        .await?;

    let response_data = AdminLoginResponse { token };
    Ok(Json(ApiResponse::success(response_data)))
}

// Get current admin user info
pub async fn get_current_admin(
    State(auth_service): State<Arc<AuthService>>,
    headers: HeaderMap,
) -> Result<Json<ApiResponse<AdminUserResponse>>, AuthError> {
    let auth_header = headers
        .get("authorization")
        .and_then(|v| v.to_str().ok())
        .and_then(|auth| auth.strip_prefix("Bearer "))
        .ok_or(AuthError::Unauthorized)?;

    // Verify the admin token and get claims
    let claims = auth_service.key_manager.verify_jwt(auth_header)?;

    // Check if the token has admin entitlements
    if let Some(admin) = claims.entitlements.get("admin") {
        if admin.as_bool() != Some(true) {
            return Err(AuthError::Unauthorized);
        }
    } else {
        return Err(AuthError::Unauthorized);
    }

    let admin_user = auth_service.get_admin_user(claims.sub).await?;

    let response_data = AdminUserResponse {
        id: admin_user.id,
        username: admin_user.username,
        created_at: admin_user.created_at.into(),
        last_login_at: admin_user.last_login_at.map(|dt| dt.into()),
    };
    Ok(Json(ApiResponse::success(response_data)))
}

// Admin authentication middleware
pub async fn admin_auth(
    State(auth_service): State<Arc<AuthService>>,
    headers: HeaderMap,
    request: axum::extract::Request,
    next: axum::middleware::Next,
) -> Result<axum::response::Response, AuthError> {
    let auth_header = headers
        .get("authorization")
        .and_then(|v| v.to_str().ok())
        .and_then(|auth| auth.strip_prefix("Bearer "))
        .ok_or(AuthError::Unauthorized)?;

    // Use the new admin token verification
    if !auth_service.verify_admin_token(auth_header).await? {
        return Err(AuthError::Unauthorized);
    }

    Ok(next.run(request).await)
}

// Instance authentication middleware
pub async fn instance_auth(
    State(auth_service): State<Arc<AuthService>>,
    headers: HeaderMap,
    request: axum::extract::Request,
    next: axum::middleware::Next,
) -> Result<axum::response::Response, AuthError> {
    let api_key = headers
        .get("x-api-key")
        .and_then(|v| v.to_str().ok())
        .ok_or(AuthError::Unauthorized)?;

    // Extract instance ID from the path
    let path = request.uri().path();
    let instance_id = path.split('/').nth(2).ok_or(AuthError::Unauthorized)?;

    // Verify the API key belongs to this instance
    auth_service
        .verify_instance_api_key(instance_id, api_key)
        .await?;

    Ok(next.run(request).await)
}

// Get dashboard statistics
pub async fn get_dashboard_stats(
    State(auth_service): State<Arc<AuthService>>,
) -> Result<Json<ApiResponse<DashboardStatsResponse>>, AuthError> {
    let stats = auth_service.get_dashboard_stats().await?;
    Ok(Json(ApiResponse::success(stats)))
}

// Instance-level APIs (for software instances to query their own info)

// Get instance status (for the instance itself)
pub async fn get_instance_status(
    State(auth_service): State<Arc<AuthService>>,
    Path(instance_id): Path<String>,
) -> Result<Json<ApiResponse<InstanceResponse>>, AuthError> {
    let instance = auth_service.get_instance_by_id(instance_id).await?;
    let instance_response = InstanceResponse {
        id: instance.id,
        name: instance.name,
        license_expires_at: instance.license_expires_at.into(),
        entitlements: instance.entitlements,
        last_seen_ip: instance.last_seen_ip,
        last_seen_at: instance.last_seen_at.map(|dt| dt.into()),
        current_session_id: instance.current_session_id,
        created_at: instance.created_at.into(),
        updated_at: instance.updated_at.into(),
    };
    Ok(Json(ApiResponse::success(instance_response)))
}

// Get instance renewal history
pub async fn get_instance_renewal_history(
    State(auth_service): State<Arc<AuthService>>,
    Path(instance_id): Path<String>,
) -> Result<Json<ApiResponse<Vec<RenewalHistoryItem>>>, AuthError> {
    let history = auth_service
        .get_instance_renewal_history(instance_id)
        .await?;
    Ok(Json(ApiResponse::success(history)))
}

// Apply renewal code for instance (simplified version)
pub async fn apply_renewal_code_for_instance(
    State(auth_service): State<Arc<AuthService>>,
    Path(instance_id): Path<String>,
    Json(request): Json<ApplyRenewalCodeRequest>,
) -> Result<Json<ApiResponse<serde_json::Value>>, AuthError> {
    auth_service
        .apply_renewal_code(instance_id, request.renewal_code_id)
        .await?;
    Ok(Json(ApiResponse::success(serde_json::json!({}))))
}

// Get RSA public key for JWT verification (public endpoint)
pub async fn get_public_key(
    State(auth_service): State<Arc<AuthService>>,
) -> Result<Json<ApiResponse<PublicKeyResponse>>, AuthError> {
    let public_key_pem = auth_service.key_manager.get_rsa_public_key_pem()
        .map_err(|e| AuthError::Internal(anyhow::anyhow!("Failed to get public key: {}", e)))?;
    
    let response = PublicKeyResponse {
        public_key: public_key_pem,
        algorithm: "RS256".to_string(),
    };
    
    Ok(Json(ApiResponse::success(response)))
}
