use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde::Serialize;

#[derive(Debug, thiserror::Error)]
pub enum AuthError {
    #[error("Database error: {0}")]
    Database(#[from] prisma_client_rust::QueryError),
    
    #[error("JWT error: {0}")]
    Jwt(#[from] jsonwebtoken::errors::Error),
    
    #[error("Crypto error: {0}")]
    Crypto(String),
    
    #[error("Instance not found")]
    InstanceNotFound,
    
    #[error("License expired")]
    LicenseExpired,
    
    #[error("License in use by another instance")]
    LicenseInUse,
    
    #[error("Invalid session")]
    InvalidSession,
    
    #[error("Unauthorized")]
    Unauthorized,
    
    #[error("Bad request: {0}")]
    BadRequest(String),
    
    #[error("Internal server error: {0}")]
    Internal(#[from] anyhow::Error),
}

#[derive(Serialize)]
struct ErrorResponse {
    error: String,
}

impl IntoResponse for AuthError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            AuthError::InstanceNotFound => (StatusCode::FORBIDDEN, "INSTANCE_NOT_FOUND"),
            AuthError::LicenseExpired => (StatusCode::FORBIDDEN, "LICENSE_EXPIRED"),
            AuthError::LicenseInUse => (StatusCode::CONFLICT, "LICENSE_IN_USE"),
            AuthError::InvalidSession => (StatusCode::UNAUTHORIZED, "INVALID_SESSION"),
            AuthError::Unauthorized => (StatusCode::UNAUTHORIZED, "UNAUTHORIZED"),
            AuthError::BadRequest(_) => (StatusCode::BAD_REQUEST, "BAD_REQUEST"),
            _ => {
                log::error!("Internal server error: {}", self);
                (StatusCode::INTERNAL_SERVER_ERROR, "INTERNAL_SERVER_ERROR")
            }
        };

        let body = Json(ErrorResponse {
            error: error_message.to_string(),
        });

        (status, body).into_response()
    }
}