use anyhow::{anyhow, Result};
use jsonwebtoken::{encode, decode, Head<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, <PERSON>od<PERSON><PERSON><PERSON>, Valida<PERSON>};
use rsa::{RsaPrivate<PERSON>ey, RsaPublic<PERSON>ey, pkcs8::{EncodePrivate<PERSON><PERSON>, EncodePub<PERSON><PERSON>ey}};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct JwtClaims {
    pub sub: String,        // instance ID
    pub sid: String,        // session ID
    pub exp: u64,           // expiration timestamp
    pub iat: u64,           // issued at timestamp
    pub entitlements: serde_json::Value,
}

pub struct KeyManager {
    rsa_private_key: RsaPrivateKey,
    rsa_public_key: RsaPublicKey,
}

impl KeyManager {
    pub fn new() -> Result<Self> {
        // Generate RSA key pair for JWT signing
        let mut rng = rand::thread_rng();
        let rsa_private_key = RsaPrivateKey::new(&mut rng, 2048)
            .map_err(|e| anyhow!("Failed to generate RSA private key: {}", e))?;
        let rsa_public_key = RsaPublicKey::from(&rsa_private_key);

        log::info!("Generated new RSA key pair for JWT signing");

        Ok(Self {
            rsa_private_key,
            rsa_public_key,
        })
    }


    pub fn get_rsa_public_key_pem(&self) -> Result<String> {
        self.rsa_public_key
            .to_public_key_pem(rsa::pkcs8::LineEnding::LF)
            .map_err(|e| anyhow!("Failed to serialize RSA public key: {}", e))
    }

    pub fn get_rsa_private_key_pem(&self) -> Result<String> {
        Ok(self.rsa_private_key
            .to_pkcs8_pem(rsa::pkcs8::LineEnding::LF)
            .map_err(|e| anyhow!("Failed to serialize RSA private key: {}", e))?
            .to_string())
    }

    pub fn create_jwt(&self, claims: &JwtClaims) -> Result<String> {
        let header = Header::new(Algorithm::RS256);
        let private_key_pem = self.get_rsa_private_key_pem()?;
        let encoding_key = EncodingKey::from_rsa_pem(private_key_pem.as_bytes())
            .map_err(|e| anyhow!("Failed to create encoding key: {}", e))?;
        
        encode(&header, claims, &encoding_key)
            .map_err(|e| anyhow!("Failed to create JWT: {}", e))
    }

    pub fn verify_jwt(&self, token: &str) -> Result<JwtClaims> {
        let mut validation = Validation::new(Algorithm::RS256);
        validation.validate_exp = true;
        
        let public_key_pem = self.get_rsa_public_key_pem()?;
        let decoding_key = DecodingKey::from_rsa_pem(public_key_pem.as_bytes())
            .map_err(|e| anyhow!("Failed to create decoding key: {}", e))?;
        
        let token_data = decode::<JwtClaims>(token, &decoding_key, &validation)
            .map_err(|e| anyhow!("Failed to verify JWT: {}", e))?;
        
        Ok(token_data.claims)
    }
}