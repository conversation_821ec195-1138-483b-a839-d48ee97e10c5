use std::collections::{HashMap, HashSet};

use anyhow::anyhow;
use chrono::Utc;
use common::reset_traffic_task::{ArcResetTrafficContext, ResetTrafficTask};
use jsonwebtoken::{encode, Enco<PERSON><PERSON><PERSON>, <PERSON><PERSON>};
use log::{error, info, warn};
use prisma_client_rust::Direction;
use redis::AsyncCommands;
use warp::{reject::Rejection, reply::Reply};

use crate::{
    add_user::{add_user_proc, edit_user_proc},
    error::Error,
    message::{
        AddeUserInfo, BillingType, Claims, EditUserInfo, ExtendSubscriptionTimeRequest, LineInfo,
        LoginRequest, LoginResponse, PaginatedSubscriptionResponse, PaginationInfo,
        ResetUserTrafficRequest, RmvUserRequest, SubscriptionInfo, SubscriptionItem,
        SubscriptionSearchRequest,
    },
    prisma, AppState,
};

pub async fn handle_login(
    login: LoginRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Login attempt with token_id: {}", login.token);

    // Find subscription by token
    let subscription = match app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(login.token.clone()))
        .select(prisma::subscription::select!({
            id
            token_id
            activated
            valid_until
        }))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Login failed: Invalid token");
            return Err(warp::reject::custom(Error::Auth(
                "Invalid token".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error during login: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };

    // Check if subscription is activated
    if !subscription.activated {
        warn!(
            "Login failed: Subscription not activated for token_id: {}",
            subscription.token_id
        );
        return Err(warp::reject::custom(Error::Auth(
            "Subscription not activated".to_string(),
        )));
    }

    // Create JWT token
    let claims = Claims {
        token_id: subscription.token_id.clone(),
        exp: (Utc::now() + chrono::Duration::days(7)).timestamp() as usize,
    };

    let jwt = match encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(std::env::var("JWT_SECRET").unwrap_or_default().as_bytes()),
    ) {
        Ok(token) => token,
        Err(e) => {
            error!("Failed to create JWT token: {}", e);
            return Err(warp::reject::custom(Error::Auth(
                "Failed to create token".to_string(),
            )));
        }
    };

    info!("Login successful for token_id: {}", subscription.token_id);
    Ok(warp::reply::json(&LoginResponse { jwt }))
}

pub async fn handle_subscription_info(
    token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Fetching subscription info for token_id: {}", token_id);

    let subscription = match app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!({
            id
            token_id
            valid_until
            last_reset
            reset_days
            traffic
            bandwidth
            lines
            is_admin
            allow_forward_endpoint
            package_id
        }))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };
    let mut in_package_lines_ids = HashSet::new();
    let mut package_line_traffic_scales = HashMap::new();
    if let Some(package_id) = subscription.package_id {
        let package = app_state
            .db
            .package()
            .find_unique(prisma::package::id::equals(package_id))
            .include(prisma::package::include!({ package_lines }))
            .exec()
            .await
            .map_err(Error::Database)?
            .ok_or(Error::NotFound("Package not found".to_string()))?;

        // Collect package line IDs and their traffic scales
        for package_line in package.package_lines.into_iter() {
            in_package_lines_ids.insert(package_line.line_id);
            if let Some(traffic_scale) = package_line.traffic_scale {
                package_line_traffic_scales.insert(package_line.line_id, traffic_scale as f32);
            }
        }
    }
    let mut line_info: Vec<LineInfo> = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![prisma::outbound_endpoint::id::in_vec(
            subscription.lines,
        )])
        .order_by(prisma::outbound_endpoint::id::order(Direction::Desc))
        .select(prisma::outbound_endpoint::select!(
         {
                id
                display_name
                ingress_ipv_4
                port_start
                port_end
                allow_forward
                traffic_scale
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?
        .into_iter()
        .map(|x| {
            // Priority: package traffic scale > outbound endpoint traffic scale > 1.0 default
            let traffic_scale = if let Some(package_scale) = package_line_traffic_scales.get(&x.id)
            {
                Some(*package_scale)
            } else if let Some(endpoint_scale) = x.traffic_scale {
                Some(endpoint_scale as f32)
            } else {
                Some(1.0)
            };

            LineInfo {
                id: x.id,
                display_name: x.display_name,
                ip_addr: x.ingress_ipv_4,
                is_online: None,
                port_start: x.port_start,
                port_end: x.port_end,
                allow_forward: x.allow_forward,
                is_in_package: Some(in_package_lines_ids.get(&x.id).is_some()),
                traffic_scale,
            }
        })
        .collect();
    let mut conn = app_state.redis_pool.get().await.map_err(Error::Redis)?;
    for l in line_info.iter_mut() {
        let online: u128 = get_number!(conn, format!("server:status:{}", l.id), 0);
        l.is_online = Some(online == 1);
    }
    let traffic_used = get_number!(conn, format!("sub:used:{}", subscription.id), 0);
    let info = SubscriptionInfo {
        id: subscription.id,
        valid_until: subscription.valid_until.into(),
        next_reset: subscription.last_reset.map(|last_reset| {
            (last_reset + chrono::Duration::days(subscription.reset_days.unwrap_or(30) as i64))
                .to_utc()
        }),
        traffic_total: subscription.traffic,
        traffic_used,
        bandwidth: subscription.bandwidth,
        lines: line_info,
        is_admin: subscription.is_admin,
        allow_forward_endpoint: subscription.allow_forward_endpoint,
        is_expired: subscription.valid_until < Utc::now(),
    };

    info!(
        "Successfully retrieved subscription info for token_id: {}",
        token_id
    );
    Ok(warp::reply::json(&info))
}

pub async fn handle_get_subscription_list_with_search(
    search_request: Option<SubscriptionSearchRequest>,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    use chrono::NaiveDate;
    use prisma::subscription::WhereParam;
    use prisma_client_rust::Direction;

    // Extract pagination parameters with defaults
    let page = search_request.as_ref().and_then(|s| s.page).unwrap_or(1);
    let page_size = search_request
        .as_ref()
        .and_then(|s| s.page_size)
        .unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Calculate skip and take for pagination
    let skip = ((page - 1) * page_size) as i64;
    let take = page_size as i64;

    // Extract search parameters
    let id_search = search_request.as_ref().and_then(|s| s.id);
    let token_id_search = search_request.as_ref().and_then(|s| s.token_id.as_ref());
    let email_search = search_request.as_ref().and_then(|s| s.email.as_ref());
    let valid_until_start = search_request
        .as_ref()
        .and_then(|s| s.valid_until_start.as_ref());
    let valid_until_end = search_request
        .as_ref()
        .and_then(|s| s.valid_until_end.as_ref());
    let next_reset_start = search_request
        .as_ref()
        .and_then(|s| s.next_reset_start.as_ref());
    let next_reset_end = search_request
        .as_ref()
        .and_then(|s| s.next_reset_end.as_ref());
    let lines_search = search_request.as_ref().and_then(|s| s.lines.as_ref());

    info!(
        "Get subscription list with search - page: {}, page_size: {}, id: {:?}, token_id: {:?}, email: {:?}, valid_until: {:?}-{:?}, next_reset: {:?}-{:?}, lines: {:?}",
        page, page_size, id_search, token_id_search, email_search, valid_until_start, valid_until_end, next_reset_start, next_reset_end, lines_search
    );

    // Build where conditions
    let mut where_conditions: Vec<WhereParam> = vec![];

    // Exact ID search (highest priority)
    if let Some(id) = id_search {
        where_conditions.push(prisma::subscription::id::equals(id));
    }

    // Exact token ID search
    if let Some(token_id) = token_id_search {
        where_conditions.push(prisma::subscription::token_id::equals(token_id.clone()));
    }

    // Fuzzy email search (case-insensitive)
    if let Some(email) = email_search {
        // Convert to lowercase for case-insensitive search
        let email_lower = email.to_lowercase();
        where_conditions.push(prisma::subscription::owner_address::contains(email_lower));
    }

    // Valid until date range filtering
    if let Some(start_date_str) = valid_until_start {
        if let Ok(start_date) = NaiveDate::parse_from_str(start_date_str, "%Y-%m-%d") {
            let start_datetime = start_date.and_hms_opt(0, 0, 0).unwrap().and_utc();
            where_conditions.push(prisma::subscription::valid_until::gte(
                start_datetime.into(),
            ));
        }
    }
    if let Some(end_date_str) = valid_until_end {
        if let Ok(end_date) = NaiveDate::parse_from_str(end_date_str, "%Y-%m-%d") {
            let end_datetime = end_date.and_hms_opt(23, 59, 59).unwrap().and_utc();
            where_conditions.push(prisma::subscription::valid_until::lte(end_datetime.into()));
        }
    }

    // Lines filtering (ANY logic - subscription contains any of the selected lines)
    if let Some(lines) = lines_search {
        if !lines.is_empty() {
            // Use array overlap to check if subscription.lines contains any of the search lines
            where_conditions.push(prisma::subscription::lines::has_some(lines.clone()));
        }
    }

    // Note: Next reset date filtering is handled post-query since it's a calculated field
    // We'll filter the results after fetching from the database

    // Get total count with search filters
    // Note: For next reset date filtering, we need to adjust the count since it's post-processed
    let total_count = if next_reset_start.is_some() || next_reset_end.is_some() {
        // If we have next reset filters, we need to count after processing
        // For now, we'll use the database count and adjust later if needed
        // This is a trade-off between accuracy and performance
        app_state
            .db
            .subscription()
            .count(where_conditions.clone())
            .exec()
            .await
            .map_err(Error::Database)? as u32
    } else {
        // No next reset filters, use direct database count
        app_state
            .db
            .subscription()
            .count(where_conditions.clone())
            .exec()
            .await
            .map_err(Error::Database)? as u32
    };

    // Determine ordering - prioritize exact ID matches
    let order_by = if id_search.is_some() {
        // If searching by ID, order by ID ascending to put exact matches first
        prisma::subscription::id::order(Direction::Asc)
    } else {
        // Default ordering by ID descending (newest first)
        prisma::subscription::id::order(Direction::Desc)
    };

    // Query subscriptions with search filters and pagination
    let subscription_list = app_state
        .db
        .subscription()
        .find_many(where_conditions)
        .order_by(order_by)
        .skip(skip)
        .take(take)
        .select(prisma::subscription::select!({
           id
           token_id
           lines
           valid_until
           last_reset
           reset_days
           traffic
           owner_address
           activated
           allow_forward_endpoint
           max_port_num_per_server
           billing_type
           base_price
           recurring_price
           allow_ip_num
           allow_conn_num
           package_id
        }))
        .exec()
        .await
        .map_err(Error::Database)?;

    let mut subscription_items = vec![];
    let mut conn = app_state.redis_pool.get().await.map_err(Error::Redis)?;

    // Parse next reset date filters for post-processing
    let next_reset_start_date = next_reset_start.and_then(|s| {
        NaiveDate::parse_from_str(s, "%Y-%m-%d")
            .ok()
            .and_then(|d| d.and_hms_opt(0, 0, 0))
            .map(|dt| dt.and_utc())
    });
    let next_reset_end_date = next_reset_end.and_then(|s| {
        NaiveDate::parse_from_str(s, "%Y-%m-%d")
            .ok()
            .and_then(|d| d.and_hms_opt(23, 59, 59))
            .map(|dt| dt.and_utc())
    });

    for subscription in subscription_list {
        info!(
            "Fetching subscription info for token_id: {}",
            subscription.token_id
        );
        let mut in_package_lines_ids = HashSet::new();
        let mut package_line_traffic_scales = HashMap::new();
        if let Some(package_id) = subscription.package_id {
            let package = app_state
                .db
                .package()
                .find_unique(prisma::package::id::equals(package_id))
                .include(prisma::package::include!({ package_lines }))
                .exec()
                .await
                .map_err(Error::Database)?
                .ok_or(Error::NotFound("Package not found".to_string()))?;

            // Collect package line IDs and their traffic scales
            for package_line in package.package_lines.into_iter() {
                in_package_lines_ids.insert(package_line.line_id);
                if let Some(traffic_scale) = package_line.traffic_scale {
                    package_line_traffic_scales.insert(package_line.line_id, traffic_scale as f32);
                }
            }
        }

        // Get line information for this subscription
        let mut line_info: Vec<LineInfo> = app_state
            .db
            .outbound_endpoint()
            .find_many(vec![prisma::outbound_endpoint::id::in_vec(
                subscription.lines.clone(),
            )])
            .order_by(prisma::outbound_endpoint::id::order(Direction::Desc))
            .select(prisma::outbound_endpoint::select!(
             {
                    id
                    display_name
                    ingress_ipv_4
                    port_start
                    port_end
                    allow_forward
                    allow_ip_num
                    allow_conn_num
                    traffic_scale
                }
            ))
            .exec()
            .await
            .map_err(Error::Database)?
            .into_iter()
            .map(|x| {
                // Priority: package traffic scale > outbound endpoint traffic scale > 1.0 default
                let traffic_scale =
                    if let Some(package_scale) = package_line_traffic_scales.get(&x.id) {
                        Some(*package_scale)
                    } else if let Some(endpoint_scale) = x.traffic_scale {
                        Some(endpoint_scale as f32)
                    } else {
                        Some(1.0)
                    };

                LineInfo {
                    id: x.id,
                    display_name: x.display_name,
                    ip_addr: x.ingress_ipv_4,
                    is_online: None,
                    port_start: x.port_start,
                    port_end: x.port_end,
                    allow_forward: x.allow_forward,
                    is_in_package: Some(in_package_lines_ids.get(&x.id).is_some()),
                    traffic_scale,
                }
            })
            .collect();

        // update line online status
        for line in line_info.iter_mut() {
            line.is_online = Some(get_number!(conn, format!("server:status:{}", line.id), 0) == 1);
        }

        // Get traffic usage from Redis
        let traffic_used = get_number!(conn, format!("sub:used:{}", subscription.id), 0);

        // Calculate next reset date
        let next_reset = subscription.last_reset.map(|last_reset| {
            (last_reset + chrono::Duration::days(subscription.reset_days.unwrap_or(30) as i64))
                .to_utc()
        });

        // Apply next reset date filtering (post-query filtering)
        let passes_next_reset_filter = {
            if let Some(start_date) = next_reset_start_date {
                if let Some(reset_date) = next_reset {
                    if reset_date < start_date {
                        false
                    } else {
                        true
                    }
                } else {
                    false // No next reset date, doesn't match filter
                }
            } else {
                true // No start filter
            }
        } && {
            if let Some(end_date) = next_reset_end_date {
                if let Some(reset_date) = next_reset {
                    if reset_date > end_date {
                        false
                    } else {
                        true
                    }
                } else {
                    false // No next reset date, doesn't match filter
                }
            } else {
                true // No end filter
            }
        };

        // Only include subscription if it passes all filters
        if passes_next_reset_filter {
            // Construct BillingType from database fields
            let bill_type = if subscription.billing_type == 0 {
                // Cycle billing
                BillingType::Cycle {
                    days: subscription.reset_days.unwrap_or(30) as usize,
                    price: subscription.base_price as usize,
                }
            } else {
                // OneTime billing
                BillingType::OneTime {
                    price: subscription.base_price as usize,
                    days: subscription.reset_days.unwrap_or(365) as usize,
                }
            };

            // Calculate total_days from reset_days or use a default
            let total_days = subscription.reset_days.unwrap_or(30) as u32;

            let info = SubscriptionItem {
                valid_until: subscription.valid_until.into(),
                next_reset,
                traffic_total: subscription.traffic * 1024 * 1024 * 1024,
                traffic_used,
                lines: line_info,
                id: subscription.id,
                token_id: subscription.token_id,
                email_address: subscription.owner_address,
                activated: subscription.activated,
                allow_forward_endpoint: subscription.allow_forward_endpoint,
                max_ports_per_server: subscription.max_port_num_per_server,
                bill_type,
                total_days,
                allow_ip_num: subscription.allow_ip_num,
                allow_conn_num: subscription.allow_conn_num,
                package_id: subscription.package_id,
            };
            subscription_items.push(info);
        }
    }

    // Calculate pagination info
    let total_pages = (total_count + page_size - 1) / page_size;

    let pagination_info = PaginationInfo {
        current_page: page,
        page_size,
        total_items: total_count,
        total_pages,
    };

    let response = PaginatedSubscriptionResponse {
        subscriptions: subscription_items,
        pagination: pagination_info,
    };

    Ok(warp::reply::json(&response))
}

pub async fn handle_reset_user_traffic(
    reset_user_traffic_request: ResetUserTrafficRequest,
    reset_traffic_ctx: ArcResetTrafficContext,
) -> Result<impl Reply, Rejection> {
    tokio::spawn(async move {
        let _ = reset_traffic_ctx
            .traffic_retry_queue
            .send(Box::new(ResetTrafficTask::new(
                reset_traffic_ctx.clone(),
                reset_user_traffic_request.user_id,
            )))
            .await;
    });
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_add_user(
    app_state: AppState,
    request: AddeUserInfo,
) -> Result<impl Reply, Rejection> {
    log::info!("Add user: {:?}", request);
    add_user_proc(vec![request], &app_state.db)
        .await
        .map_err(|e| {
            log::error!("Failed to add user: {}", e);
            warp::reject::custom(Error::InternalError(e))
        })?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_edit_user(
    app_state: AppState,
    request: EditUserInfo,
) -> Result<impl Reply, Rejection> {
    log::info!("Edit user: {:?}", request);
    edit_user_proc(request, &app_state.db).await.map_err(|e| {
        log::error!("Failed to edit user: {}", e);
        warp::reject::custom(Error::InternalError(e))
    })?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_rmv_user(
    app_state: AppState,
    rmv_user_request: RmvUserRequest,
) -> Result<impl Reply, Rejection> {
    let user_id = rmv_user_request.user_id;
    // remove all ports
    app_state
        .db
        .port()
        .delete_many(vec![prisma::port::subscription_id::equals(Some(user_id))])
        .exec()
        .await
        .map_err(Error::Database)?;
    // remove all forward endpoints
    app_state
        .db
        .forward_endpoint()
        .delete_many(vec![prisma::forward_endpoint::subscription_id::equals(
            Some(user_id),
        )])
        .exec()
        .await
        .map_err(Error::Database)?;

    // remove subscription
    app_state
        .db
        .subscription()
        .delete(prisma::subscription::id::equals(user_id))
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_extend_subscription_time(
    app_state: AppState,
    extend_subscription_time_request: ExtendSubscriptionTimeRequest,
) -> Result<impl Reply, Rejection> {
    let user_id = extend_subscription_time_request.user_id;
    let sub = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::id::equals(user_id))
        .select(prisma::subscription::select!({
            reset_days
            valid_until
        }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;
    let days = extend_subscription_time_request
        .days
        .unwrap_or(
            sub.reset_days
                .ok_or(warp::reject::custom(Error::InternalError(anyhow!(
                    "Reset days not set"
                ))))?,
        );
    let begin = if sub.valid_until < Utc::now() {
        Utc::now()
    } else {
        sub.valid_until.clone().into()
    };
    let new_valid_until = begin + chrono::Duration::days(days as i64);
    app_state
        .db
        .subscription()
        .update(
            prisma::subscription::id::equals(user_id),
            vec![prisma::subscription::valid_until::set(
                new_valid_until.into(),
            )],
        )
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}
