use base64::Engine;
use log::info;
use warp::{reject::Rejection, reply::Reply};

use crate::{
    add_machine::{add_machine, remove_line},
    error::Error,
    handlers::{
        calculate_server_match_score, generate_tot_config_or_forward_config,
        get_balance_strategy_from_config, matches_regex_or_exact, SubscriptionCommonInfo,
    },
    message::{
        AddServerRequest, FilterType, ModifyServerRequest, PaginatedServerResponse, PaginationInfo,
        PaginationRequest, RmvServerRequest, ServerInfo, ServerListResponse, ServerSearchRequest,
    },
    prisma,
    tot::get_tot_config_from_str,
    AppState,
};
use prisma_client_rust::Direction;

use redis::AsyncCommands;

/// Collect all available server versions from the database
async fn get_all_server_versions(app_state: &AppState) -> Result<Vec<String>, Error> {
    let all_servers_for_versions = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![])
        .select(prisma::outbound_endpoint::select!({
            version
        }))
        .exec()
        .await
        .map_err(Error::Database)?;
    
    let mut all_versions = std::collections::HashSet::new();
    for server in &all_servers_for_versions {
        let version = server.version.as_deref().unwrap_or("Unknown");
        all_versions.insert(version.to_string());
    }
    
    let mut available_versions: Vec<String> = all_versions.into_iter().collect();
    available_versions.sort();
    Ok(available_versions)
}

pub async fn handle_get_server_list_with_search(
    search_request: Option<ServerSearchRequest>,
    app_state: AppState,
) -> Result<warp::reply::Json, Rejection> {
    // Extract pagination and search parameters with defaults
    let page = search_request.as_ref().and_then(|s| s.page).unwrap_or(1);
    let page_size = search_request
        .as_ref()
        .and_then(|s| s.page_size)
        .unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Extract search parameters
    let id_search = search_request.as_ref().and_then(|s| s.id);
    let name_search = search_request.as_ref().and_then(|s| s.name.as_ref());
    let ip_addr_search = search_request.as_ref().and_then(|s| s.ip_addr.as_ref());
    let version_filter = search_request.as_ref().and_then(|s| s.version.as_ref());
    let status_filter = search_request.as_ref().and_then(|s| s.status.as_ref());

    info!(
        "Get servers with search, page: {}, page_size: {}, id: {:?}, name: {:?}, ip_addr: {:?}, version: {:?}, status: {:?}",
        page, page_size, id_search, name_search, ip_addr_search, version_filter, status_filter
    );

    // For search functionality, we need to get all servers first and then filter
    // This is because regex filtering and status checking happens in application code
    let server_list = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![])
        .order_by(prisma::outbound_endpoint::id::order(Direction::Desc))
        .select(prisma::outbound_endpoint::select!({
            id
            display_name
            ingress_ipv_4
            port_start
            port_end
            traffic_scale
            allow_forward
            allow_latency_test
            use_forward_as_tun
            interface_name
            pubkey
            version
            proxy_config
            related_forward_endpoint_ids
            related_tot_server_ids
            allow_ip_v_6
            allow_ip_num
            allow_conn_num
            protocol_filters
        }))
        .exec()
        .await
        .map_err(Error::Database)?;

    // If ID search is specified, filter for exact ID match first (takes priority)
    let server_list = if let Some(search_id) = id_search {
        server_list
            .into_iter()
            .filter(|server| server.id == search_id)
            .collect()
    } else {
        server_list
    };

    let mut server_list_rst = vec![];
    let mut conn = app_state.redis_pool.get().await.map_err(Error::Redis)?;

    for server in server_list {
        let is_online = get_number!(conn, format!("server:status:{}", server.id), 0) == 1;
        let used_ports = app_state
            .db
            .port()
            .find_many(vec![prisma::port::outbound_endpoint_id::equals(Some(
                server.id,
            ))])
            .exec()
            .await
            .map_err(Error::Database)?;
        let mut used_ports_rst = vec![];
        for port in used_ports {
            used_ports_rst.push(port.port_v_4);
        }
        let protocol_filters = server
            .protocol_filters
            .into_iter()
            .filter_map(|v| FilterType::from_str(&v))
            .collect::<Vec<_>>();
        let tot_config = server.proxy_config.as_ref().and_then(|config| {
            base64::engine::general_purpose::STANDARD
                .decode(config)
                .ok()
                .and_then(|decoded| String::from_utf8(decoded).ok())
                .and_then(|config_str| get_tot_config_from_str(&config_str))
        });
        let server_info = ServerInfo {
            id: server.id,
            display_name: if server.display_name.is_empty() {
                format!("Server {}", server.id)
            } else {
                server.display_name.clone()
            },
            ip_addr: server.ingress_ipv_4.clone(),
            is_online: Some(is_online),
            port_start: server.port_start,
            port_end: server.port_end,
            used_ports: Some(used_ports_rst),
            interface_name: server.interface_name.clone(),
            server_pubkey: server.pubkey,
            version: server.version.clone(),
            balance_strategy: server
                .proxy_config
                .as_ref()
                .and_then(|v| get_balance_strategy_from_config(&v)),
            forward_endpoints: Some(server.related_forward_endpoint_ids),
            traffic_scale: server.traffic_scale.map(|v| v as f32),
            allow_forward: server.allow_forward,
            allow_latency_test: server.allow_latency_test,
            allow_ipv6: Some(server.allow_ip_v_6),
            use_forward_as_tun: server.use_forward_as_tun,
            allow_ip_num: server.allow_ip_num,
            allow_conn_num: server.allow_conn_num,
            protocol_filters: if protocol_filters.is_empty() {
                None
            } else {
                Some(protocol_filters)
            },
            tot_server_list: if server.related_tot_server_ids.is_empty() {
                None
            } else {
                Some(server.related_tot_server_ids)
            },
            tot_server_select_mode: tot_config
                .as_ref()
                .map(|config| config.tot_server_select_mode.into()),
            tot_server_test_method: tot_config
                .as_ref()
                .map(|config| config.tot_server_test_method.into()),
        };

        // Apply search filters (skip if ID search is used since exact ID match takes priority)
        if id_search.is_none() {
            // Check name filter (fuzzy/regex)
            if let Some(name_pattern) = name_search {
                if !matches_regex_or_exact(&server_info.display_name, name_pattern) {
                    continue;
                }
            }

            // Check IP address filter (fuzzy/regex)
            if let Some(ip_pattern) = ip_addr_search {
                if !matches_regex_or_exact(&server_info.ip_addr, ip_pattern) {
                    continue;
                }
            }

            // Check version filter (multi-select with ANY logic)
            if let Some(versions) = version_filter {
                if !versions.is_empty() {
                    let server_version = server_info.version.as_deref().unwrap_or("Unknown");
                    if !versions.iter().any(|v| v == server_version) {
                        continue;
                    }
                }
            }

            // Check status filter (multi-select with ANY logic)
            if let Some(statuses) = status_filter {
                if !statuses.is_empty() {
                    let server_status = if server_info.is_online.unwrap_or(false) {
                        "Online"
                    } else {
                        "Offline"
                    };
                    if !statuses.iter().any(|s| s == server_status) {
                        continue;
                    }
                }
            }
        }

        server_list_rst.push(server_info);
    }

    // Sort results to prioritize exact matches (skip if ID search is used)
    if id_search.is_none() && (name_search.is_some() || ip_addr_search.is_some()) {
        server_list_rst.sort_by(|a, b| {
            let a_score = calculate_server_match_score(a, name_search, ip_addr_search);
            let b_score = calculate_server_match_score(b, name_search, ip_addr_search);
            b_score.cmp(&a_score) // Higher scores first
        });
    }

    // Collect all available versions from ALL servers (not just filtered) for filter dropdown
    // This ensures users can see all version options regardless of current filters
    let available_versions = get_all_server_versions(&app_state).await?;

    // Apply pagination to filtered results
    let total_filtered = server_list_rst.len() as u32;
    let total_pages = if total_filtered == 0 {
        1
    } else {
        (total_filtered + page_size - 1) / page_size
    };

    let skip = ((page - 1) * page_size) as usize;
    let take = page_size as usize;
    let paginated_servers: Vec<ServerInfo> =
        server_list_rst.into_iter().skip(skip).take(take).collect();

    let pagination_info = PaginationInfo {
        current_page: page,
        page_size,
        total_items: total_filtered,
        total_pages,
    };

    let response = PaginatedServerResponse {
        servers: paginated_servers,
        pagination: pagination_info,
        available_versions,
    };

    info!(
        "Successfully retrieved {} filtered servers (page {}/{}, total: {})",
        response.servers.len(),
        page,
        total_pages,
        total_filtered
    );
    Ok(warp::reply::json(&response))
}

pub async fn handle_get_all_server_list(
    app_state: AppState,
) -> Result<warp::reply::Json, Rejection> {
    // This endpoint returns all servers without pagination for admin use
    // Used specifically for populating search dropdowns with complete data

    let server_list = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![])
        .order_by(prisma::outbound_endpoint::id::order(Direction::Desc))
        .select(prisma::outbound_endpoint::select!({
            id
            display_name
            traffic_scale
        }))
        .exec()
        .await
        .map_err(Error::Database)?;

    // Transform to simplified format for dropdown use
    let server_list_rst: Vec<_> = server_list
        .into_iter()
        .map(|server| {
            serde_json::json!({
                "id": server.id,
                "display_name": if server.display_name.is_empty() {
                    format!("Server {}", server.id)
                } else {
                    server.display_name
                },
                "traffic_scale": server.traffic_scale.unwrap_or(1.0)
            })
        })
        .collect();

    Ok(warp::reply::json(&serde_json::json!({
        "servers": server_list_rst
    })))
}

pub async fn handle_get_server_list(
    pagination: Option<PaginationRequest>,
    app_state: AppState,
) -> Result<warp::reply::Json, Rejection> {
    // Extract pagination parameters with defaults
    let page = pagination.as_ref().and_then(|p| p.page).unwrap_or(1);
    let page_size = pagination.as_ref().and_then(|p| p.page_size).unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Calculate skip and take for pagination
    let skip = ((page - 1) * page_size) as i64;
    let take = page_size as i64;

    // Get total count of servers
    let total_count = app_state
        .db
        .outbound_endpoint()
        .count(vec![])
        .exec()
        .await
        .map_err(Error::Database)? as u32;

    let server_list = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![])
        .order_by(prisma::outbound_endpoint::id::order(Direction::Desc))
        .skip(skip)
        .take(take)
        .select(prisma::outbound_endpoint::select!({
            id
            display_name
            ingress_ipv_4
            port_start
            port_end
            traffic_scale
            allow_forward
            allow_latency_test
            use_forward_as_tun
            interface_name
            pubkey
            version
            proxy_config
            related_forward_endpoint_ids
            related_tot_server_ids
            allow_ip_v_6
            allow_ip_num
            allow_conn_num
            protocol_filters
        }))
        .exec()
        .await
        .map_err(Error::Database)?;
    let mut server_list_rst = vec![];
    let mut conn = app_state.redis_pool.get().await.map_err(Error::Redis)?;
    for server in server_list {
        let is_online = get_number!(conn, format!("server:status:{}", server.id), 0) == 1;
        let used_ports = app_state
            .db
            .port()
            .find_many(vec![prisma::port::outbound_endpoint_id::equals(Some(
                server.id,
            ))])
            .exec()
            .await
            .map_err(Error::Database)?;
        let mut used_ports_rst = vec![];
        for port in used_ports {
            used_ports_rst.push(port.port_v_4);
        }
        let protocol_filters = server
            .protocol_filters
            .into_iter()
            .filter_map(|v| FilterType::from_str(&v))
            .collect::<Vec<_>>();
        let tot_config = server.proxy_config.as_ref().and_then(|config| {
            base64::engine::general_purpose::STANDARD
                .decode(config)
                .ok()
                .and_then(|decoded| String::from_utf8(decoded).ok())
                .and_then(|config_str| get_tot_config_from_str(&config_str))
        });
        server_list_rst.push(ServerInfo {
            id: server.id,
            display_name: if server.display_name.is_empty() {
                format!("Server {}", server.id)
            } else {
                server.display_name
            },
            ip_addr: server.ingress_ipv_4,
            is_online: Some(is_online),
            port_start: server.port_start,
            port_end: server.port_end,
            used_ports: Some(used_ports_rst),
            interface_name: server.interface_name,
            server_pubkey: server.pubkey,
            version: server.version,
            balance_strategy: server
                .proxy_config
                .as_ref()
                .and_then(|v| get_balance_strategy_from_config(&v)),
            forward_endpoints: Some(server.related_forward_endpoint_ids),
            traffic_scale: server.traffic_scale.map(|v| v as f32),
            allow_forward: server.allow_forward,
            allow_latency_test: server.allow_latency_test,
            allow_ipv6: Some(server.allow_ip_v_6),
            use_forward_as_tun: server.use_forward_as_tun,
            allow_ip_num: server.allow_ip_num,
            allow_conn_num: server.allow_conn_num,
            protocol_filters: if protocol_filters.is_empty() {
                None
            } else {
                Some(protocol_filters)
            },
            tot_server_list: if server.related_tot_server_ids.is_empty() {
                None
            } else {
                Some(server.related_tot_server_ids)
            },
            tot_server_select_mode: tot_config
                .as_ref()
                .map(|config| config.tot_server_select_mode.into()),
            tot_server_test_method: tot_config
                .as_ref()
                .map(|config| config.tot_server_test_method.into()),
        });
    }

    // Check if pagination was requested
    if pagination.is_some() {
        // Collect all available versions from ALL servers (not just current page) for filter dropdown
        let available_versions = get_all_server_versions(&app_state).await?;

        // Calculate pagination info
        let total_pages = if total_count == 0 {
            1
        } else {
            (total_count + page_size - 1) / page_size
        };

        let pagination_info = PaginationInfo {
            current_page: page,
            page_size,
            total_items: total_count,
            total_pages,
        };

        let response = PaginatedServerResponse {
            servers: server_list_rst,
            pagination: pagination_info,
            available_versions,
        };
        Ok(warp::reply::json(&response))
    } else {
        // Return old format for backward compatibility
        Ok(warp::reply::json(&ServerListResponse {
            servers: server_list_rst,
        }))
    }
}

pub async fn handle_add_server(
    app_state: AppState,
    token_id: String,
    add_server_request: AddServerRequest,
) -> Result<impl Reply, Rejection> {
    log::info!("user: {}, Add server: {:?}", token_id, add_server_request);
    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(SubscriptionCommonInfo::select())
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;

    let forward_config = generate_tot_config_or_forward_config(
        &token_id,
        add_server_request.forward_endpoints,
        add_server_request
            .balance_strategy
            .as_ref()
            .map(|x| *x as u32),
        add_server_request.tot_server_list,
        add_server_request.tot_server_select_mode,
        add_server_request.tot_server_test_method,
        &subscription,
        &app_state.db,
    )
    .await
    .map_err(|e| {
        log::error!("Failed to generate port forward data: {}", e);
        warp::reject::custom(Error::InternalError(e))
    })?;
    add_machine(
        app_state.db.clone(),
        &add_server_request.display_name,
        &add_server_request.ip_addr,
        add_server_request.port_start.unwrap_or(30000),
        add_server_request.port_end.unwrap_or(31000),
        add_server_request.traffic_scale.unwrap_or(1.0),
        &token_id,
        &add_server_request.interface_name,
        add_server_request.allow_forward.unwrap_or(false),
        add_server_request.allow_latency_test.unwrap_or(true),
        forward_config,
        add_server_request.use_forward_as_tun.unwrap_or(false),
        add_server_request.allow_ip_num,
        add_server_request.allow_conn_num,
        add_server_request.protocol_filters,
    )
    .await
    .map_err(|e| {
        log::error!("Failed to add server: {}", e);
        warp::reject::custom(Error::InternalError(e))
    })?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_rmv_server(
    app_state: AppState,
    rmv_server_request: RmvServerRequest,
) -> Result<impl Reply, Rejection> {
    remove_line(app_state.db.clone(), rmv_server_request.server_id)
        .await
        .map_err(|e| {
            log::error!("Failed to remove server: {}", e);
            warp::reject::custom(Error::InternalError(e))
        })?;
    Ok(warp::reply::html("Ok"))
}
pub async fn handle_modify_server(
    token_id: String,
    app_state: AppState,
    modify_server_request: ModifyServerRequest,
) -> Result<impl Reply, Rejection> {
    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(SubscriptionCommonInfo::select())
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;

    let forward_config = generate_tot_config_or_forward_config(
        &token_id,
        modify_server_request.forward_endpoints,
        modify_server_request
            .balance_strategy
            .as_ref()
            .map(|x| *x as u32),
        modify_server_request.tot_server_list,
        modify_server_request.tot_server_select_mode,
        modify_server_request.tot_server_test_method,
        &subscription,
        &app_state.db,
    )
    .await
    .map_err(|e| {
        log::error!("Failed to generate port forward data: {}", e);
        warp::reject::custom(Error::InternalError(e))
    })?;

    let mut set_args = vec![
        prisma::outbound_endpoint::display_name::set(modify_server_request.display_name),
        prisma::outbound_endpoint::ingress_ipv_4::set(modify_server_request.ip_addr),
    ];
    if let Some(interface_name) = modify_server_request.interface_name {
        set_args.push(prisma::outbound_endpoint::interface_name::set(Some(
            interface_name,
        )));
    }
    if let Some(port_start) = modify_server_request.port_start {
        set_args.push(prisma::outbound_endpoint::port_start::set(Some(port_start)));
    }
    if let Some(port_end) = modify_server_request.port_end {
        set_args.push(prisma::outbound_endpoint::port_end::set(Some(port_end)));
    }
    if let Some(traffic_scale) = modify_server_request.traffic_scale {
        set_args.push(prisma::outbound_endpoint::traffic_scale::set(Some(
            traffic_scale as f64,
        )));
    }
    if let Some(allow_forward) = modify_server_request.allow_forward {
        set_args.push(prisma::outbound_endpoint::allow_forward::set(Some(
            allow_forward,
        )));
    }
    if let Some(allow_ipv6) = modify_server_request.allow_ipv6 {
        set_args.push(prisma::outbound_endpoint::allow_ip_v_6::set(allow_ipv6));
    }

    if let Some(allow_latency_test) = modify_server_request.allow_latency_test {
        set_args.push(prisma::outbound_endpoint::allow_latency_test::set(Some(
            allow_latency_test,
        )));
    }
    if let Some(use_forward_as_tun) = modify_server_request.use_forward_as_tun {
        set_args.push(prisma::outbound_endpoint::use_forward_as_tun::set(Some(
            use_forward_as_tun,
        )));
    }
    if let Some(allow_ip_num) = modify_server_request.allow_ip_num {
        set_args.push(prisma::outbound_endpoint::allow_ip_num::set(Some(
            allow_ip_num,
        )));
    }
    if let Some(allow_conn_num) = modify_server_request.allow_conn_num {
        set_args.push(prisma::outbound_endpoint::allow_conn_num::set(Some(
            allow_conn_num,
        )));
    }
    if let Some(protocol_filters) = modify_server_request.protocol_filters {
        let protocol_filters_strings = protocol_filters
            .iter()
            .map(|f| f.as_str().to_string())
            .collect::<Vec<String>>();
        set_args.push(prisma::outbound_endpoint::protocol_filters::set(
            protocol_filters_strings,
        ));
    }
    if let Some((proxy_config, (related_forward_endpoint_ids, related_tot_endpoint_ids))) =
        forward_config
    {
        set_args.push(prisma::outbound_endpoint::proxy_config::set(Some(
            base64::engine::general_purpose::STANDARD.encode(proxy_config),
        )));
        set_args.push(
            prisma::outbound_endpoint::related_forward_endpoint_ids::set(
                related_forward_endpoint_ids,
            ),
        );
        if let Some(related_tot_endpoint_ids) = related_tot_endpoint_ids {
            set_args.push(prisma::outbound_endpoint::related_tot_server_ids::set(
                related_tot_endpoint_ids,
            ));
        }
    }
    app_state
        .db
        .outbound_endpoint()
        .update(
            prisma::outbound_endpoint::id::equals(modify_server_request.server_id),
            set_args,
        )
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}
