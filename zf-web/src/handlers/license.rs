use anyhow::anyhow;
use chrono::Utc;
use log::{error, info};
use warp::{reject::Rejection, reply::Reply};

use crate::error::Error;
use crate::message::{RenewalHistoryItem, SoftwareLicenseInfo};
use crate::{
    message::{ApplyRenewalCodeRequest, RenewalHistoryResponse},
    AppState,
};

pub async fn handle_apply_renewal_code(
    _token_id: String,
    request: ApplyRenewalCodeRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Apply renewal code request for zf-controler software license");

    // Call zf-controler API instead of directly calling zf-auth-server
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/apply-renewal",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    info!("Calling zf-controler at: {}", controler_url);

    let response = client
        .post(&controler_url)
        .json(&request)
        .send()
        .await
        .map_err(|e| {
            error!("Failed to call zf-controler: {}", e);
            Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
        })?;

    if response.status().is_success() {
        let response_json: serde_json::Value = response.json().await.map_err(|e| {
            error!("Failed to parse zf-controler response: {}", e);
            Error::InternalError(anyhow!("Failed to parse zf-controler response"))
        })?;

        info!("Software license renewal code applied successfully");
        Ok(warp::reply::json(&response_json))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);
        Err(warp::reject::custom(Error::InternalError(anyhow!(
            "Software license renewal failed: {}",
            error_text
        ))))
    }
}

pub async fn handle_get_renewal_history(
    token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get renewal history request for token_id: {}", token_id);

    // Call zf-controler API instead of directly calling zf-auth-server
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/renewal-history",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    let response = client.get(&controler_url).send().await.map_err(|e| {
        error!("Failed to call zf-controler: {}", e);
        Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
    })?;

    if response.status().is_success() {
        let controler_response: serde_json::Value = response.json().await.map_err(|e| {
            error!("Failed to parse zf-controler response: {}", e);
            Error::InternalError(anyhow!("Failed to parse zf-controler response"))
        })?;

        let history = RenewalHistoryResponse {
            history: controler_response["data"]
                .as_array()
                .unwrap_or(&Vec::new())
                .iter()
                .filter_map(|item| {
                    Some(RenewalHistoryItem {
                        date: item["date"]
                            .as_str()
                            .and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok())
                            .map(|dt| dt.with_timezone(&Utc))?,
                        code_id: item["code_id"].as_str()?.to_string(),
                        extended_days: item["extended_days"].as_i64()? as i32,
                        new_expiry_date: item["new_expiry_date"]
                            .as_str()
                            .and_then(|s| chrono::DateTime::parse_from_rfc3339(s).ok())
                            .map(|dt| dt.with_timezone(&Utc))?,
                        status: item["status"].as_str()?.to_string(),
                    })
                })
                .collect(),
        };

        Ok(warp::reply::json(&history))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);
        // Return empty history on error instead of failing
        let history = RenewalHistoryResponse { history: vec![] };
        Ok(warp::reply::json(&history))
    }
}

pub async fn handle_get_software_license_info(
    _token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Get software license info request");

    // Call zf-controler API instead of directly calling zf-auth-server
    let client = reqwest::Client::new();
    let controler_url = format!(
        "{}/api/license/info",
        app_state.opt.mgmt_url.as_str().trim_end_matches('/')
    );

    info!("Fetching software license info from: {}", controler_url);

    let response = client.get(&controler_url).send().await.map_err(|e| {
        error!("Failed to call zf-controler: {}", e);
        Error::InternalError(anyhow!("Failed to communicate with zf-controler"))
    })?;

    if response.status().is_success() {
        let controler_response: serde_json::Value = response.json().await.map_err(|e| {
            error!("Failed to parse zf-controler response: {}", e);
            Error::InternalError(anyhow!("Failed to parse zf-controler response"))
        })?;

        // Extract license information from the zf-controler response
        let instance = &controler_response["data"];

        // Parse the license info fields
        let license_expires_at_str = instance["licenseExpiresAt"].as_str().ok_or_else(|| {
            error!("Missing licenseExpiresAt field in zf-controler response");
            Error::InternalError(anyhow!("Missing license expiration date"))
        })?;

        let license_expires_at = chrono::DateTime::parse_from_rfc3339(license_expires_at_str)
            .map_err(|e| {
                error!(
                    "Failed to parse license expiration date '{}': {}",
                    license_expires_at_str, e
                );
                Error::InternalError(anyhow!("Invalid license expiration date format"))
            })?
            .with_timezone(&Utc);

        let license_id = instance["id"].as_str().unwrap_or("unknown");

        let now = Utc::now();
        let is_expired = license_expires_at < now;
        let days_remaining = if is_expired {
            0
        } else {
            (license_expires_at - now).num_days() as i32
        };

        let license_info = SoftwareLicenseInfo {
            valid_until: license_expires_at,
            is_expired,
            days_remaining,
            license_id: license_id.to_string(),
        };

        info!("Successfully retrieved software license info");
        Ok(warp::reply::json(&license_info))
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        error!("zf-controler returned error: {}", error_text);
        Err(warp::reject::custom(Error::InternalError(anyhow!(
            "Failed to fetch software license info: {}",
            error_text
        ))))
    }
}
