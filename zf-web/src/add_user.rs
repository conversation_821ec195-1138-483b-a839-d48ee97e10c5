#![allow(unused)]
use std::sync::Arc;

use anyhow::anyhow;
// use chrono::{Days, Utc};

use crate::{
    message::{AddeUserInfo, BillingType, EditUserInfo},
    prisma,
};

impl BillingType {
    fn to_int(&self) -> u32 {
        match self {
            BillingType::Cycle { days, price } => 0,
            BillingType::OneTime { price, .. } => 1,
        }
    }
    fn price(&self) -> usize {
        match self {
            BillingType::Cycle { days, price } => *price,
            BillingType::OneTime { price, .. } => *price,
        }
    }
    fn cycle_days(&self) -> Option<usize> {
        match self {
            BillingType::Cycle { days, price } => Some(*days),
            BillingType::OneTime { price, days } => None,
        }
    }
}

pub async fn add_user_proc(
    users: Vec<AddeUserInfo>,
    db: &Arc<prisma::PrismaClient>,
) -> anyhow::Result<()> {
    // let mut all_paras = vec![];
    for u in users.iter() {
        let token_id = uuid::Uuid::new_v4().to_string();
        let owner_address = u.address.clone();
        let billing_type = u.bill_type.to_int();
        let base_price = u.bill_type.price();
        let recurring_price = u.bill_type.price();
        let bandwidth = u.bandwidth;
        let traffic = u.traffic;
        let activated = u.activated;
        let mut lines = if let Some(package_id) = u.package_id {
            let package = db
                .package()
                .find_unique(prisma::package::id::equals(package_id))
                .include(prisma::package::include!({ package_lines }))
                .exec()
                .await?
                .ok_or(anyhow!("Package not found"))?;

            package
                .package_lines
                .into_iter()
                .map(|x| x.line_id)
                .collect::<Vec<_>>()
        } else {
            u.lines.iter().map(Clone::clone).collect::<Vec<_>>()
        };
        lines.sort();
        lines.dedup();
        let params = vec![
            prisma::subscription::reset_days::set(u.bill_type.cycle_days().map(|x| x as i32)),
            prisma::subscription::last_reset::set(Some(
                prisma_client_rust::chrono::Utc::now().fixed_offset(),
            )),
            prisma::subscription::bandwidth::set(bandwidth.map(|x| x as i32)),
            prisma::subscription::lines::set(lines),
            prisma::subscription::allow_forward_endpoint::set(u.allow_forward_endpoint),
            prisma::subscription::allow_ip_num::set(u.allow_ip_num),
            prisma::subscription::allow_conn_num::set(u.allow_conn_num),
            prisma::subscription::package_id::set(u.package_id),
        ];
        let valid_until = prisma_client_rust::chrono::Utc::now()
            .checked_add_days(prisma_client_rust::chrono::Days::new(u.total_days as u64))
            .ok_or(anyhow!("can't add days"))?
            .fixed_offset();
        println!("add user: {} token: {}", u.address, token_id);
        db.subscription()
            .create(
                token_id,
                owner_address,
                billing_type as i32,
                base_price as i32,
                recurring_price as i32,
                traffic as i64,
                valid_until,
                activated,
                u.max_ports_per_server as i32,
                params,
            )
            .exec()
            .await?;
    }
    // db.subscription().create_many(all_paras).exec().await?;
    Ok(())
}

pub async fn edit_user_proc(
    edit_info: EditUserInfo,
    db: &Arc<prisma::PrismaClient>,
) -> anyhow::Result<()> {
    // First, verify the subscription exists
    let existing_subscription = db
        .subscription()
        .find_unique(prisma::subscription::id::equals(edit_info.user_id))
        .exec()
        .await?
        .ok_or(anyhow!("Subscription not found"))?;

    let billing_type = edit_info.bill_type.to_int();
    let base_price = edit_info.bill_type.price();
    let recurring_price = edit_info.bill_type.price();
    let bandwidth = edit_info.bandwidth;
    let traffic = edit_info.traffic;
    let activated = edit_info.activated;

    let mut lines = if let Some(package_id) = edit_info.package_id {
        let package = db
            .package()
            .find_unique(prisma::package::id::equals(package_id))
            .include(prisma::package::include!({ package_lines }))
            .exec()
            .await?
            .ok_or(anyhow!("Package not found"))?;
        package
            .package_lines
            .into_iter()
            .map(|x| x.line_id)
            .collect::<Vec<_>>()
    } else {
        edit_info.lines.iter().map(Clone::clone).collect::<Vec<_>>()
    };
    lines.sort();
    lines.dedup();

    let params = vec![
        prisma::subscription::owner_address::set(edit_info.address.clone()),
        prisma::subscription::billing_type::set(billing_type as i32),
        prisma::subscription::base_price::set(base_price as i32),
        prisma::subscription::recurring_price::set(recurring_price as i32),
        prisma::subscription::traffic::set(traffic as i64),
        prisma::subscription::valid_until::set(existing_subscription.valid_until),
        prisma::subscription::activated::set(activated),
        prisma::subscription::max_port_num_per_server::set(edit_info.max_ports_per_server as i32),
        prisma::subscription::reset_days::set(edit_info.bill_type.cycle_days().map(|x| x as i32)),
        prisma::subscription::bandwidth::set(bandwidth.map(|x| x as i32)),
        prisma::subscription::lines::set(lines),
        prisma::subscription::allow_forward_endpoint::set(edit_info.allow_forward_endpoint),
        prisma::subscription::allow_ip_num::set(edit_info.allow_ip_num),
        prisma::subscription::allow_conn_num::set(edit_info.allow_conn_num),
        prisma::subscription::package_id::set(edit_info.package_id),
    ];

    println!("edit user: {} id: {}", edit_info.address, edit_info.user_id);

    db.subscription()
        .update(prisma::subscription::id::equals(edit_info.user_id), params)
        .exec()
        .await?;

    Ok(())
}

pub async fn update_subscription_by_package_update(
    package_id: i32,
    db: &Arc<prisma::PrismaClient>,
    bandwidth: Option<u32>, //mbps
    traffic: u64,           // GB
    max_ports_per_server: u32,
    bill_type: BillingType,
    lines: Vec<i32>,
    allow_forward_endpoint: bool,
    allow_ip_num: Option<i32>,
    allow_conn_num: Option<i32>,
) -> anyhow::Result<()> {
    let billing_type = bill_type.to_int();
    let base_price = bill_type.price();
    let recurring_price = bill_type.price();
    let bandwidth = bandwidth;
    let traffic = traffic;

    let lines = lines.iter().map(Clone::clone).collect::<Vec<_>>();

    let params = vec![
        prisma::subscription::billing_type::set(billing_type as i32),
        prisma::subscription::base_price::set(base_price as i32),
        prisma::subscription::recurring_price::set(recurring_price as i32),
        prisma::subscription::traffic::set(traffic as i64),
        prisma::subscription::max_port_num_per_server::set(max_ports_per_server as i32),
        prisma::subscription::reset_days::set(bill_type.cycle_days().map(|x| x as i32)),
        prisma::subscription::bandwidth::set(bandwidth.map(|x| x as i32)),
        prisma::subscription::lines::set(lines),
        prisma::subscription::allow_forward_endpoint::set(allow_forward_endpoint),
        prisma::subscription::allow_ip_num::set(allow_ip_num),
        prisma::subscription::allow_conn_num::set(allow_conn_num),
    ];

    println!("update subscription by package update: {}", package_id);

    db.subscription()
        .update_many(
            vec![prisma::subscription::package_id::equals(Some(package_id))],
            params,
        )
        .exec()
        .await?;

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::message::BillingType;

    #[test]
    fn test_billing_type_conversion() {
        let cycle_billing = BillingType::Cycle {
            days: 30,
            price: 100,
        };
        let onetime_billing = BillingType::OneTime {
            price: 200,
            days: 365,
        };

        assert_eq!(cycle_billing.to_int(), 0);
        assert_eq!(onetime_billing.to_int(), 1);

        assert_eq!(cycle_billing.price(), 100);
        assert_eq!(onetime_billing.price(), 200);

        assert_eq!(cycle_billing.cycle_days(), Some(30));
        assert_eq!(onetime_billing.cycle_days(), None);
    }
}
