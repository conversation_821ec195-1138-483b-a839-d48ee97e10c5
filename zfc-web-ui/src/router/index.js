import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useSubscriptionStore } from '../stores/subscription'
import { ElMessage } from 'element-plus'
import 'element-plus/es/components/message/style/css'

const routes = [
  {
    path: '/',
    name: 'login',
    component: () => import('../views/Login.vue'),
    alias: '/login',
    meta: { requiresGuest: true }
  },
  {
    path: '/theme-test',
    name: 'theme-test',
    component: () => import('../views/ThemeTest.vue')
  },
  {
    path: '/server-management-test',
    name: 'server-management-test',
    component: () => import('../views/ServerManagementTest.vue')
  },
  {
    path: '/loading-test',
    name: 'loading-test',
    component: () => import('../views/LoadingTest.vue')
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'subscription',
        component: () => import('../views/Subscription.vue')
      },
      {
        path: 'ports',
        name: 'ports',
        component: () => import('../views/Ports.vue')
      },
      {
        path: 'forward-endpoints',
        name: 'forward-endpoints',
        component: () => import('../views/ForwardEndpoints.vue')
      },
      {
        path: 'subscription-management',
        name: 'subscription-management',
        component: () => import('../views/SubscriptionManagement.vue'),
        meta: { requiresAdmin: true }
      },
      {
        path: 'server-management',
        name: 'server-management',
        component: () => import('../views/ServerManagement.vue'),
        meta: { requiresAdmin: true }
      },
      {
        path: 'license-renewal',
        name: 'license-renewal',
        component: () => import('../views/LicenseRenewal.vue'),
        meta: { requiresAdmin: true }
      },
      {
        path: 'package-management',
        name: 'package-management',
        component: () => import('../views/PackageManagement.vue'),
        meta: { requiresAdmin: true }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  console.log('Router navigation:', from.path, '->', to.path)
  const authStore = useAuthStore()
  const subscriptionStore = useSubscriptionStore()
  
  // Wait for auth store to initialize
  if (!authStore.isInitialized) {
    console.log('Auth store not initialized, initializing...')
    await authStore.initialize()
  }

  // If route requires auth, ensure subscription data is loaded
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)

  if (requiresAuth && authStore.isAuthenticated) {
    await subscriptionStore.fetchData()
  }

  if (requiresGuest && authStore.isAuthenticated) {
    console.log('Guest route accessed while authenticated, redirecting to dashboard')
    next('/dashboard')
  } else if (requiresAuth && !authStore.isAuthenticated) {
    console.log('Auth required but not authenticated, redirecting to login')
    next('/')
  } else if (requiresAdmin && !subscriptionStore.isAdmin) {
    console.log('Admin required but not admin, redirecting to dashboard')
    ElMessage.warning('You do not have permission to access this page')
    next('/dashboard')
  } else {
    console.log('Route access granted, proceeding to:', to.path)
    next()
  }
})

export default router