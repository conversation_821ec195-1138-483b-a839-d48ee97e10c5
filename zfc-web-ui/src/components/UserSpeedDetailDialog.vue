<template>
  <el-dialog
    v-model="dialogVisible"
    :title="$t('dialogs.userSpeedDetail.title')"
    width="90%"
    top="5vh"
    @close="handleClose"
  >
    <div class="speed-detail-content">
      <!-- Historical Speed Charts -->
      <el-card class="chart-card">
        <template #header>
          <div class="chart-header">
            <h3>{{ $t('dialogs.userSpeedDetail.historicalData') }}</h3>
          </div>
        </template>

        <div class="chart-main-layout">
          <!-- Left side: Charts and Controls -->
          <div class="chart-left-panel">
            <div class="chart-controls">
              <div class="time-range-controls">
                <el-radio-group v-model="selectedTimeRange" @change="handleTimeRangeChange">
                  <el-radio-button label="1h">{{ $t('timeRange.1h') }}</el-radio-button>
                  <el-radio-button label="3h">{{ $t('timeRange.3h') }}</el-radio-button>
                  <el-radio-button label="6h">{{ $t('timeRange.6h') }}</el-radio-button>
                  <el-radio-button label="24h">{{ $t('timeRange.24h') }}</el-radio-button>
                  <el-radio-button label="7d">{{ $t('timeRange.7d') }}</el-radio-button>
                </el-radio-group>
                <el-button 
                  :icon="Refresh" 
                  @click="refreshChartData"
                  :loading="chartLoading"
                  size="small"
                >
                  {{ $t('common.refresh') }}
                </el-button>
              </div>
              
              <!-- Line Selection Controls -->
              <div class="line-selection-controls" v-if="availableLines.length > 0">
                <el-select
                  v-model="selectedLineIds"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="3"
                  :placeholder="$t('dialogs.userSpeedDetail.selectLines')"
                  style="width: 300px"
                  @change="handleLineSelectionChange"
                >
                  <template #header>
                    <el-checkbox
                      v-model="selectAll"
                      :indeterminate="isIndeterminate"
                      @change="handleSelectAll"
                    >
                      {{ $t('dialogs.userSpeedDetail.selectAll') }}
                    </el-checkbox>
                  </template>
                  <el-option
                    v-for="line in availableLines"
                    :key="line.line_id"
                    :label="line.line_name || `${$t('dialogs.userSpeedDetail.linePrefix')} ${line.line_id}`"
                    :value="line.line_id"
                  >
                    <span class="line-option">
                      <span>{{ line.line_name || `${$t('dialogs.userSpeedDetail.linePrefix')} ${line.line_id}` }}</span>
                      <span class="line-speed">{{ formatSpeed(line.latest_total_speed) }}</span>
                    </span>
                  </el-option>
                </el-select>
                
                <el-alert
                  v-if="hasMoreLines"
                  :title="$t('dialogs.userSpeedDetail.showingLines', { count: selectedLineIds.length, total: totalLinesCount })"
                  type="info"
                  show-icon
                  :closable="false"
                  style="margin-top: 8px"
                />
              </div>
            </div>

            <div v-loading="chartLoading" class="chart-container">
              <div v-if="!chartData || chartData.length === 0" class="no-chart-data">
                <el-empty :description="$t('dialogs.userSpeedDetail.noData')" />
              </div>
              <div v-else>
                <!-- Total Speed Chart -->
                <div class="chart-section">
                  <h4>{{ $t('dialogs.userSpeedDetail.totalSpeed') }}</h4>
                  <v-chart 
                    class="chart" 
                    :option="totalSpeedOption" 
                    :autoresize="true"
                    ref="totalChart"
                  />
                </div>

                <!-- Traffic Consumption Chart -->
                <div class="chart-section">
                  <h4>{{ $t('dialogs.userSpeedDetail.trafficConsumption') }}</h4>
                  <v-chart 
                    class="chart" 
                    :option="trafficConsumptionOption" 
                    :autoresize="true"
                    ref="trafficChart"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Right side: Real-time Line Speeds -->
          <div class="chart-right-panel" v-if="realtimeLineSpeeds.length > 0">
            <div class="realtime-speeds">
              <h4>{{ $t('dialogs.userSpeedDetail.realtimeSpeed') }}</h4>
              <div class="speed-grid">
                <div 
                  v-for="lineSpeed in realtimeLineSpeeds" 
                  :key="lineSpeed.line_id"
                  class="speed-item"
                >
                  <div class="line-name">{{ lineSpeed.line_name || `${$t('dialogs.userSpeedDetail.linePrefix')} ${lineSpeed.line_id}` }}</div>
                  <div class="speed-info">
                    <span class="total-speed">{{ formatSpeed(lineSpeed.upload_speed + lineSpeed.download_speed) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  DataZoomComponent
} from 'echarts/components'
import dayjs from 'dayjs'
import { getUserHistoricalSpeeds, getUserLineSpeeds } from '../api'
import { useSubscriptionStore } from '../stores/subscription'

// Register ECharts components
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  ToolboxComponent,
  DataZoomComponent
])

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'close'])

const { t } = useI18n()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const selectedTimeRange = ref('24h')
const chartLoading = ref(false)
const chartData = ref([])
const totalChart = ref(null)
const trafficChart = ref(null)

// Line selection data
const selectedLineIds = ref([])
const availableLines = ref([])
const hasMoreLines = ref(false)
const totalLinesCount = ref(0)
const selectAll = ref(false)

// Real-time line speeds data
const realtimeLineSpeeds = ref([])
const realtimeLoading = ref(false)

const subscriptionStore = useSubscriptionStore()

const handleClose = () => {
  emit('close')
}

// Computed properties for line selection
const isIndeterminate = computed(() => {
  const selectedCount = selectedLineIds.value.length
  const totalCount = availableLines.value.length
  return selectedCount > 0 && selectedCount < totalCount
})

// Line selection methods
const handleSelectAll = (checked) => {
  if (checked) {
    selectedLineIds.value = availableLines.value.map(line => line.line_id)
  } else {
    selectedLineIds.value = []
  }
  loadHistoricalData()
}

const handleLineSelectionChange = () => {
  selectAll.value = selectedLineIds.value.length === availableLines.value.length
  loadHistoricalData()
}

const formatSpeed = (bytesPerSecond) => {
  if (!bytesPerSecond || bytesPerSecond < 0) return '0 B/s'
  
  const units = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  let value = bytesPerSecond
  let unitIndex = 0
  
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024
    unitIndex++
  }
  
  return `${value.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
}

const formatTraffic = (bytes) => {
  if (!bytes || bytes < 0) return '0 B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let value = bytes
  let unitIndex = 0
  
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024
    unitIndex++
  }
  
  return `${value.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
}

const getTimeRangeInMinutes = (range) => {
  const ranges = {
    '1h': 60,
    '3h': 180,
    '6h': 360,
    '24h': 1440,
    '7d': 10080
  }
  return ranges[range] || 1440
}

const getTimeFormat = (range) => {
  switch (range) {
    case '1h':
    case '3h':
    case '6h':
      return 'HH:mm'
    case '24h':
      return 'MM-DD HH:mm'
    case '7d':
      return 'MM-DD'
    default:
      return 'MM-DD HH:mm'
  }
}

const loadHistoricalData = async () => {
  if (!subscriptionStore.data?.id) {
    ElMessage.warning(t('dialogs.userSpeedDetail.failedToGetSubscription'))
    return
  }

  chartLoading.value = true
  try {
    const timeRangeMinutes = getTimeRangeInMinutes(selectedTimeRange.value)
    const requestData = {
      subscription_id: subscriptionStore.data.id,
      time_range: selectedTimeRange.value,
      minutes: timeRangeMinutes
    }
    
    // Add line IDs filter if lines are specifically selected
    if (selectedLineIds.value.length > 0 && selectedLineIds.value.length < availableLines.value.length) {
      requestData.line_ids = selectedLineIds.value
    }
    
    const response = await getUserHistoricalSpeeds(requestData)

    chartData.value = response.data?.historical_speeds || []
    availableLines.value = response.data?.available_lines || []
    hasMoreLines.value = response.data?.has_more_lines || false
    totalLinesCount.value = response.data?.total_lines_count || 0
    
    // Initialize selected line IDs on first load
    if (selectedLineIds.value.length === 0 && availableLines.value.length > 0) {
      selectedLineIds.value = availableLines.value
        .filter(line => line.is_selected)
        .map(line => line.line_id)
      selectAll.value = selectedLineIds.value.length === availableLines.value.length
    }
    
  } catch (error) {
    console.error('Failed to load historical data:', error)
    ElMessage.error(t('dialogs.userSpeedDetail.failedToLoadHistorical'))
    chartData.value = []
    availableLines.value = []
    hasMoreLines.value = false
    totalLinesCount.value = 0
  } finally {
    chartLoading.value = false
  }
}

const totalSpeedOption = computed(() => {
  if (!chartData.value || chartData.value.length === 0) return {}

  // Filter data by selected lines if any are selected
  const filteredData = selectedLineIds.value.length > 0 
    ? chartData.value.filter(item => selectedLineIds.value.includes(item.line_id))
    : chartData.value

  // Group data by line_id
  const lineDataMap = new Map()
  filteredData.forEach(item => {
    if (!lineDataMap.has(item.line_id)) {
      lineDataMap.set(item.line_id, {
        name: item.line_name || `${t('dialogs.userSpeedDetail.linePrefix')} ${item.line_id}`,
        times: [],
        total_speeds: []
      })
    }
    const lineData = lineDataMap.get(item.line_id)
    lineData.times.push(dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value)))
    lineData.total_speeds.push(item.upload_speed + item.download_speed)
  })

  const series = Array.from(lineDataMap.values()).map(lineData => ({
    name: lineData.name,
    type: 'line',
    smooth: true,
    data: lineData.total_speeds,
    symbol: 'none',
    lineStyle: { width: 2 }
  }))

  const allTimes = Array.from(new Set(filteredData.map(item => 
    dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value))
  ))).sort()

  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach(param => {
          result += `${param.marker} ${param.seriesName}: ${formatSpeed(param.value)}<br/>`
        })
        return result
      }
    },
    legend: {
      data: series.map(s => s.name),
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: allTimes,
      axisLabel: {
        interval: 'auto',
        rotate: selectedTimeRange.value === '7d' ? 45 : 0
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => formatSpeed(value)
      }
    },
    grid: {
      bottom: 60,
      left: 80,
      right: 20,
      top: 20
    },
    dataZoom: [{
      type: 'inside',
      start: 0,
      end: 100
    }],
    series
  }
})

const trafficConsumptionOption = computed(() => {
  if (!chartData.value || chartData.value.length === 0) return {}

  // Filter data by selected lines if any are selected
  const filteredData = selectedLineIds.value.length > 0 
    ? chartData.value.filter(item => selectedLineIds.value.includes(item.line_id))
    : chartData.value

  // Group data by line_id
  const lineDataMap = new Map()
  filteredData.forEach(item => {
    if (!lineDataMap.has(item.line_id)) {
      lineDataMap.set(item.line_id, {
        name: item.line_name || `${t('dialogs.userSpeedDetail.linePrefix')} ${item.line_id}`,
        times: [],
        traffic_data: []
      })
    }
    const lineData = lineDataMap.get(item.line_id)
    lineData.times.push(dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value)))
    lineData.traffic_data.push(item.traffic_inc || 0)
  })

  const series = Array.from(lineDataMap.values()).map(lineData => ({
    name: lineData.name,
    type: 'bar',
    data: lineData.traffic_data,
    barMaxWidth: 20
  }))

  const allTimes = Array.from(new Set(filteredData.map(item => 
    dayjs(item.timestamp).format(getTimeFormat(selectedTimeRange.value))
  ))).sort()

  return {
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = `${params[0].axisValue}<br/>`
        params.forEach(param => {
          result += `${param.marker} ${param.seriesName}: ${formatTraffic(param.value)}<br/>`
        })
        return result
      }
    },
    legend: {
      data: series.map(s => s.name),
      bottom: 0
    },
    xAxis: {
      type: 'category',
      data: allTimes,
      axisLabel: {
        interval: 'auto',
        rotate: selectedTimeRange.value === '7d' ? 45 : 0
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => formatTraffic(value)
      }
    },
    grid: {
      bottom: 60,
      left: 80,
      right: 20,
      top: 20
    },
    dataZoom: [{
      type: 'inside',
      start: 0,
      end: 100
    }],
    series
  }
})

const handleTimeRangeChange = () => {
  loadHistoricalData()
}

const refreshChartData = () => {
  loadHistoricalData()
  loadRealtimeLineSpeeds()
}

const loadRealtimeLineSpeeds = async () => {
  if (!subscriptionStore.data?.id) return

  realtimeLoading.value = true
  try {
    const response = await getUserLineSpeeds(subscriptionStore.data.id)
    realtimeLineSpeeds.value = response.data?.line_speeds || []
  } catch (error) {
    console.error('Failed to load realtime line speeds:', error)
    ElMessage.error(t('dialogs.userSpeedDetail.failedToLoadRealtime'))
    realtimeLineSpeeds.value = []
  } finally {
    realtimeLoading.value = false
  }
}

watch(() => props.visible, async (newVal) => {
  if (newVal) {
    // Always fetch fresh subscription data to ensure we have the latest info
    try {
      await subscriptionStore.fetchData(true) // Force refresh
    } catch (error) {
      console.error('Failed to fetch subscription data:', error)
      ElMessage.error(t('dialogs.userSpeedDetail.failedToFetchSubscription'))
      return
    }
    loadHistoricalData()
    loadRealtimeLineSpeeds()
  }
})
</script>

<style scoped>
.speed-detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-card {
  border-radius: 8px;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-light);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 16px;
}

.chart-header h3 {
  margin: 0;
  color: var(--theme-text-primary);
}

.chart-main-layout {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.chart-left-panel {
  flex: 1;
  min-width: 0;
}

.chart-right-panel {
  width: 300px;
  flex-shrink: 0;
}

.chart-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.time-range-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.line-selection-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.line-option {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.line-speed {
  color: var(--theme-text-secondary);
  font-size: 12px;
}

.realtime-speeds {
  height: 100%;
}

.realtime-speeds h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
  border-bottom: 2px solid var(--theme-border-light);
  padding-bottom: 8px;
}

.speed-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.speed-item {
  padding: 8px 12px;
  background: var(--theme-fill-light);
  border-radius: 6px;
  border: 1px solid var(--theme-border-lighter);
}

.line-name {
  font-size: 13px;
  font-weight: 500;
  color: var(--theme-text-primary);
  margin-bottom: 4px;
}

.speed-info {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.total-speed {
  color: var(--theme-primary);
  font-weight: 600;
}

.chart-container {
  min-height: 400px;
}

.chart-section {
  margin-bottom: 40px;
}

.chart-section:last-child {
  margin-bottom: 0;
}

.chart-section h4 {
  margin: 0 0 16px 0;
  color: var(--theme-text-primary);
  font-size: 16px;
  font-weight: 600;
}

.chart {
  height: 300px;
  width: 100%;
}

.no-chart-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Responsive design */
@media (max-width: 1200px) {
  .chart-main-layout {
    flex-direction: column;
  }
  
  .chart-right-panel {
    width: 100%;
  }
  
  .speed-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .chart-controls {
    align-items: flex-start;
    width: 100%;
  }
  
  .time-range-controls {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }
  
  .line-selection-controls {
    width: 100%;
  }
  
  .line-selection-controls .el-select {
    width: 100% !important;
  }
  
  .speed-grid {
    grid-template-columns: 1fr;
  }
  
  :deep(.el-radio-group) {
    flex-wrap: wrap;
  }
  
  .chart {
    height: 250px;
  }
}

/* Dark mode compatibility */
:global(.dark) .chart-card {
  background-color: var(--theme-bg-primary);
  border-color: var(--theme-border-dark);
}
</style>