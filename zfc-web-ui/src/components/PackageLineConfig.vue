<template>
  <div class="package-line-config">
    <div v-if="loading" class="loading-state">
      {{ t('common.loading') }}...
    </div>
    
    <div v-else-if="modelValue.length === 0" class="empty-state">
      <el-empty :description="t('pages.packageManagement.pleaseSelectAtLeastOneLine')" />
      <el-button type="primary" @click="addLine">
        {{ t('pages.packageManagement.addLine') }}
      </el-button>
    </div>

    <div v-else class="lines-list">
      <div v-for="(line, index) in modelValue" :key="index" class="line-item">
        <el-card class="line-card">
          <div class="line-content">
            <div class="line-header">
              <h4>{{ t('forms.labels.line') }} {{ index + 1 }}</h4>
              <el-button 
                type="danger" 
                link 
                size="small" 
                @click="removeLine(index)"
                :disabled="modelValue.length <= 1"
              >
                {{ t('pages.packageManagement.removeLine') }}
              </el-button>
            </div>
            
            <el-form :model="line" :inline="true" class="line-form">
              <el-form-item :label="t('forms.labels.line')" required>
                <el-select
                  v-model="line.line_id"
                  :placeholder="t('forms.placeholders.selectLines')"
                  style="width: 200px"
                  @change="validateForm"
                >
                  <el-option
                    v-for="availableLine in availableLines"
                    :key="availableLine.id"
                    :label="availableLine.name"
                    :value="availableLine.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item :label="t('pages.packageManagement.bandwidthLimit')">
                <el-input-number
                  v-model="line.bandwidth_limit"
                  :min="0"
                  :max="99999"
                  :precision="0"
                  controls-position="right"
                  style="width: 150px"
                  @change="validateForm"
                />
                <span class="input-suffix">Mbps</span>
              </el-form-item>

              <el-form-item :label="t('pages.packageManagement.trafficScale')">
                <el-input-number
                  v-model="line.traffic_scale"
                  :min="0.1"
                  :max="10"
                  :precision="1"
                  :step="0.1"
                  controls-position="right"
                  style="width: 120px"
                  @change="validateForm"
                />
              </el-form-item>

              <el-form-item :label="t('pages.packageManagement.lineTraffic')">
                <el-input-number
                  v-model="line.line_traffic"
                  :min="0"
                  :max="999999"
                  :precision="0"
                  controls-position="right"
                  style="width: 150px"
                  @change="validateForm"
                />
                <span class="input-suffix">GB</span>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </div>

      <div class="add-line-section">
        <el-button type="primary" @click="addLine" :disabled="!canAddMore">
          {{ t('pages.packageManagement.addLine') }}
        </el-button>
        <span v-if="!canAddMore" class="add-line-hint">
          {{ t('pages.packageManagement.allLinesSelected') }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { getAllServerList } from '../api'

const { t } = useI18n()

// Props & Emits
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'validate'])

// Reactive data
const availableLines = ref([])
const loadingLines = ref(false)

// Computed
const canAddMore = computed(() => {
  const usedLineIds = new Set(props.modelValue.map(line => line.line_id))
  return availableLines.value.some(line => !usedLineIds.has(line.id))
})

// Methods
const loadAvailableLines = async () => {
  try {
    loadingLines.value = true
    const response = await getAllServerList()
    // Extract lines from servers - each server is treated as a line
    const lines = []
    if (response.data && response.data.servers) {
      response.data.servers.forEach(server => {
        lines.push({
          id: server.id,
          name: server.display_name || `Server ${server.id}`,
          server_name: server.display_name || `Server ${server.id}`
        })
      })
    }
    availableLines.value = lines
  } catch (error) {
    console.error('Failed to load available lines:', error)
    // Fallback with some mock data for development
    availableLines.value = [
      { id: 1, name: 'Line 1', server_name: 'Server 1' },
      { id: 2, name: 'Line 2', server_name: 'Server 2' },
      { id: 3, name: 'Line 3', server_name: 'Server 3' }
    ]
  } finally {
    loadingLines.value = false
  }
}

const addLine = () => {
  const usedLineIds = new Set(props.modelValue.map(line => line.line_id))
  const availableLine = availableLines.value.find(line => !usedLineIds.has(line.id))
  
  if (availableLine) {
    const newLine = {
      line_id: availableLine.id,
      bandwidth_limit: null,
      traffic_scale: 1.0,
      line_traffic: null
    }
    emit('update:modelValue', [...props.modelValue, newLine])
    validateForm()
  }
}

const removeLine = (index) => {
  const newLines = [...props.modelValue]
  newLines.splice(index, 1)
  emit('update:modelValue', newLines)
  validateForm()
}

const validateForm = () => {
  // Basic validation - ensure all lines have line_id selected
  const isValid = props.modelValue.length > 0 && 
                  props.modelValue.every(line => line.line_id != null)
  emit('validate', isValid)
}

// Watchers
watch(() => props.modelValue, () => {
  validateForm()
}, { deep: true })

// Lifecycle
onMounted(() => {
  loadAvailableLines()
  validateForm()
})
</script>

<style scoped>
.package-line-config {
  padding: 0;
}

.loading-state {
  text-align: center;
  color: var(--el-text-color-placeholder);
  padding: 40px 0;
}

.empty-state {
  text-align: center;
  padding: 20px 0;
}

.lines-list {
  space-y: 16px;
}

.line-item {
  margin-bottom: 16px;
}

.line-card {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
}

.line-content {
  padding: 0;
}

.line-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.line-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.line-form {
  margin: 0;
}

.line-form :deep(.el-form-item) {
  margin-bottom: 12px;
  margin-right: 16px;
}

.input-suffix {
  margin-left: 8px;
  color: var(--el-text-color-regular);
  font-size: 12px;
}

.add-line-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.add-line-hint {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

@media (max-width: 768px) {
  .line-form {
    flex-direction: column;
  }
  
  .line-form :deep(.el-form-item) {
    margin-right: 0;
    width: 100%;
  }
  
  .input-suffix {
    display: block;
    margin-left: 0;
    margin-top: 4px;
  }
}
</style>