<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? t('pages.packageManagement.editPackage') : t('pages.packageManagement.createPackage')"
    width="800px"
    :close-on-click-modal="false"
    @closed="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="140px"
      class="package-form"
    >
      <!-- Basic Information -->
      <div class="form-section">
        <h4 class="section-title">{{ t('dialogs.userDetail.basicInfo') }}</h4>
        
        <el-form-item :label="t('pages.packageManagement.packageName')" prop="name">
          <el-input
            v-model="form.name"
            :placeholder="t('pages.packageManagement.pleaseEnterPackageName')"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item :label="t('pages.packageManagement.displayName')" prop="display_name">
          <el-input
            v-model="form.display_name"
            :placeholder="t('pages.packageManagement.pleaseEnterDisplayName')"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item :label="t('pages.packageManagement.description')">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </div>

      <!-- Billing Configuration -->
      <div class="form-section">
        <h4 class="section-title">{{ t('pages.subscriptionManagement.billingType') }}</h4>
        
        <el-form-item :label="t('pages.subscriptionManagement.billingType')">
          <el-radio-group v-model="form.billing_type" @change="handleBillingTypeChange">
            <el-radio value="cycle">{{ t('pages.subscriptionManagement.cycleBilling') }}</el-radio>
            <el-radio value="onetime">{{ t('pages.subscriptionManagement.onetimeBilling') }}</el-radio>
            <el-radio value="none">{{ t('common.none') }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- Cycle Billing Fields -->
        <template v-if="form.billing_type === 'cycle'">
          <el-form-item :label="t('pages.subscriptionManagement.cycleDays')" prop="cycle_days">
            <el-input-number
              v-model="form.cycle_days"
              :min="1"
              :max="9999"
              :precision="0"
              controls-position="right"
              style="width: 200px"
            />
            <span class="input-suffix">{{ t('common.days') }}</span>
          </el-form-item>

          <el-form-item :label="t('pages.subscriptionManagement.cyclePrice')" prop="cycle_price">
            <el-input-number
              v-model="form.cycle_price"
              :min="0"
              :max="999999"
              :precision="0"
              controls-position="right"
              style="width: 200px"
            />
          </el-form-item>
        </template>

        <!-- One-time Billing Fields -->
        <template v-if="form.billing_type === 'onetime'">
          <el-form-item :label="t('pages.subscriptionManagement.onetimePrice')" prop="onetime_price">
            <el-input-number
              v-model="form.onetime_price"
              :min="0"
              :max="999999"
              :precision="0"
              controls-position="right"
              style="width: 200px"
            />
          </el-form-item>

          <el-form-item :label="t('pages.subscriptionManagement.totalDays')" prop="total_days">
            <el-input-number
              v-model="form.total_days"
              :min="1"
              :max="9999"
              :precision="0"
              controls-position="right"
              style="width: 200px"
            />
            <span class="input-suffix">{{ t('common.days') }}</span>
          </el-form-item>
        </template>
      </div>

      <!-- Package Configuration -->
      <div class="form-section">
        <h4 class="section-title">{{ t('pages.packageManagement.packageLines') }}</h4>
        
        <el-form-item :label="t('pages.packageManagement.totalTraffic')" prop="total_traffic">
          <el-input-number
            v-model="form.total_traffic"
            :min="0"
            :max="99999"
            :precision="0"
            controls-position="right"
            style="width: 200px"
          />
          <span class="input-suffix">GB</span>
        </el-form-item>

        <el-form-item :label="t('pages.packageManagement.bandwidth')" prop="bandwidth">
          <el-input-number
            v-model="form.bandwidth"
            :min="0"
            :max="99999"
            :precision="0"
            controls-position="right"
            style="width: 200px"
          />
          <span class="input-suffix">Mbps</span>
          <div class="form-help">{{ t('pages.packageManagement.leaveEmptyForUnlimited') }}</div>
        </el-form-item>

        <el-form-item :label="t('pages.packageManagement.maxPortsPerServer')" prop="max_ports_per_server">
          <el-input-number
            v-model="form.max_ports_per_server"
            :min="0"
            :max="999"
            :precision="0"
            controls-position="right"
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item :label="t('pages.packageManagement.allowIpNum')" prop="allow_ip_num">
          <el-input-number
            v-model="form.allow_ip_num"
            :min="0"
            :max="999"
            :precision="0"
            controls-position="right"
            style="width: 200px"
          />
          <div class="form-help">{{ t('pages.packageManagement.maxIpsDescription') }}</div>
        </el-form-item>

        <el-form-item :label="t('pages.packageManagement.allowConnNum')" prop="allow_conn_num">
          <el-input-number
            v-model="form.allow_conn_num"
            :min="0"
            :max="9999"
            :precision="0"
            controls-position="right"
            style="width: 200px"
          />
          <div class="form-help">{{ t('pages.packageManagement.maxConnectionsPerIpDescription') }}</div>
        </el-form-item>

        <el-form-item :label="t('pages.packageManagement.allowForwardEndpoint')">
          <el-switch
            v-model="form.allow_forward_endpoint"
            :active-text="t('pages.subscriptionManagement.enabled')"
            :inactive-text="t('pages.subscriptionManagement.disabled')"
          />
        </el-form-item>

        <el-form-item :label="t('pages.packageManagement.isActive')">
          <el-switch
            v-model="form.is_active"
            :active-text="t('pages.packageManagement.active')"
            :inactive-text="t('pages.packageManagement.inactive')"
          />
        </el-form-item>

        <el-form-item :label="t('pages.packageManagement.isDefault')">
          <el-switch
            v-model="form.is_default"
            :active-text="t('pages.packageManagement.default')"
            :inactive-text="'-'"
          />
        </el-form-item>
      </div>

      <!-- Package Lines Configuration -->
      <div class="form-section">
        <h4 class="section-title">{{ t('pages.packageManagement.packageLines') }}</h4>
        
        <PackageLineConfig
          v-model="form.lines"
          :loading="loadingLines"
          @validate="handleLinesValidate"
        />
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false" :disabled="submitting">
          {{ t('common.cancel') }}
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? t('pages.packageManagement.update') : t('pages.packageManagement.create') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { createPackage, updatePackage } from '../api'
import PackageLineConfig from './PackageLineConfig.vue'

const { t } = useI18n()

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  packageData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// Refs
const formRef = ref()
const dialogVisible = ref(false)
const submitting = ref(false)
const loadingLines = ref(false)
const linesValid = ref(true)

// Computed
const isEdit = computed(() => props.packageData !== null)

// Form data
const form = reactive({
  name: '',
  display_name: '',
  description: '',
  // Billing fields
  billing_type: 'none', // 'cycle', 'onetime', 'none'
  cycle_days: 30,
  cycle_price: 0,
  onetime_price: 0,
  total_days: 365,
  // Package config
  total_traffic: 1, // Required field with default value
  bandwidth: null,
  max_ports_per_server: 1, // Required field with default value
  allow_forward_endpoint: true,
  allow_ip_num: null,
  allow_conn_num: null,
  is_active: true,
  is_default: false,
  lines: []
})

// Form rules
const rules = {
  name: [
    { required: true, message: t('pages.packageManagement.pleaseEnterPackageName'), trigger: 'blur' },
    { min: 1, max: 50, message: t('forms.validation.required'), trigger: 'blur' }
  ],
  display_name: [
    { required: true, message: t('pages.packageManagement.pleaseEnterDisplayName'), trigger: 'blur' },
    { min: 1, max: 100, message: t('forms.validation.required'), trigger: 'blur' }
  ],
  total_traffic: [
    { required: true, message: t('pages.packageManagement.pleaseFillTotalTraffic'), trigger: 'blur' },
    { type: 'number', min: 1, message: t('pages.packageManagement.totalTrafficMustBeGreaterThanZero'), trigger: 'blur' }
  ],
  max_ports_per_server: [
    { required: true, message: t('pages.packageManagement.pleaseFillMaxPortsPerServer'), trigger: 'blur' },
    { type: 'number', min: 1, message: t('pages.packageManagement.maxPortsPerServerMustBeGreaterThanZero'), trigger: 'blur' }
  ]
}

// Watch for visibility changes
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    initForm()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// Billing type change handler
const handleBillingTypeChange = () => {
  // Reset billing fields when type changes
  if (form.billing_type === 'cycle') {
    form.cycle_days = 30
    form.cycle_price = 0
  } else if (form.billing_type === 'onetime') {
    form.onetime_price = 0  
    form.total_days = 365
  }
}

// Initialize form
const initForm = () => {
  if (props.packageData) {
    // Edit mode - populate form with existing data
    const billType = props.packageData.bill_type
    let billingType = 'none'
    let cycleDays = 30
    let cyclePrice = 0
    let onetimePrice = 0
    let totalDays = 365

    if (billType) {
      if (billType.Cycle) {
        billingType = 'cycle'
        cycleDays = billType.Cycle.days
        cyclePrice = billType.Cycle.price
      } else if (billType.OneTime) {
        billingType = 'onetime'
        onetimePrice = billType.OneTime.price
        totalDays = billType.OneTime.days
      }
    }

    Object.assign(form, {
      name: props.packageData.name || '',
      display_name: props.packageData.display_name || '',
      description: props.packageData.description || '',
      // Billing fields
      billing_type: billingType,
      cycle_days: cycleDays,
      cycle_price: cyclePrice,
      onetime_price: onetimePrice,
      total_days: totalDays,
      // Package config
      total_traffic: props.packageData.total_traffic || null,
      bandwidth: props.packageData.bandwidth || null,
      max_ports_per_server: props.packageData.max_ports_per_server || null,
      allow_forward_endpoint: props.packageData.allow_forward_endpoint ?? true,
      allow_ip_num: props.packageData.allow_ip_num || null,
      allow_conn_num: props.packageData.allow_conn_num || null,
      is_active: props.packageData.is_active ?? true,
      is_default: props.packageData.is_default ?? false,
      lines: props.packageData.package_lines ? [...props.packageData.package_lines] : []
    })
  } else {
    // Create mode - reset form
    Object.assign(form, {
      name: '',
      display_name: '',
      description: '',
      // Billing fields
      billing_type: 'none',
      cycle_days: 30,
      cycle_price: 0,
      onetime_price: 0,
      total_days: 365,
      // Package config
      total_traffic: 1, // Required field with default value
      bandwidth: null,
      max_ports_per_server: 1, // Required field with default value
      allow_forward_endpoint: true,
      allow_ip_num: null,
      allow_conn_num: null,
      is_active: true,
      is_default: false,
      lines: []
    })
  }
  
  // Reset validation
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// Handle lines validation
const handleLinesValidate = (valid) => {
  linesValid.value = valid
}

// Handle form submission
const handleSubmit = async () => {
  try {
    // Validate form
    const valid = await formRef.value.validate()
    if (!valid || !linesValid.value) {
      if (!linesValid.value) {
        ElMessage.error(t('pages.packageManagement.pleaseSelectAtLeastOneLine'))
      }
      return
    }

    submitting.value = true

    // Prepare billing data
    let billType = null
    if (form.billing_type === 'cycle') {
      billType = {
        Cycle: {
          days: form.cycle_days,
          price: form.cycle_price
        }
      }
    } else if (form.billing_type === 'onetime') {
      billType = {
        OneTime: {
          price: form.onetime_price,
          days: form.total_days
        }
      }
    }

    // Prepare data with proper null handling
    const data = {
      name: form.name.trim(),
      display_name: form.display_name.trim(),
      description: form.description ? form.description.trim() : null,
      total_traffic: form.total_traffic, // Required field, must be a number
      bandwidth: form.bandwidth > 0 ? form.bandwidth : null,
      max_ports_per_server: form.max_ports_per_server, // Required field, must be a number
      allow_forward_endpoint: Boolean(form.allow_forward_endpoint),
      allow_ip_num: form.allow_ip_num > 0 ? form.allow_ip_num : null,
      allow_conn_num: form.allow_conn_num > 0 ? form.allow_conn_num : null,
      is_active: Boolean(form.is_active),
      is_default: Boolean(form.is_default),
      bill_type: billType, // 计费信息
      lines: form.lines.map(line => ({
        line_id: line.line_id,
        bandwidth_limit: line.bandwidth_limit > 0 ? line.bandwidth_limit : null,
        traffic_scale: line.traffic_scale > 0 ? line.traffic_scale : 1.0,
        line_traffic: line.line_traffic > 0 ? line.line_traffic : null
      }))
    }

    // Add id for update
    if (isEdit.value) {
      data.id = props.packageData.id
    }

    // Submit
    if (isEdit.value) {
      await updatePackage(data)
      ElMessage.success(t('pages.packageManagement.packageUpdatedSuccessfully'))
    } else {
      await createPackage(data)
      ElMessage.success(t('pages.packageManagement.packageCreatedSuccessfully'))
    }

    emit('success')
  } catch (error) {
    console.error('Failed to submit package:', error)
    if (error.message && error.message.includes('name already exists')) {
      ElMessage.error(t('pages.packageManagement.packageNameExists'))
    } else {
      const errorMsg = isEdit.value 
        ? t('pages.packageManagement.failedToUpdatePackage')
        : t('pages.packageManagement.failedToCreatePackage')
      ElMessage.error(error.message || errorMsg)
    }
  } finally {
    submitting.value = false
  }
}

// Handle dialog close
const handleClose = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.package-form {
  margin: 0;
}

.form-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.form-section:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.section-title {
  margin: 0 0 20px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.input-suffix {
  margin-left: 8px;
  color: var(--el-text-color-regular);
}

.form-help {
  margin-top: 4px;
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 200px;
}

:deep(.el-switch) {
  height: 24px;
}

:deep(.el-switch__label) {
  font-size: 14px;
}

@media (max-width: 768px) {
  .package-form {
    padding: 0 8px;
  }
  
  :deep(.el-form-item__label) {
    width: 120px !important;
  }
  
  .input-suffix {
    display: block;
    margin-left: 0;
    margin-top: 4px;
  }
}
</style>