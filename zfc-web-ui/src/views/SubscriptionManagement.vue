<template>
  <div class="subscription-management">
    <div class="header">
      <h2>{{ t('pages.subscriptionManagement.title') }}</h2>
      <div class="header-actions">
        <el-button
          @click="toggleSearch"
          :icon="searchExpanded ? ArrowUp : ArrowDown"
          type="default"
          class="search-toggle-btn"
          :aria-expanded="searchExpanded"
          :aria-controls="'search-form-container'"
          :title="searchExpanded ? t('pages.subscriptionManagement.hideSearchFilters') : t('pages.subscriptionManagement.showSearchFilters')"
        >
          <span class="search-toggle-text">
            {{ searchExpanded ? t('pages.subscriptionManagement.hideSearch') : t('pages.subscriptionManagement.showSearch') }}
          </span>
          <el-badge
            v-if="activeFiltersCount > 0"
            :value="activeFiltersCount"
            class="filter-badge"
            type="primary"
          />
        </el-button>
        <el-button type="primary" @click="handleShowAddDialog">
          {{ t('pages.subscriptionManagement.addSubscription') }}
        </el-button>
      </div>
    </div>

    <!-- Collapsible Search Form -->
    <el-collapse-transition>
      <div
        v-show="searchExpanded"
        class="search-container"
        id="search-form-container"
        role="region"
        :aria-label="t('pages.subscriptionManagement.searchAndFilterOptions')"
      >
        <el-form :model="searchForm" class="search-form" :inline="true">
          <el-form-item :label="t('pages.subscriptionManagement.id')">
            <el-input
              v-model="searchForm.id"
              :placeholder="t('pages.subscriptionManagement.searchByExactId')"
              clearable
              style="width: 120px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item :label="t('pages.subscriptionManagement.tokenId')">
            <el-input
              v-model="searchForm.tokenId"
              :placeholder="t('pages.subscriptionManagement.searchByExactTokenId')"
              clearable
              style="width: 200px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item :label="t('pages.subscriptionManagement.email')">
            <el-input
              v-model="searchForm.email"
              :placeholder="t('pages.subscriptionManagement.searchByEmail')"
              clearable
              style="width: 200px"
              @input="handleSearchInput"
            />
          </el-form-item>
          <el-form-item :label="t('pages.subscriptionManagement.validUntil')">
            <el-date-picker
              v-model="searchForm.validUntilStart"
              type="date"
              :placeholder="t('pages.subscriptionManagement.startDate')"
              style="width: 140px"
              @change="handleSearchChange"
              clearable
            />
            <span style="margin: 0 8px;">{{ t('pages.subscriptionManagement.to') }}</span>
            <el-date-picker
              v-model="searchForm.validUntilEnd"
              type="date"
              :placeholder="t('pages.subscriptionManagement.endDate')"
              style="width: 140px"
              @change="handleSearchChange"
              clearable
            />
          </el-form-item>
          <el-form-item :label="t('pages.subscriptionManagement.nextReset')">
            <el-date-picker
              v-model="searchForm.nextResetStart"
              type="date"
              :placeholder="t('pages.subscriptionManagement.startDate')"
              style="width: 140px"
              @change="handleSearchChange"
              clearable
            />
            <span style="margin: 0 8px;">{{ t('pages.subscriptionManagement.to') }}</span>
            <el-date-picker
              v-model="searchForm.nextResetEnd"
              type="date"
              :placeholder="t('pages.subscriptionManagement.endDate')"
              style="width: 140px"
              @change="handleSearchChange"
              clearable
            />
          </el-form-item>
          <el-form-item :label="t('pages.subscriptionManagement.lines')">
            <el-select
              v-model="searchForm.lines"
              :placeholder="t('pages.subscriptionManagement.selectLines')"
              clearable
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              style="width: 200px"
              @change="handleSearchChange"
              :filter-method="filterSearchLines"
              default-first-option
              reserve-keyword
              :loading="loadingAllServers"
            >
              <el-option
                v-for="line in filteredSearchLines"
                :key="line.id"
                :label="line.display_name"
                :value="line.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              {{ t('pages.subscriptionManagement.search') }}
            </el-button>
            <el-button @click="handleClearSearch">
              {{ t('pages.subscriptionManagement.clear') }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-collapse-transition>

    <el-card class="table-card">
      <div class="table-wrapper">
        <el-table
          :data="subscriptions"
          v-loading="loading"
          style="width: 100%"
          class="subscription-table"
          :row-style="{ height: '60px' }"
        >
        <el-table-column
          prop="id"
          :label="t('pages.subscriptionManagement.id')"
          width="80"
        />
        <el-table-column
          prop="token_id"
          :label="t('pages.subscriptionManagement.tokenId')"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span
              class="token-id-cell"
              @click="handleCopyTokenId(scope.row)"
              :title="t('pages.subscriptionManagement.clickToCopy') + scope.row.token_id"
            >
              {{ scope.row.token_id }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="email_address"
          :label="t('pages.subscriptionManagement.email')"
          min-width="180"
          show-overflow-tooltip
        />
        <el-table-column
          prop="valid_until"
          :label="t('pages.subscriptionManagement.validUntil')"
          width="160"
          :formatter="formatDate"
          align="center"
        />
        <el-table-column
          prop="next_reset"
          :label="t('pages.subscriptionManagement.nextReset')"
          width="160"
          :formatter="formatDate"
          align="center"
        />
        <el-table-column
          :label="t('pages.subscriptionManagement.traffic')"
          min-width="300"
          align="center"
        >
          <template #default="scope">
            <div class="traffic-progress">
              <div class="progress-wrapper">
                <div
                  class="progress-bar"
                  :style="getTrafficProgressStyle(scope.row)"
                  :class="getTrafficProgressClass(scope.row)"
                >
                </div>
                <span class="progress-text">
                  {{ getTrafficDisplayText(scope.row) }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="lines"
          :label="t('pages.subscriptionManagement.lines')"
          width="120"
          align="center"
        >
          <template #default="scope">
            <el-popover
              placement="top"
              :width="200"
              trigger="click"
              popper-class="line-popover"
            >
              <template #reference>
                <el-tag
                  type="info"
                  class="line-count"
                  effect="plain"
                >
                  {{ scope.row.lines.length + ' ' + t('pages.subscriptionManagement.lines') }}
                </el-tag>
              </template>
              <template #default>
                <div class="line-list">
                  <div class="line-list-header">{{ t('pages.subscriptionManagement.lineCount') }}</div>
                  <el-scrollbar max-height="300px">
                    <div 
                      v-for="line in scope.row.lines" 
                      :key="line.id"
                      class="line-item"
                    >
                      <div class="line-info">
                        <div class="line-name">{{ line.display_name || 'Unnamed Line' }}</div>
                        <div class="line-status">
                          <el-tag 
                            size="small" 
                            :type="line.is_online ? 'success' : 'danger'"
                          >
                            {{ line.is_online ? t('pages.subscriptionManagement.enabled') : t('pages.subscriptionManagement.disabled') }}
                          </el-tag>
                        </div>
                      </div>
                      <div class="line-speed" v-if="line.is_online">
                        <div class="speed-item-small">
                          <span class="speed-value-small">{{ formatSpeed(getLineUploadSpeed(scope.row.id, line.id) + getLineDownloadSpeed(scope.row.id, line.id)) }}</span>
                        </div>
                      </div>
                    </div>
                  </el-scrollbar>
                </div>
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('dialogs.userDetail.realtimeSpeed')"
          width="160"
          align="center"
        >
          <template #default="scope">
            <div class="speed-display">
              <div class="speed-item">
                <span class="speed-value">{{ formatSpeed(getUserUploadSpeed(scope.row.id) + getUserDownloadSpeed(scope.row.id)) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="t('pages.subscriptionManagement.actions')"
          width="220"
          fixed="right"
          align="center"
        >
          <template #default="scope">
            <el-space>
              <el-button
                size="small"
                type="success"
                @click="handleViewDetail(scope.row)"
                :icon="View"
                circle
                :title="t('pages.subscriptionManagement.view')"
              />
              <el-button
                size="small"
                type="primary"
                @click="handleExtend(scope.row)"
                :icon="Timer"
                circle
                :title="t('pages.subscriptionManagement.extend')"
              />
              <el-button
                size="small"
                type="warning"
                @click="handleResetTraffic(scope.row)"
                :icon="Refresh"
                circle
                :title="t('pages.subscriptionManagement.resetTraffic')"
              />
              <el-button
                size="small"
                type="info"
                @click="handleEdit(scope.row)"
                circle
                :title="t('pages.subscriptionManagement.edit')"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.row)"
                :icon="Delete"
                circle
                :title="t('pages.subscriptionManagement.delete')"
              />
            </el-space>
          </template>
        </el-table-column>
        </el-table>
      </div>

      <!-- Pagination Controls -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>{{ t('messages.info.totalItems', { count: totalItems }) }}</span>
          <el-select
            v-model="pageSize"
            @change="handlePageSizeChange"
            class="page-size-selector"
            size="small"
          >
            <el-option
              v-for="size in pageSizeOptions"
              :key="size"
              :label="`${size} / page`"
              :value="size"
            />
          </el-select>
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalItems"
          :page-count="totalPages"
          layout="prev, pager, next, jumper"
          @current-change="handlePageChange"
          class="pagination-controls"
          small
        />
      </div>
    </el-card>

    <!-- Add User Dialog -->
    <el-dialog
      v-model="showAddDialog"
      :title="t('pages.subscriptionManagement.addUser')"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="180px"
        label-position="left"
      >
        <el-form-item :label="t('pages.subscriptionManagement.emailAddress')" prop="email_address">
          <el-input v-model="form.email_address" :placeholder="t('pages.subscriptionManagement.pleaseEnterEmailAddress')" />
        </el-form-item>

        <el-form-item :label="t('pages.packageManagement.selectPackage')" prop="package_id">
          <el-select
            v-model="form.package_id"
            :placeholder="t('forms.placeholders.selectPackage')"
            clearable
            filterable
            style="width: 100%"
            :loading="loadingPackages"
          >
            <el-option
              v-for="pkg in availablePackages"
              :key="pkg.id"
              :label="`${pkg.display_name} (${pkg.name})`"
              :value="pkg.id"
            />
          </el-select>
          <div class="field-tip">{{ t('pages.packageManagement.leaveEmptyForUnlimitedPackage') }}</div>
        </el-form-item>

        <el-form-item :label="t('pages.subscriptionManagement.bandwidth')" prop="bandwidth">
          <el-input-number 
            v-model="form.bandwidth" 
            :min="0"
            :placeholder="t('pages.subscriptionManagement.leaveEmptyForUnlimited')"
            :disabled="!!form.package_id"
          />
          <div v-if="form.package_id" class="field-disabled-tip">
            {{ t('pages.subscriptionManagement.fieldManagedByPackage') }}
          </div>
        </el-form-item>

        <el-form-item :label="t('pages.subscriptionManagement.trafficGB')" prop="traffic">
          <el-input-number 
            v-model="trafficGB" 
            :min="1"
            :precision="0"
            :placeholder="t('pages.subscriptionManagement.enterTrafficLimitGB')"
            :disabled="!!form.package_id"
          />
          <div v-if="form.package_id" class="field-disabled-tip">
            {{ t('pages.subscriptionManagement.fieldManagedByPackage') }}
          </div>
        </el-form-item>

        <el-form-item :label="t('pages.subscriptionManagement.maxPortsPerServer')" prop="max_ports_per_server">
          <el-input-number 
            v-model="form.max_ports_per_server" 
            :min="1"
            :precision="0"
            :disabled="!!form.package_id"
          />
          <div v-if="form.package_id" class="field-disabled-tip">
            {{ t('pages.subscriptionManagement.fieldManagedByPackage') }}
          </div>
        </el-form-item>

        <!-- Show package configuration notice when package is selected -->
        <template v-if="form.package_id">
          <div class="package-config-notice">
            <el-alert
              :title="t('pages.subscriptionManagement.usingPackageConfig')"
              type="info"
              show-icon
              :closable="false"
            >
              <template #default>
                <div>{{ t('pages.subscriptionManagement.packageConfigDescription') }}</div>
                <div class="package-config-actions">
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="handleEditPackageFromAdd"
                    class="edit-package-btn"
                  >
                    {{ t('pages.subscriptionManagement.editPackageConfig') }}
                  </el-button>
                </div>
              </template>
            </el-alert>
          </div>
          
          <!-- Show package lines (read-only) -->
          <el-form-item :label="t('pages.subscriptionManagement.packageLines')">
            <div class="package-lines-section">
              <div class="package-lines-list">
                <el-tag
                  v-for="line in packageLines"
                  :key="'pkg-' + line.id"
                  type="success"
                  effect="plain"
                  class="package-line-tag"
                >
                  {{ line.display_name }} ({{ formatTrafficScale(line.traffic_scale) }})
                  <span class="package-line-badge">{{ t('pages.subscriptionManagement.includedInPackage') }}</span>
                </el-tag>
              </div>
            </div>
          </el-form-item>
        </template>

        <!-- Show manual configuration when no package is selected -->
        <template v-else>
          <el-form-item :label="t('pages.subscriptionManagement.billingType')" prop="bill_type">
            <el-radio-group v-model="billingType">
              <el-radio label="cycle">{{ t('pages.subscriptionManagement.cycle') }}</el-radio>
              <el-radio label="onetime">{{ t('pages.subscriptionManagement.onetime') }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <template v-if="billingType === 'cycle'">
            <el-form-item :label="t('pages.subscriptionManagement.cycleDaysLabel')" prop="cycle_days">
              <el-input-number 
                v-model="form.cycle_days" 
                :min="1"
                :precision="0"
              />
            </el-form-item>
            <el-form-item :label="t('pages.subscriptionManagement.cyclePriceLabel')" prop="cycle_price">
              <el-input-number 
                v-model="form.cycle_price" 
                :min="0"
                :precision="0"
              />
            </el-form-item>
          </template>

          <template v-else>
            <el-form-item :label="t('pages.subscriptionManagement.totalDaysLabel')" prop="total_days">
              <el-input-number 
                v-model="form.total_days" 
                :min="1"
                :precision="0"
              />
            </el-form-item>
            <el-form-item :label="t('pages.subscriptionManagement.onetimePriceLabel')" prop="onetime_price">
              <el-input-number 
                v-model="form.onetime_price" 
                :min="0"
                :precision="0"
              />
            </el-form-item>
          </template>

          <el-form-item :label="t('pages.subscriptionManagement.linesLabel')" prop="lines">
            <el-select
              v-model="form.lines"
              multiple
              filterable
              remote
              :remote-method="searchServers"
              :loading="loadingAllServers"
              :placeholder="t('pages.subscriptionManagement.searchAndSelectLines')"
              style="width: 100%"
            >
              <el-option
                v-for="line in filteredDialogLines"
                :key="line.id"
                :label="formatLineDisplayName(line)"
                :value="line.id"
              />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item :label="t('pages.subscriptionManagement.maxIpsLabel')" prop="allow_ip_num">
          <el-input-number 
            v-model="form.allow_ip_num" 
            :min="0"
            :placeholder="t('pages.subscriptionManagement.leaveEmptyUnlimited')"
            controls-position="right"
            style="width: 160px"
            :disabled="!!form.package_id"
          />
          <div v-if="!form.package_id" class="field-tip">{{ t('pages.subscriptionManagement.maxIpsDescription') }}</div>
          <div v-else class="field-disabled-tip">{{ t('pages.subscriptionManagement.fieldManagedByPackage') }}</div>
        </el-form-item>

        <el-form-item :label="t('pages.subscriptionManagement.maxConnectionsPerIpLabel')" prop="allow_conn_num">
          <el-input-number 
            v-model="form.allow_conn_num" 
            :min="0"
            :placeholder="t('pages.subscriptionManagement.leaveEmptyUnlimited')"
            controls-position="right"
            style="width: 160px"
            :disabled="!!form.package_id"
          />
          <div v-if="!form.package_id" class="field-tip">{{ t('pages.subscriptionManagement.maxConnectionsPerIpDescription') }}</div>
          <div v-else class="field-disabled-tip">{{ t('pages.subscriptionManagement.fieldManagedByPackage') }}</div>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="form.activated">{{ t('pages.subscriptionManagement.activateImmediatelyLabel') }}</el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-checkbox 
            v-model="form.allow_forward_endpoint"
            :disabled="!!form.package_id"
          >
            {{ t('pages.subscriptionManagement.allowForwardEndpointLabel') }}
          </el-checkbox>
          <div v-if="form.package_id" class="field-disabled-tip">
            {{ t('pages.subscriptionManagement.fieldManagedByPackage') }}
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">{{ t('pages.subscriptionManagement.cancel') }}</el-button>
          <el-button type="primary" @click="handleAddSubmit" :loading="submitting">
            {{ t('pages.subscriptionManagement.add') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Edit User Dialog -->
    <el-dialog
      v-model="showEditDialog"
:title="t('pages.subscriptionManagement.editSubscription')"
      width="600px"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="rules"
        label-width="180px"
        label-position="left"
      >
        <el-form-item :label="t('pages.subscriptionManagement.emailAddress')" prop="email_address">
          <el-input v-model="editForm.email_address" :placeholder="t('pages.subscriptionManagement.enterEmailAddress')" />
        </el-form-item>

        <el-form-item :label="t('pages.packageManagement.selectPackage')" prop="package_id">
          <el-select
            v-model="editForm.package_id"
            :placeholder="t('forms.placeholders.selectPackage')"
            clearable
            filterable
            style="width: 100%"
            :loading="loadingPackages"
          >
            <el-option
              v-for="pkg in availablePackages"
              :key="pkg.id"
              :label="`${pkg.display_name} (${pkg.name})`"
              :value="pkg.id"
            />
          </el-select>
          <div class="field-tip">{{ t('pages.packageManagement.leaveEmptyForUnlimitedPackage') }}</div>
        </el-form-item>

        <el-form-item :label="t('pages.subscriptionManagement.bandwidth')" prop="bandwidth">
          <el-input-number
            v-model="editForm.bandwidth"
            :min="0"
            :placeholder="t('pages.subscriptionManagement.leaveEmptyUnlimited')"
            :disabled="!!editForm.package_id"
          />
          <div v-if="editForm.package_id" class="field-disabled-tip">
            {{ t('pages.subscriptionManagement.fieldManagedByPackage') }}
          </div>
        </el-form-item>

        <el-form-item :label="t('pages.subscriptionManagement.trafficGB')" prop="traffic">
          <el-input-number
            v-model="editTrafficGB"
            :min="1"
            :precision="0"
            :placeholder="t('pages.subscriptionManagement.enterTrafficLimitGB')"
            :disabled="!!editForm.package_id"
          />
          <div v-if="editForm.package_id" class="field-disabled-tip">
            {{ t('pages.subscriptionManagement.fieldManagedByPackage') }}
          </div>
        </el-form-item>

        <el-form-item :label="t('pages.subscriptionManagement.maxPortsPerServer')" prop="max_ports_per_server">
          <el-input-number
            v-model="editForm.max_ports_per_server"
            :min="1"
            :max="100"
            :disabled="!!editForm.package_id"
          />
          <div v-if="editForm.package_id" class="field-disabled-tip">
            {{ t('pages.subscriptionManagement.fieldManagedByPackage') }}
          </div>
        </el-form-item>

        <!-- Show package configuration notice when package is selected -->
        <template v-if="editForm.package_id">
          <div class="package-config-notice">
            <el-alert
              :title="t('pages.subscriptionManagement.usingPackageConfig')"
              type="info"
              show-icon
              :closable="false"
            >
              <template #default>
                <div>{{ t('pages.subscriptionManagement.packageConfigDescription') }}</div>
                <div class="package-config-actions">
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="handleEditPackage"
                    class="edit-package-btn"
                  >
                    {{ t('pages.subscriptionManagement.editPackageConfig') }}
                  </el-button>
                </div>
              </template>
            </el-alert>
          </div>
          
          <!-- Show package lines (read-only) -->
          <el-form-item :label="t('pages.subscriptionManagement.packageLines')">
            <div class="package-lines-section">
              <div class="package-lines-list">
                <el-tag
                  v-for="line in editPackageLines"
                  :key="'edit-pkg-' + line.id"
                  type="success"
                  effect="plain"
                  class="package-line-tag"
                >
                  {{ line.display_name }} ({{ formatTrafficScale(line.traffic_scale) }})
                  <span class="package-line-badge">{{ t('pages.subscriptionManagement.includedInPackage') }}</span>
                </el-tag>
              </div>
            </div>
          </el-form-item>
        </template>

        <!-- Show manual configuration when no package is selected -->
        <template v-else>
          <el-form-item :label="t('pages.subscriptionManagement.billingType')">
            <el-radio-group v-model="editBillingType">
              <el-radio label="cycle">{{ t('pages.subscriptionManagement.cycleBillingLabel') }}</el-radio>
              <el-radio label="onetime">{{ t('pages.subscriptionManagement.onetimeBillingLabel') }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <div v-if="editBillingType === 'cycle'">
            <el-form-item :label="t('pages.subscriptionManagement.cycleDaysLabel')" prop="cycle_days">
              <el-input-number
                v-model="editForm.cycle_days"
                :min="1"
                :max="365"
              />
            </el-form-item>

            <el-form-item :label="t('pages.subscriptionManagement.cyclePriceLabel')" prop="cycle_price">
              <el-input-number
                v-model="editForm.cycle_price"
                :min="0"
                :precision="2"
              />
            </el-form-item>
          </div>

          <div v-else>
            <el-form-item :label="t('pages.subscriptionManagement.onetimePriceLabel')" prop="onetime_price">
              <el-input-number
                v-model="editForm.onetime_price"
                :min="0"
                :precision="2"
              />
            </el-form-item>

            <el-form-item :label="t('pages.subscriptionManagement.totalDaysLabel')" prop="total_days">
              <el-input-number
                v-model="editForm.total_days"
                :min="1"
                :max="3650"
              />
            </el-form-item>
          </div>

          <el-form-item :label="t('pages.subscriptionManagement.linesLabel')" prop="lines">
            <el-select
              v-model="editForm.lines"
              multiple
              filterable
              remote
              :remote-method="searchServers"
              :placeholder="t('pages.subscriptionManagement.searchAndSelectLines')"
              style="width: 100%"
              :loading="loadingAllServers"
            >
              <el-option
                v-for="line in filteredDialogLines"
                :key="line.id"
                :label="formatLineDisplayName(line)"
                :value="line.id"
              />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item :label="t('pages.subscriptionManagement.maxIpsLabel')" prop="allow_ip_num">
          <el-input-number 
            v-model="editForm.allow_ip_num" 
            :min="0"
            :placeholder="t('pages.subscriptionManagement.leaveEmptyUnlimited')"
            controls-position="right"
            style="width: 160px"
            :disabled="!!editForm.package_id"
          />
          <div v-if="!editForm.package_id" class="field-tip">{{ t('pages.subscriptionManagement.maxIpsDescription') }}</div>
          <div v-else class="field-disabled-tip">{{ t('pages.subscriptionManagement.fieldManagedByPackage') }}</div>
        </el-form-item>

        <el-form-item :label="t('pages.subscriptionManagement.maxConnectionsPerIpLabel')" prop="allow_conn_num">
          <el-input-number 
            v-model="editForm.allow_conn_num" 
            :min="0"
            :placeholder="t('pages.subscriptionManagement.leaveEmptyUnlimited')"
            controls-position="right"
            style="width: 160px"
            :disabled="!!editForm.package_id"
          />
          <div v-if="!editForm.package_id" class="field-tip">{{ t('pages.subscriptionManagement.maxConnectionsPerIpDescription') }}</div>
          <div v-else class="field-disabled-tip">{{ t('pages.subscriptionManagement.fieldManagedByPackage') }}</div>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="editForm.activated">{{ t('pages.subscriptionManagement.activateImmediatelyLabel') }}</el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="editForm.allow_forward_endpoint">{{ t('pages.subscriptionManagement.allowForwardEndpointLabel') }}</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">{{ t('pages.subscriptionManagement.cancel') }}</el-button>
          <el-button type="primary" @click="handleEditSubmit" :loading="submitting">
            {{ t('pages.subscriptionManagement.update') }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- User Detail Dialog -->
    <UserDetailDialog
      v-model:visible="showDetailDialog"
      :user-data="selectedUserForDetail"
      @close="showDetailDialog = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getSubscriptionList, addUser, editUser, removeUser, extendSubscriptionTime, resetUserTraffic, getServerList, getAllServerList, getUserSpeeds, getDetailedPackageList } from '../api'
import dayjs from 'dayjs'
import { Timer, Delete, Refresh, Edit, ArrowUp, ArrowDown, View } from '@element-plus/icons-vue'
import { useThemeStore } from '../stores/theme'
import UserDetailDialog from '../components/UserDetailDialog.vue'

// i18n setup
const { t } = useI18n()

// Theme store
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// Removed reactive headerCellStyle for performance - using static CSS instead

const loading = ref(false)
const submitting = ref(false)
const showAddDialog = ref(false)
const showEditDialog = ref(false)
const showDetailDialog = ref(false)
const formRef = ref(null)
const editFormRef = ref(null)
const subscriptions = ref([])
const selectedUserForDetail = ref(null)

// User speeds state
const userSpeeds = ref({})
let speedUpdateTimer = null

// Pagination state
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)
const totalPages = ref(1)
const pageSizeOptions = [20, 50, 100, 200]

// Search state
const searchForm = ref({
  id: '',
  tokenId: '',
  email: '',
  validUntilStart: '',
  validUntilEnd: '',
  nextResetStart: '',
  nextResetEnd: '',
  lines: []
})
const searchTimeout = ref(null)

// Search UI state
const searchExpanded = ref(false)

const billingType = ref('cycle')
const editBillingType = ref('cycle')
const trafficGB = ref(100)
const editTrafficGB = ref(100)
const editingUser = ref(null)

const availableLines = ref([])
const loadingServers = ref(false)

// Global data for search dropdowns
const allAvailableLines = ref([])
const loadingAllServers = ref(false)

// Package data
const availablePackages = ref([])
const loadingPackages = ref(false)
const packageLines = ref([])
const editPackageLines = ref([])

// Load all servers for search dropdown (admin only)
const loadAllServersForSearch = async () => {
  loadingAllServers.value = true
  try {
    const response = await getAllServerList()
    const serverList = response.data?.servers || []
    if (Array.isArray(serverList)) {
      allAvailableLines.value = serverList.map(server => ({
        id: server.id,
        display_name: server.display_name || `Server ${server.id}`
      }))
      console.log('Successfully loaded all servers for search:', allAvailableLines.value.length, 'items')
      // Initialize filtered search lines and dialog lines
      filteredSearchLines.value = allAvailableLines.value
      searchServers('') // Initialize dialog lines with filtered data
    } else {
      throw new Error('Invalid server list format')
    }
  } catch (error) {
    ElMessage.error(t('messages.error.failedToLoadCompleteServerList'))
    console.error('Failed to load complete server list:', error)
    allAvailableLines.value = []
    filteredSearchLines.value = []
    // Will be initialized when searchServers is called
  } finally {
    loadingAllServers.value = false
  }
}

// Load server list from API (for form dropdowns)
const loadServerList = async () => {
  loadingServers.value = true
  try {
    const response = await getServerList()
    const serverList = response.data?.servers || []
    if (Array.isArray(serverList)) {
      availableLines.value = serverList.map(server => ({
        id: server.id,
        display_name: server.display_name || `Server ${server.id}`
      }))
    } else {
      throw new Error('Invalid server list format')
    }
  } catch (error) {
    ElMessage.error(t('messages.error.failedToLoadServerList'))
    console.error('Failed to load server list:', error)
  } finally {
    loadingServers.value = false
  }
}

// Load available packages for selection
const loadAvailablePackages = async () => {
  try {
    loadingPackages.value = true
    const response = await getDetailedPackageList()
    if (response.data && Array.isArray(response.data.packages)) {
      availablePackages.value = response.data.packages
      console.log('Successfully loaded packages:', availablePackages.value.length, 'items')
    } else {
      throw new Error('Invalid package list format')
    }
  } catch (error) {
    console.error('Failed to load packages:', error)
    ElMessage.error('Failed to load available packages')
    availablePackages.value = []
  } finally {
    loadingPackages.value = false
  }
}

const form = ref({
  email_address: '',
  bandwidth: null,
  traffic: 0,
  activated: true,
  max_ports_per_server: 1,
  cycle_days: 30,
  cycle_price: 0,
  onetime_price: 0,
  total_days: 365,
  lines: [],
  allow_forward_endpoint: false,
  allow_ip_num: null,
  allow_conn_num: null,
  package_id: null
})

const editForm = ref({
  user_id: null,
  email_address: '',
  bandwidth: null,
  traffic: 0,
  activated: true,
  max_ports_per_server: 1,
  cycle_days: 30,
  cycle_price: 0,
  onetime_price: 0,
  total_days: 365,
  lines: [],
  allow_forward_endpoint: false,
  allow_ip_num: null,
  allow_conn_num: null,
  package_id: null
})

const rules = {
  email_address: [
    { required: true, message: t('pages.subscriptionManagement.pleaseEnterEmailAddress'), trigger: 'blur' },
    { type: 'email', message: t('pages.subscriptionManagement.pleaseEnterValidEmailAddress'), trigger: 'blur' }
  ],
  max_ports_per_server: [
    { required: true, message: t('pages.subscriptionManagement.pleaseEnterMaxPortsPerServer'), trigger: 'blur' }
  ],
  lines: [
    { required: true, message: t('pages.subscriptionManagement.pleaseSelectAtLeastOneLine'), trigger: 'change' }
  ]
}

// Watch traffic GB and convert to bytes
watch(trafficGB, (newValue) => {
  form.value.traffic = newValue
})

watch(editTrafficGB, (newValue) => {
  editForm.value.traffic = newValue
})

// Watch package selection to auto-fill form data
watch(() => form.value.package_id, (newPackageId) => {
  if (newPackageId && availablePackages.value?.length > 0) {
    const selectedPackage = availablePackages.value.find(pkg => pkg.id === newPackageId)
    if (selectedPackage) {
      // Auto-fill form with package data
      form.value.bandwidth = selectedPackage.bandwidth
      const GB = 1024 * 1024 * 1024
      trafficGB.value = Math.round(selectedPackage.total_traffic / GB)
      form.value.max_ports_per_server = selectedPackage.max_ports_per_server
      form.value.allow_forward_endpoint = selectedPackage.allow_forward_endpoint
      form.value.allow_ip_num = selectedPackage.allow_ip_num
      form.value.allow_conn_num = selectedPackage.allow_conn_num
      
      // Set package lines for display
      packageLines.value = selectedPackage.lines ? selectedPackage.lines.map(line => ({
        id: line.id,
        display_name: line.display_name,
        traffic_scale: line.traffic_scale || 1.0
      })) : []
      
      // Auto-fill billing information from package
      if (selectedPackage.bill_type) {
        if (selectedPackage.bill_type.Cycle) {
          billingType.value = 'cycle'
          form.value.cycle_days = selectedPackage.bill_type.Cycle.days
          form.value.cycle_price = selectedPackage.bill_type.Cycle.price
        } else if (selectedPackage.bill_type.OneTime) {
          billingType.value = 'onetime'
          form.value.onetime_price = selectedPackage.bill_type.OneTime.price
          form.value.total_days = selectedPackage.bill_type.OneTime.days
        }
      }
      
      // Clear any previously selected additional lines since we're changing packages
      form.value.lines = []
    }
  } else {
    // Clear package lines when no package is selected
    packageLines.value = []
  }
})

// Watch package selection for edit form
watch(() => editForm.value.package_id, (newPackageId) => {
  if (newPackageId && availablePackages.value?.length > 0) {
    const selectedPackage = availablePackages.value.find(pkg => pkg.id === newPackageId)
    if (selectedPackage) {
      // Auto-fill edit form with package data
      editForm.value.bandwidth = selectedPackage.bandwidth
      const GB = 1024 * 1024 * 1024
      editTrafficGB.value = Math.round(selectedPackage.total_traffic / GB)
      editForm.value.max_ports_per_server = selectedPackage.max_ports_per_server
      editForm.value.allow_forward_endpoint = selectedPackage.allow_forward_endpoint
      editForm.value.allow_ip_num = selectedPackage.allow_ip_num
      editForm.value.allow_conn_num = selectedPackage.allow_conn_num
      
      // Set package lines for edit form display
      editPackageLines.value = selectedPackage.lines ? selectedPackage.lines.map(line => ({
        id: line.id,
        display_name: line.display_name,
        traffic_scale: line.traffic_scale || 1.0
      })) : []
      
      // Auto-fill billing information from package
      if (selectedPackage.bill_type) {
        if (selectedPackage.bill_type.Cycle) {
          editBillingType.value = 'cycle'
          editForm.value.cycle_days = selectedPackage.bill_type.Cycle.days
          editForm.value.cycle_price = selectedPackage.bill_type.Cycle.price
        } else if (selectedPackage.bill_type.OneTime) {
          editBillingType.value = 'onetime'
          editForm.value.onetime_price = selectedPackage.bill_type.OneTime.price
          editForm.value.total_days = selectedPackage.bill_type.OneTime.days
        }
      }
      
      // Filter out package lines from additional lines selection
      const packageLineIds = new Set(selectedPackage.lines?.map(line => line.id) || [])
      const currentAdditionalLines = editForm.value.lines.filter(lineId => !packageLineIds.has(lineId))
      editForm.value.lines = currentAdditionalLines
    }
  } else {
    // Clear package lines when no package is selected
    editPackageLines.value = []
  }
})

const resetForm = () => {
  formRef.value?.resetFields()
  form.value = {
    email_address: '',
    bandwidth: null,
    traffic: 0,
    activated: true,
    max_ports_per_server: 1,
    cycle_days: 30,
    cycle_price: 0,
    onetime_price: 0,
    total_days: 365,
    lines: [],
    allow_forward_endpoint: false,
    allow_ip_num: null,
    allow_conn_num: null,
    package_id: null
  }
  trafficGB.value = 100
  billingType.value = 'cycle'
}

const resetEditForm = () => {
  editFormRef.value?.resetFields()
  editForm.value = {
    user_id: null,
    email_address: '',
    bandwidth: null,
    traffic: 0,
    activated: false, // Default to false for consistency
    max_ports_per_server: 1,
    cycle_days: 30,
    cycle_price: 0,
    onetime_price: 0,
    total_days: 365,
    lines: [],
    allow_forward_endpoint: false, // Explicitly set to false
    allow_ip_num: null,
    allow_conn_num: null,
    package_id: null
  }
  editTrafficGB.value = 100
  editBillingType.value = 'cycle'
}

const handleAddSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const submitData = {
          address: form.value.email_address,
          bandwidth: form.value.bandwidth,
          traffic: trafficGB.value,
          activated: form.value.activated,
          max_ports_per_server: form.value.max_ports_per_server,
          bill_type: billingType.value === 'cycle'
            ? { Cycle: { days: form.value.cycle_days, price: form.value.cycle_price } }
            : { OneTime: { price: form.value.onetime_price, days: form.value.total_days } },
          total_days: billingType.value === 'cycle' ? form.value.cycle_days : form.value.total_days,
          lines: form.value.lines,
          allow_forward_endpoint: form.value.allow_forward_endpoint,
          allow_ip_num: form.value.allow_ip_num,
          allow_conn_num: form.value.allow_conn_num,
          package_id: form.value.package_id
        }

        await addUser(submitData)
        ElMessage.success(t('messages.success.subscriptionAdded'))
        showAddDialog.value = false
        resetForm()
        loadSubscriptions(currentPage.value, pageSize.value)
      } catch (error) {
        console.error('Failed to add subscription:', error)
        ElMessage.error(t('messages.error.failedToAddSubscription'))
      } finally {
        submitting.value = false
      }
    }
  })
}

const formatDate = (row, column, cellValue) => {
  if (!cellValue) return '-'
  return dayjs(cellValue).format('YYYY-MM-DD HH:mm')
}

// Optimized traffic formatting functions to prevent multiple calculations per row
const formatTrafficBytes = (bytes) => {
  if (bytes === 0) return '0 GB'
  const GB = 1024 * 1024 * 1024
  return `${(bytes / GB).toFixed(2)} GB`
}

const getTrafficPercentage = (row) => {
  if (!row.traffic_total || !row.traffic_used) return 0
  const percentage = (row.traffic_used / row.traffic_total) * 100
  return Math.min(percentage, 100)
}

// Performance-optimized functions that calculate once per row
const getTrafficProgressStyle = (row) => {
  const percentage = getTrafficPercentage(row)
  return { width: `${percentage}%` }
}

const getTrafficProgressClass = (row) => {
  const percentage = getTrafficPercentage(row)
  return { 'zero-progress': percentage === 0 }
}

const getTrafficDisplayText = (row) => {
  const usedText = formatTrafficBytes(row.traffic_used)
  const totalText = formatTrafficBytes(row.traffic_total)
  const percentage = getTrafficPercentage(row).toFixed(1)
  return `${usedText} / ${totalText} (${percentage}%)`
}

const handleAdd = () => {
  // TODO: Implement add functionality when backend API is ready
  console.log('Add new subscription')
}

const handleViewDetail = (row) => {
  selectedUserForDetail.value = row
  showDetailDialog.value = true
}

const handleEdit = (row) => {
  console.log('Edit:', row)
  console.log('Row activated field:', row.activated)
  console.log('Row allow_forward_endpoint field:', row.allow_forward_endpoint)
  editingUser.value = row

  // Ensure allAvailableLines includes all currently assigned lines
  if (row.lines && Array.isArray(row.lines)) {
    const currentLineIds = new Set(allAvailableLines.value.map(line => line.id))
    const missingLines = row.lines.filter(line => !currentLineIds.has(line.id))

    if (missingLines.length > 0) {
      // Add missing lines to allAvailableLines so they can be displayed properly
      allAvailableLines.value = [...allAvailableLines.value, ...missingLines.map(line => ({
        id: line.id,
        display_name: line.display_name || `Server ${line.id}`
      }))]
      // Update filtered lists as well
      filteredSearchLines.value = allAvailableLines.value
      searchServers('') // Re-initialize dialog lines with filtered data
    }
  }

  // Pre-populate the edit form with existing data
  // Extract billing information from the bill_type object
  let cycle_days = 30
  let cycle_price = 0
  let onetime_price = 0
  let billing_type = 'cycle'

  if (row.bill_type) {
    if (row.bill_type.Cycle) {
      billing_type = 'cycle'
      cycle_days = row.bill_type.Cycle.days || 30
      cycle_price = row.bill_type.Cycle.price || 0
    } else if (row.bill_type.OneTime) {
      billing_type = 'onetime'
      onetime_price = row.bill_type.OneTime.price || 0
      cycle_days = row.bill_type.OneTime.days || 365
    }
  }

  editForm.value = {
    user_id: row.id,
    email_address: row.email_address || row.owner_address || '',
    bandwidth: row.bandwidth,
    traffic: row.traffic_total,
    activated: Boolean(row.activated), // Ensure boolean value
    max_ports_per_server: row.max_ports_per_server,
    cycle_days: cycle_days,
    cycle_price: cycle_price,
    onetime_price: onetime_price,
    total_days: row.total_days || cycle_days,
    lines: row.lines ? row.lines.map(line => line.id) : [],
    allow_forward_endpoint: Boolean(row.allow_forward_endpoint), // Ensure boolean value
    allow_ip_num: row.allow_ip_num,
    allow_conn_num: row.allow_conn_num,
    package_id: row.package_id || null
  }

  // Set traffic in GB for the input
  const GB = 1024 * 1024 * 1024
  editTrafficGB.value = Math.round(row.traffic_total / GB)

  // Set billing type based on the bill_type object
  editBillingType.value = billing_type
  
  // Set package lines for edit form if package is selected
  if (editForm.value.package_id && availablePackages.value?.length > 0) {
    const selectedPackage = availablePackages.value.find(pkg => pkg.id === editForm.value.package_id)
    if (selectedPackage && selectedPackage.lines) {
      editPackageLines.value = selectedPackage.lines.map(line => ({
        id: line.id,
        display_name: line.display_name || allAvailableLines.value.find(l => l.id === line.id)?.display_name || `Server ${line.id}`
      }))
      
      // Filter out package lines from the selected lines to show only additional lines
      const packageLineIds = new Set(selectedPackage.lines.map(line => line.id))
      editForm.value.lines = (row.lines ? row.lines.map(line => line.id) : []).filter(lineId => !packageLineIds.has(lineId))
    } else {
      editPackageLines.value = []
    }
  } else {
    editPackageLines.value = []
  }

  showEditDialog.value = true
}

const handleEditSubmit = async () => {
  if (!editFormRef.value) return

  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const submitData = {
          user_id: editForm.value.user_id,
          address: editForm.value.email_address,
          bandwidth: editForm.value.bandwidth,
          traffic: editTrafficGB.value,
          activated: Boolean(editForm.value.activated), // Ensure boolean value is always sent
          max_ports_per_server: editForm.value.max_ports_per_server,
          bill_type: editBillingType.value === 'cycle'
            ? { Cycle: { days: editForm.value.cycle_days, price: editForm.value.cycle_price } }
            : { OneTime: { price: editForm.value.onetime_price, days: editForm.value.total_days } },
          total_days: editBillingType.value === 'cycle' ? editForm.value.cycle_days : editForm.value.total_days,
          lines: editForm.value.lines,
          allow_forward_endpoint: Boolean(editForm.value.allow_forward_endpoint), // Ensure boolean value is always sent
          allow_ip_num: editForm.value.allow_ip_num,
          allow_conn_num: editForm.value.allow_conn_num,
          package_id: editForm.value.package_id
        }

        console.log('Submitting edit data:', submitData) // Debug log to verify data structure
        await editUser(submitData)
        ElMessage.success(t('messages.success.subscriptionUpdated'))
        showEditDialog.value = false
        resetEditForm()
        loadSubscriptions(currentPage.value, pageSize.value)
      } catch (error) {
        console.error('Failed to update subscription:', error)
        ElMessage.error(t('messages.error.failedToUpdateSubscription'))
      } finally {
        submitting.value = false
      }
    }
  })
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      t('messages.confirm.deleteSubscription'),
      t('common.warning'),
      {
        confirmButtonText: t('actions.delete'),
        cancelButtonText: t('actions.cancel'),
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    loading.value = true
    await removeUser(row.id)
    ElMessage.success(t('messages.success.subscriptionDeleted'))
    await loadSubscriptions(currentPage.value, pageSize.value)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(t('messages.error.failedToDeleteSubscription'))
    }
  } finally {
    loading.value = false
  }
}

const handleExtend = async (row) => {
  try {
    const { value: days } = await ElMessageBox.prompt(
      'Enter number of days to extend (leave empty to use subscription cycle)',
      'Extend Subscription',
      {
        confirmButtonText: 'Extend',
        cancelButtonText: 'Cancel',
        inputPattern: /^[0-9]*$/,
        inputErrorMessage: 'Please enter a valid number',
        inputPlaceholder: 'Enter days or leave empty',
        showClose: false
      }
    )
    
    loading.value = true
    const daysNumber = days === '' ? null : parseInt(days)
    await extendSubscriptionTime(row.id, daysNumber)
    ElMessage.success(t('messages.success.subscriptionExtended'))
    await loadSubscriptions(currentPage.value, pageSize.value)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(t('messages.error.failedToExtendSubscription'))
    }
  } finally {
    loading.value = false
  }
}

const handleResetTraffic = (row) => {
  ElMessageBox.confirm(
    t('messages.confirm.resetTraffic'),
    t('common.warning'),
    {
      confirmButtonText: t('actions.reset'),
      cancelButtonText: t('actions.cancel'),
      type: 'warning',
    }
  ).then(async () => {
    try {
      await resetUserTraffic(row.id)
      ElMessage.success(t('messages.success.trafficReset'))
      loadSubscriptions(currentPage.value, pageSize.value)  // 重新加载列表以更新数据
    } catch (error) {
      console.error('Error resetting traffic:', error)
      ElMessage.error(error.message || t('messages.error.failedToResetTraffic'))
    }
  }).catch(() => {
    // 用户取消操作
  })
}

const handleCopyTokenId = async (row) => {
  const tokenId = row.token_id
  const email = row.email_address || row.owner_address || 'Unknown'
  const subscriptionId = row.id

  try {
    // Try modern Clipboard API first
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(tokenId)
    } else {
      // Fallback for older browsers or non-secure contexts
      const textArea = document.createElement('textarea')
      textArea.value = tokenId
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      try {
        document.execCommand('copy')
        textArea.remove()
      } catch (err) {
        console.error('Failed to copy text:', err)
        textArea.remove()
        throw new Error('Failed to copy text')
      }
    }

    // Show success notification with detailed information
    ElMessage.success(t('messages.success.tokenCopied', { tokenId, email, subscriptionId }))
  } catch (err) {
    console.error('Failed to copy token ID:', err)
    ElMessage.error(t('messages.error.failedToCopyToken'))
  }
}

const loadSubscriptions = async (page = currentPage.value, size = pageSize.value) => {
  loading.value = true
  try {
    // Prepare search parameters
    const searchParams = {
      page,
      page_size: size
    }

    // Add search criteria if they exist
    if (searchForm.value.id?.trim()) {
      searchParams.id = parseInt(searchForm.value.id.trim())
    }
    if (searchForm.value.tokenId?.trim()) {
      searchParams.token_id = searchForm.value.tokenId.trim()
    }
    if (searchForm.value.email?.trim()) {
      searchParams.email = searchForm.value.email.trim()
    }
    if (searchForm.value.validUntilStart) {
      searchParams.valid_until_start = dayjs(searchForm.value.validUntilStart).format('YYYY-MM-DD')
    }
    if (searchForm.value.validUntilEnd) {
      searchParams.valid_until_end = dayjs(searchForm.value.validUntilEnd).format('YYYY-MM-DD')
    }
    if (searchForm.value.nextResetStart) {
      searchParams.next_reset_start = dayjs(searchForm.value.nextResetStart).format('YYYY-MM-DD')
    }
    if (searchForm.value.nextResetEnd) {
      searchParams.next_reset_end = dayjs(searchForm.value.nextResetEnd).format('YYYY-MM-DD')
    }
    if (searchForm.value.lines?.length > 0) {
      searchParams.lines = searchForm.value.lines
    }

    const { data } = await getSubscriptionList(searchParams)

    // Handle paginated response
    if (data.subscriptions && data.pagination) {
      // New paginated response format
      subscriptions.value = data.subscriptions
      currentPage.value = data.pagination.current_page
      pageSize.value = data.pagination.page_size
      totalItems.value = data.pagination.total_items
      totalPages.value = data.pagination.total_pages
    } else if (data?.subscriptions) {
      // Fallback for old response format (if backend doesn't support pagination yet)
      subscriptions.value = data.subscriptions
      totalItems.value = data.subscriptions.length
      totalPages.value = 1
    } else {
      subscriptions.value = []
      totalItems.value = 0
      totalPages.value = 1
    }
  } catch (error) {
    ElMessage.error(t('messages.error.failedToLoadSubscriptions'))
    subscriptions.value = []
    totalItems.value = 0
    totalPages.value = 1
  } finally {
    loading.value = false
  }
}

const searchServers = (query) => {
  // Determine which lines to use as the base for filtering
  // If no package is selected, use all available lines
  // If a package is selected, use availableAdditionalLines (lines not in package)
  const selectedPackageId = form.value.package_id || editForm.value.package_id
  const baseLines = selectedPackageId ? availableAdditionalLines.value : allAvailableLines.value
  
  if (query) {
    filteredDialogLines.value = baseLines.filter(line =>
      line.display_name.toLowerCase().includes(query.toLowerCase())
    )
  } else {
    filteredDialogLines.value = baseLines
  }
}

// Pagination handlers
const handlePageChange = (page) => {
  currentPage.value = page
  loadSubscriptions(page, pageSize.value)
}

const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // Reset to first page when changing page size
  loadSubscriptions(1, size)
}

// Search handlers
const handleSearchInput = () => {
  // Debounce search input to avoid too many API calls
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  searchTimeout.value = setTimeout(() => {
    handleSearch()
  }, 500)
}

const handleSearchChange = () => {
  // Immediate search for dropdown and date picker changes
  handleSearch()
}

const handleSearch = () => {
  currentPage.value = 1 // Reset to first page when searching
  loadSubscriptions(1, pageSize.value)
}

const handleClearSearch = () => {
  searchForm.value = {
    id: '',
    tokenId: '',
    email: '',
    validUntilStart: '',
    validUntilEnd: '',
    nextResetStart: '',
    nextResetEnd: '',
    lines: []
  }
  handleSearch()
}

// Handle edit package button click (from edit dialog)
const handleEditPackage = () => {
  const packageId = editForm.value.package_id
  if (packageId) {
    // Open package management page in a new tab
    const packageManagementUrl = '/package-management?edit=' + packageId
    window.open(packageManagementUrl, '_blank')
  }
}

// Handle edit package button click (from add dialog)
const handleEditPackageFromAdd = () => {
  const packageId = form.value.package_id
  if (packageId) {
    // Open package management page in a new tab
    const packageManagementUrl = '/package-management?edit=' + packageId
    window.open(packageManagementUrl, '_blank')
  }
}

// Handle add dialog open
const handleShowAddDialog = () => {
  showAddDialog.value = true
  // Initialize filtered lines for search
  searchServers('')
}

const toggleSearch = () => {
  searchExpanded.value = !searchExpanded.value
  saveSearchExpandedState()
}

// Filter lines for search dropdown (uses complete server list)
const filteredSearchLines = ref([])

const filterSearchLines = (query) => {
  if (query) {
    filteredSearchLines.value = allAvailableLines.value.filter(line =>
      line.display_name.toLowerCase().includes(query.toLowerCase())
    )
  } else {
    filteredSearchLines.value = allAvailableLines.value
  }
}

// Filter lines for dialog forms (uses complete server list)
const filteredDialogLines = ref([])

// Computed property to filter out package lines when a package is selected
const availableAdditionalLines = computed(() => {
  if (!allAvailableLines.value || allAvailableLines.value.length === 0) {
    return []
  }
  
  // If no package is selected, show all lines
  const selectedPackageId = form.value.package_id || editForm.value.package_id
  if (!selectedPackageId || !availablePackages.value) {
    return allAvailableLines.value
  }
  
  // Find the selected package
  const selectedPackage = availablePackages.value.find(pkg => pkg.id === selectedPackageId)
  if (!selectedPackage || !selectedPackage.lines) {
    return allAvailableLines.value
  }
  
  // Get package line IDs
  const packageLineIds = new Set(selectedPackage.lines.map(line => line.id))
  
  // Filter out package lines, only show additional lines
  return allAvailableLines.value.filter(line => !packageLineIds.has(line.id))
})



// Computed property for active filters count
const activeFiltersCount = computed(() => {
  let count = 0
  if (searchForm.value.id?.trim()) count++
  if (searchForm.value.tokenId?.trim()) count++
  if (searchForm.value.email?.trim()) count++
  if (searchForm.value.validUntilStart) count++
  if (searchForm.value.validUntilEnd) count++
  if (searchForm.value.nextResetStart) count++
  if (searchForm.value.nextResetEnd) count++
  if (searchForm.value.lines?.length > 0) count++
  return count
})

// Load search expanded state from localStorage on component mount
const loadSearchExpandedState = () => {
  const saved = localStorage.getItem('subscription-search-expanded')
  if (saved !== null) {
    searchExpanded.value = JSON.parse(saved)
  }
}

// Save search expanded state to localStorage
const saveSearchExpandedState = () => {
  localStorage.setItem('subscription-search-expanded', JSON.stringify(searchExpanded.value))
}

// Speed related functions
const updateUserSpeeds = async () => {
  try {
    const { data } = await getUserSpeeds()
    userSpeeds.value = data.user_speeds || {}
  } catch (error) {
    console.error('Failed to update user speeds:', error)
  }
}

const formatSpeed = (bytesPerSecond) => {
  if (!bytesPerSecond || bytesPerSecond < 0) return '0 B/s'
  
  const units = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  let value = bytesPerSecond
  let unitIndex = 0
  
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024
    unitIndex++
  }
  
  return `${value.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
}

const getUserUploadSpeed = (subscriptionId) => {
  const userSpeed = userSpeeds.value[subscriptionId]
  return userSpeed ? userSpeed.total_upload_speed : 0
}

const getUserDownloadSpeed = (subscriptionId) => {
  const userSpeed = userSpeeds.value[subscriptionId]
  return userSpeed ? userSpeed.total_download_speed : 0
}

const getLineUploadSpeed = (subscriptionId, lineId) => {
  const userSpeed = userSpeeds.value[subscriptionId]
  if (!userSpeed || !userSpeed.line_speeds) return 0
  
  const lineSpeed = userSpeed.line_speeds.find(line => line.line_id === lineId)
  return lineSpeed ? lineSpeed.upload_speed : 0
}

const getLineDownloadSpeed = (subscriptionId, lineId) => {
  const userSpeed = userSpeeds.value[subscriptionId]
  if (!userSpeed || !userSpeed.line_speeds) return 0
  
  const lineSpeed = userSpeed.line_speeds.find(line => line.line_id === lineId)
  return lineSpeed ? lineSpeed.download_speed : 0
}

// Helper function to format traffic scale multiplier
const formatTrafficScale = (scale) => {
  if (!scale || scale === 1.0) {
    return '1x'
  }
  return `${scale}x`
}

// Helper function to format line display name with traffic scale
const formatLineDisplayName = (line) => {
  const scale = line.traffic_scale || 1.0
  if (scale === 1.0) {
    return line.display_name
  }
  return `${line.display_name} (${scale}x)`
}

onMounted(() => {
  loadSearchExpandedState()
  loadSubscriptions()
  loadServerList()
  // Load complete server list for search dropdown
  loadAllServersForSearch()
  // Load available packages for selection
  loadAvailablePackages()
  // Start speed monitoring
  updateUserSpeeds()
  speedUpdateTimer = setInterval(updateUserSpeeds, 3000) // Update every 3 seconds
})

onUnmounted(() => {
  if (speedUpdateTimer) {
    clearInterval(speedUpdateTimer)
    speedUpdateTimer = null
  }
})
</script>

<style scoped>
.subscription-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: var(--theme-text-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.table-card {
  border-radius: 8px;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-light);
}

/* Mobile Table Wrapper */
.table-wrapper {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  max-width: 100%;
}

.line-count {
  cursor: pointer;
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-regular) !important;
  border: 1px solid var(--theme-border-base) !important;
}

.line-count:hover {
  background-color: var(--theme-fill-base) !important;
  border-color: var(--theme-primary) !important;
}

/* Dark mode line count styling */
:global(.dark) .line-count {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: 1px solid var(--theme-border-dark) !important;
}

:global(.dark) .line-count:hover {
  background-color: var(--theme-fill-darker) !important;
  border-color: var(--theme-primary) !important;
}

:deep(.line-popover) {
  padding: 0;
  background-color: var(--theme-bg-primary) !important;
  border: 1px solid var(--theme-border-base) !important;
  box-shadow: var(--theme-shadow-base) !important;
}

/* Dark mode popover styling */
:global(.dark) :deep(.line-popover) {
  background-color: var(--theme-bg-tertiary) !important;
  border: 1px solid var(--theme-border-dark) !important;
  box-shadow: var(--theme-shadow-dark) !important;
}

:global(.dark) :deep(.line-popover .el-popper__arrow::before) {
  background-color: var(--theme-bg-tertiary) !important;
  border: 1px solid var(--theme-border-dark) !important;
}

.line-list {
  width: 100%;
}

.line-list-header {
  padding: 8px 12px;
  font-weight: 600;
  border-bottom: 1px solid var(--theme-border-base);
  background-color: var(--theme-fill-light);
  color: var(--theme-text-primary);
  border-radius: 4px 4px 0 0;
}

.line-item {
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--theme-border-light);
  background-color: var(--theme-bg-primary);
  gap: 16px;
  min-height: 60px;
}

.line-item:hover {
  background-color: var(--theme-fill-extra-light);
}

.line-item:last-child {
  border-bottom: none;
}

.line-info {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.line-name {
  font-size: 14px;
  color: var(--theme-text-primary);
  font-weight: 600;
  min-width: 80px;
}

.line-status {
  display: flex;
  align-items: center;
}

.line-speed {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 11px;
  font-family: monospace;
  min-width: 85px;
  text-align: right;
}

.speed-item-small {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
}

.speed-icon-small {
  font-weight: bold;
  width: 8px;
  text-align: center;
  font-size: 10px;
}

.speed-item-small .speed-value-small {
  color: var(--theme-primary);
  font-weight: 600;
}

.speed-value-small {
  font-size: 10px;
  font-weight: 500;
  text-align: right;
  min-width: 50px;
}

/* Clean borderless table styling with performance optimizations */
:deep(.el-table) {
  border-radius: 8px;
  background-color: var(--theme-bg-primary);
  border: none;
}

/* Fixed row height for performance */
:deep(.el-table .el-table__row) {
  height: 60px !important;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0 !important;
  vertical-align: middle;
}

:deep(.el-table th) {
  font-weight: 600;
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-extra-light) !important;
  transition: none;
}

:deep(.el-table .el-table__empty-block) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-secondary) !important;
}

/* Dark mode specific table styling */
:global(.dark) :deep(.el-table th) {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-light) !important;
}

:global(.dark) :deep(.el-table .el-table__empty-block) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-secondary) !important;
}

:deep(.el-button.is-circle) {
  padding: 6px;
}

:deep(.el-card__body) {
  padding: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-select) {
  width: 100%;
}

.traffic-progress {
  padding: 0 10px;
}

.traffic-progress .progress-wrapper {
  background-color: var(--theme-fill-light);
  border: 1px solid var(--theme-border-light);
  border-radius: 6px;
  height: 26px;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.traffic-progress .progress-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  border-radius: 5px;
  box-shadow: inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

.traffic-progress .progress-bar.zero-progress {
  width: 0 !important;
}

.traffic-progress .progress-text {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme-text-regular);
  font-size: 12px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-weight: 500;
  white-space: nowrap;
  z-index: 2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Dark mode specific progress bar styling */
:global(.dark) .traffic-progress .progress-wrapper {
  background-color: var(--theme-fill-darker);
  border-color: var(--theme-border-dark);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

:global(.dark) .traffic-progress .progress-bar {
  background: linear-gradient(90deg, var(--theme-primary) 0%, var(--theme-primary-lighter) 100%);
  box-shadow: inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

:global(.dark) .traffic-progress .progress-text {
  color: var(--theme-text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.el-button {
  margin: 0 2px;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 0 20px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.page-size-selector {
  width: 120px;
}

.pagination-controls {
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }

  .pagination-info {
    order: 2;
  }

  .pagination-controls {
    order: 1;
  }

  /* Mobile Table Optimizations */
  .table-wrapper {
    margin: 0 -16px;
    border-radius: 0;
  }

  .table-card {
    margin: 0 -16px;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .subscription-table {
    min-width: 900px; /* Ensure horizontal scroll */
  }

  .el-table .el-table__row {
    height: 70px !important; /* Increase row height for mobile */
  }

  /* Mobile-friendly action buttons */
  .el-space {
    gap: 6px !important;
  }

  .el-space .el-button {
    width: 36px;
    height: 36px;
    padding: 0;
  }
}

@media (max-width: 480px) {
  .subscription-management-container {
    padding: 12px;
  }

  .table-wrapper {
    margin: 0 -12px;
  }

  .table-card {
    margin: 0 -12px;
  }

  .subscription-table {
    min-width: 1000px; /* Increase min-width for smaller screens */
  }

  .el-table .el-table__row {
    height: 80px !important; /* Further increase row height */
  }

  /* Larger touch targets */
  .el-space .el-button {
    width: 40px;
    height: 40px;
  }
}

/* Search Toggle Button Styles - optimized for performance */
.search-toggle-btn {
  position: relative;
  border-radius: 6px;
  font-weight: 500;
}

.search-toggle-text {
  margin-left: 4px;
  margin-right: 4px;
}

.filter-badge {
  margin-left: 8px;
}

.filter-badge :deep(.el-badge__content) {
  font-size: 11px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  border-radius: 8px;
}

/* Search Form Styles */
.search-container {
  margin-bottom: 20px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.search-form .el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* Dark mode compatibility for search form */
:root.dark .search-container {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
}



/* Token ID clickable cell styling */
.token-id-cell {
  cursor: pointer;
  color: var(--theme-primary);
  padding: 2px 4px;
  border-radius: 4px;
  display: inline-block;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  font-weight: 500;
}

.token-id-cell:hover {
  background-color: var(--theme-primary-light-9);
  color: var(--theme-primary-dark-2);
}

.token-id-cell:active {
  background-color: var(--theme-primary-light-8);
}

/* Dark mode specific token ID styling */
:global(.dark) .token-id-cell {
  color: var(--theme-primary-light-3);
}

:global(.dark) .token-id-cell:hover {
  background-color: var(--theme-primary-dark-8);
  color: var(--theme-primary-light-5);
}

/* Responsive design for search form */
@media (max-width: 768px) {
  .search-toggle-btn {
    width: 100%;
    justify-content: center;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form .el-form-item {
    width: 100%;
  }

  .search-form .el-input,
  .search-form .el-select,
  .search-form .el-date-picker {
    width: 100% !important;
  }
}

.field-tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
  line-height: 1.4;
}

/* Speed display styles */
.speed-display {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  font-family: monospace;
}

.speed-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.speed-icon {
  font-weight: bold;
  width: 12px;
  text-align: center;
}

.speed-value {
  min-width: 60px;
  text-align: center;
  font-weight: 600;
  color: var(--theme-primary);
}

/* Package Lines Styling */
.package-lines-section {
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--el-fill-color-extra-light);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-lighter);
}

.package-lines-header {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.package-lines-header::before {
  content: "📦";
  margin-right: 6px;
}

.package-lines-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.package-line-tag {
  position: relative;
  padding-right: 8px;
}

.package-line-badge {
  font-size: 10px;
  background-color: var(--el-color-success-light-7);
  color: var(--el-color-success);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  font-weight: 500;
}

.additional-lines-section {
  margin-top: 8px;
}

.additional-lines-header {
  font-size: 13px;
  font-weight: 500;
  color: var(--el-text-color-regular);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.additional-lines-header::before {
  content: "➕";
  margin-right: 6px;
}

/* Dark mode package lines styling */
:global(.dark) .package-lines-section {
  background-color: var(--el-fill-color-darker);
  border-color: var(--el-border-color-dark);
}

:global(.dark) .package-line-badge {
  background-color: var(--el-color-success-dark-2);
  color: var(--el-color-success-light-3);
}

/* Package-managed field styling */
.field-disabled-tip {
  font-size: 12px;
  color: var(--el-color-warning);
  margin-top: 4px;
  font-style: italic;
}

.package-config-actions {
  margin-top: 12px;
  display: flex;
  justify-content: flex-start;
}

.edit-package-btn {
  margin-left: 0;
}

/* Disabled field styling */
:deep(.el-input-number.is-disabled) {
  opacity: 0.6;
}

:deep(.el-input-number.is-disabled .el-input__inner) {
  background-color: var(--el-disabled-bg-color);
  color: var(--el-disabled-text-color);
}

</style>
