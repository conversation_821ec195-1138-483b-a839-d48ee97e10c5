<template>
  <el-card v-loading="isLoading">
    <template #header>
      <div class="card-header">
        <h3>{{ $t('pages.subscription.title') }}</h3>
        <div class="header-actions">
          <el-button type="success" :icon="TrendCharts" @click="showSpeedDetail">
            {{ $t('pages.subscription.speedDetails') }}
          </el-button>
          <el-button type="primary" :icon="Refresh" @click="refreshData">
            {{ $t('pages.subscription.refresh') }}
          </el-button>
        </div>
      </div>
    </template>
    
    <el-descriptions :column="2" border v-if="!isLoading && subscriptionData">
      <el-descriptions-item :label="$t('pages.subscription.validUntil')">
        {{ subscriptionData.valid_until ? formatDate(subscriptionData.valid_until) : 'N/A' }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.subscription.nextReset')">
        {{ subscriptionData.next_reset ? formatDate(subscriptionData.next_reset) : 'N/A' }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.subscription.bandwidth')">
        {{ subscriptionData.bandwidth ? `${subscriptionData.bandwidth} Mbps` : $t('pages.subscription.unlimited') }}
      </el-descriptions-item>
      <el-descriptions-item :label="$t('pages.subscription.trafficUsage')">
        <div class="traffic-info">
          <el-progress
            :percentage="trafficPercentage"
            :status="trafficStatus"
          />
          <div class="traffic-details">
            <span>{{ formatTraffic(subscriptionData.traffic_used) }} / {{ formatTraffic(subscriptionData.traffic_total * 1024 * 1024 * 1024) }}</span>
            <span class="traffic-percentage">{{ trafficPercentage.toFixed(1) }}%</span>
          </div>
        </div>
      </el-descriptions-item>
    </el-descriptions>
    <div v-else-if="!isLoading" class="no-data">
      <el-empty :description="$t('pages.subscription.noDataAvailable')" />
    </div>
  </el-card>
  
  <!-- 服务器状态卡片 -->
  <el-card v-if="!isLoading && subscriptionData?.lines?.length" style="margin-top: 16px">
    <template #header>
      <div class="card-header">
        <h3>{{ $t('pages.subscription.serverStatus') }}</h3>
      </div>
    </template>
    
    <div class="server-grid">
      <ServerStatusCard
        v-for="line in subscriptionData.lines"
        :key="line.id"
        :name="line.display_name"
        :down-speed="networkSpeeds[line.id]?.down_speed || 0"
        :up-speed="networkSpeeds[line.id]?.up_speed || 0"
        :total-down="networkSpeeds[line.id]?.total_rx || 0"
        :total-up="networkSpeeds[line.id]?.total_tx || 0"
        :cpu-usage="systemStats[line.id]?.cpu_usage || 0"
        :memory-total="systemStats[line.id]?.memory_total || 0"
        :memory-used="systemStats[line.id]?.memory_used || 0"
        :uptime="systemStats[line.id]?.uptime || 0"
        :tcp-connections="systemStats[line.id]?.tcp_connections || 0"
        :udp-connections="systemStats[line.id]?.udp_connections || 0"
        :is-online="line.is_online"
        :traffic-scale="line.traffic_scale"
      />
    </div>
  </el-card>
  
  <!-- User Speed Detail Dialog -->
  <UserSpeedDetailDialog
    v-model:visible="showSpeedDetailDialog"
    @close="showSpeedDetailDialog = false"
  />
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import { storeToRefs } from 'pinia'
import { getLineStats } from '../api'
import { Refresh, TrendCharts } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import ServerStatusCard from '../components/ServerStatusCard.vue'
import UserSpeedDetailDialog from '../components/UserSpeedDetailDialog.vue'

const authStore = useAuthStore()
const { subscriptionData } = storeToRefs(authStore)

const isLoading = ref(false)
const networkSpeeds = ref({})
const systemStats = ref({})
const showSpeedDetailDialog = ref(false)
let isComponentMounted = true
let speedUpdateTimer = null

const refreshData = async () => {
  isLoading.value = true
  try {
    await authStore.fetchSubscriptionData()
  } finally {
    isLoading.value = false
  }
}

const showSpeedDetail = () => {
  showSpeedDetailDialog.value = true
}

const trafficPercentage = computed(() => {
  if (!subscriptionData.value?.traffic_total || !subscriptionData.value?.traffic_used) {
    return 0
  }
  return (subscriptionData.value.traffic_used / (subscriptionData.value.traffic_total * 1024 * 1024 * 1024)) * 100
})

const trafficStatus = computed(() => {
  if (trafficPercentage.value > 90) {
    return 'exception'
  } else if (trafficPercentage.value > 70) {
    return 'warning'
  }
  return 'success'
})

const formatDate = (date) => {
  return new Date(date).toLocaleString()
}

const formatTraffic = (bytes) => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`
}

const formatSpeed = (bytesPerSec) => {
  if (!bytesPerSec && bytesPerSec !== 0) return '0 B/s'
  
  const units = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  let value = bytesPerSec
  let unitIndex = 0
  
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024
    unitIndex++
  }
  
  return `${value.toFixed(2)} ${units[unitIndex]}`
}

const getLineSpeed = (netcardSpeed, lineId, type) => {
  if (!netcardSpeed || !netcardSpeed[lineId]) return 0
  return netcardSpeed[lineId][type] || 0
}

const updateNetworkSpeeds = async () => {
  if (!isComponentMounted || !subscriptionData.value?.lines) return
  
  try {
    const { data } = await getLineStats()
    if (isComponentMounted) {
      const mappedSpeeds = {}
      const mappedStats = {}
      
      // 创建新的线路数组以保持响应性
      const updatedLines = subscriptionData.value.lines.map(line => {
        const lineId = line.id.toString()

        // 如果在返回数据中存在该线路，则使用返回的数据，否则设置为离线状态
        if (data.netcard_speed?.[lineId]) {
          const stats = data.netcard_speed[lineId]
          mappedSpeeds[lineId] = {
            down_speed: stats.rx,
            up_speed: stats.tx,
            total_rx: stats.total_rx,
            total_tx: stats.total_tx
          }
        } else {
          mappedSpeeds[lineId] = {
            down_speed: 0,
            up_speed: 0,
            total_rx: 0,
            total_tx: 0
          }
        }

        // 同样处理系统状态数据
        if (data.system_stats?.[lineId]) {
          mappedStats[lineId] = data.system_stats[lineId]
        } else {
          mappedStats[lineId] = {
            cpu_usage: 0,
            memory_total: 0,
            memory_used: 0,
            uptime: 0,
            tcp_connections: 0,
            udp_connections: 0
          }
        }

        // 返回线路对象，保持原有的 is_online 状态（来自后端的 Redis 状态）
        return {
          ...line
          // 不再修改 is_online 属性，使用后端提供的权威状态
        }
      })

      // 更新 store 中的数据
      subscriptionData.value = {
        ...subscriptionData.value,
        lines: updatedLines
      }
      
      networkSpeeds.value = mappedSpeeds
      systemStats.value = mappedStats
    }
  } catch (error) {
    console.error('Failed to update stats:', error)
  }
}

onMounted(() => {
  isComponentMounted = true
  updateNetworkSpeeds() // Initial update
  speedUpdateTimer = setInterval(updateNetworkSpeeds, 3000) // Update every 3 seconds
})

onUnmounted(() => {
  isComponentMounted = false
  if (speedUpdateTimer) {
    clearInterval(speedUpdateTimer)
    speedUpdateTimer = null
  }
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.traffic-info {
  width: 100%;
}

.traffic-details {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  color: #606266;
  font-size: 0.9em;
}

.server-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin: 0;
  padding: 0;
}
</style>