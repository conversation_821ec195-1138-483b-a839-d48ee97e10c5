<template>
  <div class="login-container">
    <div class="theme-toggle-wrapper">
      <LanguageSwitcher variant="button" :show-text="false" />
      <ThemeToggle variant="button" />
    </div>
    <el-card class="login-card">
      <template #header>
        <div class="login-header">
          <img src="/icon.png" alt="ZFC Logo" class="login-logo" />
          <h2>{{ $t('brandName') }}</h2>
        </div>
      </template>
      <el-form @submit.prevent="handleLogin">
        <el-form-item>
          <el-input
            v-model="token"
:placeholder="$t('login.pleaseEnterPassword')"
            :prefix-icon="Key"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" native-type="submit" :loading="loading" block>
{{ $t('login.loginButton') }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { Key } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import ThemeToggle from '../components/ThemeToggle.vue'
import LanguageSwitcher from '../components/LanguageSwitcher.vue'

const router = useRouter()
const authStore = useAuthStore()
const { t } = useI18n()

const token = ref('')
const loading = ref(false)

const handleLogin = async () => {
  if (!token.value) {
    ElMessage.warning(t('login.pleaseEnterPassword'))
    return
  }

  loading.value = true
  try {
    await authStore.login(token.value)
    ElMessage.success(t('login.loginSuccessful'))
    
    // Check if subscription is expired after login
    if (authStore.isSubscriptionExpired) {
      ElMessage.warning(t('messages.info.subscriptionExpiredWarning'))
    }
    
    router.push('/dashboard')
  } catch (error) {
    ElMessage.error(error.response?.data?.message || t('login.loginFailed'))
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--theme-bg-secondary);
  position: relative;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-base);
}

.login-card :deep(.el-card__header) {
  text-align: center;
  background-color: var(--theme-card-bg);
  border-bottom: 1px solid var(--theme-border-base);
}

.login-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.login-logo {
  width: 36px;
  height: 36px;
  object-fit: contain;
  transition: var(--theme-transition);
}

.login-card :deep(.el-card__header h2) {
  color: var(--theme-text-primary);
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.login-card :deep(.el-card__body) {
  background-color: var(--theme-card-bg);
}

/* Theme toggle in login page */
.theme-toggle-wrapper {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  display: flex;
  gap: 8px;
}

/* Dark theme logo adjustments */
:global(.dark) .login-logo {
  filter: brightness(1.1) contrast(1.1);
}

/* Responsive design for login */
@media (max-width: 768px) {
  .login-card {
    margin: 0 16px;
    border-radius: 12px;
  }

  .login-header h2 {
    font-size: 20px;
  }

  /* Mobile-friendly form inputs */
  .login-card :deep(.el-input__inner) {
    min-height: 48px;
    padding: 14px 16px;
    font-size: 16px;
  }

  .login-card :deep(.el-button) {
    min-height: 48px;
    padding: 14px 24px;
    font-size: 16px;
    font-weight: 600;
  }

  .login-card :deep(.el-form-item) {
    margin-bottom: 24px;
  }

  .login-card :deep(.el-form-item__label) {
    font-size: 16px;
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .login-header {
    gap: 8px;
  }

  .login-logo {
    width: 32px;
    height: 32px;
  }

  .login-card :deep(.el-card__header h2) {
    font-size: 18px;
  }

  .login-card {
    margin: 0 12px;
    border-radius: 8px;
  }

  .theme-toggle-wrapper {
    top: 16px;
    right: 16px;
  }

  /* Enhanced mobile form inputs */
  .login-card :deep(.el-input__inner) {
    min-height: 52px;
    padding: 16px;
    font-size: 16px;
  }

  .login-card :deep(.el-button) {
    min-height: 52px;
    padding: 16px 28px;
    font-size: 16px;
  }

  .login-card :deep(.el-form-item) {
    margin-bottom: 28px;
  }
}
</style>