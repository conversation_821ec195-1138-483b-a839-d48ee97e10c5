<template>
  <div class="license-renewal">
    <div class="header">
      <h2>{{ t('pages.license.title') }}</h2>
      <el-button 
        type="primary" 
        :icon="Refresh" 
        @click="refreshLicenseStatus"
        :loading="refreshing"
      >
        {{ t('pages.license.refreshStatus') }}
      </el-button>
    </div>

    <!-- License Status Card -->
    <el-card class="license-status-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <h3>{{ t('pages.license.currentLicenseStatus') }}</h3>
          <el-tag 
            :type="licenseStatusType" 
            size="large"
            :effect="isExpired ? 'dark' : 'light'"
          >
            {{ licenseStatusText }}
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="2" border v-if="softwareLicenseData">
        <el-descriptions-item :label="t('pages.license.licenseExpires')">
          <span :class="{ 'text-danger': isExpired, 'text-warning': isExpiringSoon }">
            {{ formatDate(softwareLicenseData.valid_until) }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item :label="t('pages.license.daysRemaining')">
          <span :class="{ 'text-danger': isExpired, 'text-warning': isExpiringSoon }">
            {{ daysRemaining }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item :label="t('pages.license.lastRenewal')">
          {{ lastRenewalDate || 'N/A' }}
        </el-descriptions-item>
        <el-descriptions-item :label="t('pages.license.licenseId')">
          {{ softwareLicenseData.license_id || 'N/A' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- Expiration Warning -->
      <el-alert
        v-if="isExpired"
        :title="t('pages.license.licenseExpired')"
        type="error"
        :description="t('pages.license.licenseExpiredDescription')"
        show-icon
        :closable="false"
        class="mt-4"
      />
      <el-alert
        v-else-if="isExpiringSoon"
        :title="t('pages.license.licenseExpiringSoon')"
        type="warning"
        :description="t('pages.license.licenseExpiringSoonDescription', { days: daysRemaining })"
        show-icon
        :closable="false"
        class="mt-4"
      />
    </el-card>

    <!-- Renewal Code Input Card -->
    <el-card class="renewal-card">
      <template #header>
        <h3>{{ t('pages.license.applyRenewalCode') }}</h3>
      </template>

      <el-form
        ref="renewalForm"
        :model="renewalData"
        :rules="renewalRules"
        label-width="120px"
        @submit.prevent="handleRenewal"
      >
        <el-form-item :label="t('forms.labels.renewalCode')" prop="renewalCode">
          <el-input
            v-model="renewalData.renewalCode"
            :placeholder="t('forms.placeholders.enterRenewalCode')"
            maxlength="100"
            show-word-limit
            :disabled="applying"
          />
          <div class="form-hint">
            {{ t('pages.license.renewalCodeHint') }}
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="handleRenewal"
            :loading="applying"
            :disabled="!renewalData.renewalCode.trim()"
          >
            {{ t('actions.applyRenewalCode') }}
          </el-button>
          <el-button @click="resetForm">
            {{ t('actions.clear') }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- Renewal History Card -->
    <el-card class="history-card" v-if="renewalHistory.length > 0">
      <template #header>
        <h3>{{ t('pages.license.renewalHistory') }}</h3>
      </template>

      <el-table :data="renewalHistory" style="width: 100%">
        <el-table-column prop="date" :label="t('forms.labels.applicationTime')" width="200">
          <template #default="scope">
            {{ formatDate(scope.row.date) }}
          </template>
        </el-table-column>
        <el-table-column prop="code_id" :label="t('forms.labels.codeId')" width="280" />
        <el-table-column prop="extended_days" :label="t('forms.labels.extendedDays')" width="140" />
        <el-table-column prop="status" :label="t('forms.labels.status')">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '../stores/auth'
import { useSubscriptionStore } from '../stores/subscription'
import { storeToRefs } from 'pinia'
import { Refresh } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { applyRenewalCode, getRenewalHistory, getSoftwareLicenseInfo } from '../api'

const { t } = useI18n()

const authStore = useAuthStore()
const subscriptionStore = useSubscriptionStore()

// State
const loading = ref(false)
const refreshing = ref(false)
const applying = ref(false)
const renewalHistory = ref([])
const renewalForm = ref(null)
const lastRenewalDate = ref(null)
const softwareLicenseData = ref(null)

// Form data
const renewalData = reactive({
  renewalCode: ''
})

// Form validation rules
const renewalRules = {
  renewalCode: [
    { required: true, message: () => t('forms.validation.pleaseEnterRenewalCode'), trigger: 'blur' },
    { min: 5, message: () => t('forms.validation.renewalCodeMinLength'), trigger: 'blur' }
  ]
}

// Computed properties
const isExpired = computed(() => {
  if (!softwareLicenseData.value?.valid_until) return false
  return new Date() > new Date(softwareLicenseData.value.valid_until)
})

const isExpiringSoon = computed(() => {
  if (isExpired.value) return false
  return daysRemaining.value <= 15
})

const daysRemaining = computed(() => {
  if (!softwareLicenseData.value?.valid_until) return 0
  const now = new Date()
  const expiry = new Date(softwareLicenseData.value.valid_until)
  const diffTime = expiry.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
})

const licenseStatusType = computed(() => {
  if (isExpired.value) return 'danger'
  if (isExpiringSoon.value) return 'warning'
  return 'success'
})

const licenseStatusText = computed(() => {
  if (isExpired.value) return t('status.expired')
  if (isExpiringSoon.value) return t('status.expiringSoon')
  return t('status.active')
})

// Methods
const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleString()
}

const refreshLicenseStatus = async () => {
  refreshing.value = true
  try {
    await loadSoftwareLicenseInfo()
    await loadRenewalHistory()
    ElMessage.success(t('messages.success.licenseRefreshed'))
  } catch (error) {
    ElMessage.error(t('messages.error.failedToRefreshLicense'))
  } finally {
    refreshing.value = false
  }
}

const handleRenewal = async () => {
  if (!renewalForm.value) return
  
  await renewalForm.value.validate(async (valid) => {
    if (!valid) return
    
    try {
      await ElMessageBox.confirm(
        t('pages.license.confirmRenewal'),
        t('pages.license.confirmRenewalTitle'),
        {
          confirmButtonText: t('pages.license.applyCode'),
          cancelButtonText: t('common.cancel'),
          type: 'warning'
        }
      )
      
      applying.value = true
      
      await applyRenewalCode(renewalData.renewalCode)
      
      ElMessage.success(t('messages.success.renewalApplied'))
      
      // Refresh data
      await refreshLicenseStatus()
      
      // Reset form
      resetForm()
      
    } catch (error) {
      if (error === 'cancel') return
      ElMessage.error(error.message || t('messages.error.failedToApplyRenewal'))
    } finally {
      applying.value = false
    }
  })
}

const resetForm = () => {
  renewalData.renewalCode = ''
  if (renewalForm.value) {
    renewalForm.value.resetFields()
  }
}

const loadSoftwareLicenseInfo = async () => {
  try {
    const { data } = await getSoftwareLicenseInfo()
    softwareLicenseData.value = data
  } catch (error) {
    console.error('Failed to load software license info:', error)
    throw error
  }
}

const loadRenewalHistory = async () => {
  try {
    const { data } = await getRenewalHistory()
    // Backend returns { history: [...] }
    renewalHistory.value = data.history || []
    
    // Set last renewal date
    if (renewalHistory.value.length > 0) {
      lastRenewalDate.value = renewalHistory.value[0].date
    }
  } catch (error) {
    console.error('Failed to load renewal history:', error)
  }
}

// Lifecycle
onMounted(async () => {
  loading.value = true
  try {
    await loadSoftwareLicenseInfo()
    await loadRenewalHistory()
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.license-renewal {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
}

.license-status-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.renewal-card {
  margin-bottom: 20px;
}

.history-card {
  margin-bottom: 20px;
}

.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.mt-4 {
  margin-top: 16px;
}

@media (max-width: 768px) {
  .license-renewal {
    padding: 10px;
  }
  
  .header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
}
</style>