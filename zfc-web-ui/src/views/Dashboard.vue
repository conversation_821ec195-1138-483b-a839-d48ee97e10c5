<template>
  <el-container class="dashboard">
    <el-header>
      <div class="header-content">
        <div class="header-left">
          <el-button 
            class="mobile-menu-toggle"
            @click="toggleMobileMenu"
            :icon="Menu"
            type="text"
            v-if="isMobile"
          />
          <div class="logo-container" @click="navigateToHome">
            <img src="/icon.png" alt="ZFC Logo" class="app-logo" />
          </div>
          <h2 class="header-title">{{ $t('dashboard.title') }}</h2>
        </div>
        <div class="header-actions">
          <LanguageSwitcher variant="button" :show-text="!isMobile" />
          <ThemeToggle variant="button" />
          <el-button 
            class="logout-btn"
            type="danger" 
            @click="handleLogout" 
            :icon="SwitchButton"
            :size="isMobile ? 'small' : 'default'"
          >
            <span v-if="!isMobile">{{ $t('common.logout') }}</span>
          </el-button>
        </div>
      </div>
    </el-header>
    
    <el-container>
      <!-- Mobile Navigation Drawer -->
      <el-drawer
        v-if="isMobile"
        v-model="mobileMenuOpen"
        :with-header="false"
        direction="ltr"
        size="280px"
        class="mobile-nav-drawer"
      >
        <div class="mobile-nav-header">
          <div class="mobile-nav-logo">
            <img src="/icon.png" alt="ZFC Logo" class="nav-logo" />
            <span class="nav-title">{{ $t('dashboard.title') }}</span>
          </div>
        </div>
        <el-menu
          class="mobile-nav-menu"
          :router="true"
          :default-active="$route.path"
          @select="handleMobileMenuSelect"
        >
          <el-menu-item index="/dashboard">
            <el-icon><Ticket /></el-icon>
            <span>{{ $t('navigation.subscription') }}</span>
          </el-menu-item>
          <el-menu-item index="/dashboard/ports">
            <el-icon><Connection /></el-icon>
            <span>{{ $t('navigation.ports') }}</span>
          </el-menu-item>
          <el-menu-item v-if="subscriptionStore.allowForwardEndpoint" index="/dashboard/forward-endpoints">
            <el-icon><Position /></el-icon>
            <span>{{ $t('navigation.endpoints') }}</span>
          </el-menu-item>
          <template v-if="subscriptionStore.isAdmin">
            <el-menu-item index="/dashboard/subscription-management">
              <el-icon><Setting /></el-icon>
              <span>{{ $t('navigation.subscriptionManagement') }}</span>
            </el-menu-item>
            <el-menu-item index="/dashboard/package-management">
              <el-icon><Box /></el-icon>
              <span>{{ $t('navigation.packageManagement') }}</span>
            </el-menu-item>
            <el-menu-item index="/dashboard/server-management">
              <el-icon><Monitor /></el-icon>
              <span>{{ $t('navigation.servers') }}</span>
            </el-menu-item>
            <el-menu-item index="/dashboard/license-renewal">
              <el-icon><Key /></el-icon>
              <span>{{ $t('navigation.licenseRenewal') }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-drawer>
      
      <!-- Desktop Sidebar -->
      <el-menu
        v-if="!isMobile"
        class="sidebar"
        :router="true"
        :default-active="$route.path"
      >
        <el-menu-item index="/dashboard">
          <el-icon><Ticket /></el-icon>
          <span>{{ $t('navigation.subscription') }}</span>
        </el-menu-item>
        <el-menu-item index="/dashboard/ports">
          <el-icon><Connection /></el-icon>
          <span>{{ $t('navigation.ports') }}</span>
        </el-menu-item>
        <el-menu-item v-if="subscriptionStore.allowForwardEndpoint" index="/dashboard/forward-endpoints">
          <el-icon><Position /></el-icon>
          <span>{{ $t('navigation.endpoints') }}</span>
        </el-menu-item>
        <template v-if="subscriptionStore.isAdmin">
          <el-menu-item index="/dashboard/subscription-management">
            <el-icon><Setting /></el-icon>
            <span>{{ $t('navigation.subscriptionManagement') }}</span>
          </el-menu-item>
          <el-menu-item index="/dashboard/package-management">
            <el-icon><Box /></el-icon>
            <span>{{ $t('navigation.packageManagement') }}</span>
          </el-menu-item>
          <el-menu-item index="/dashboard/server-management">
            <el-icon><Monitor /></el-icon>
            <span>{{ $t('navigation.servers') }}</span>
          </el-menu-item>
          <el-menu-item index="/dashboard/license-renewal">
            <el-icon><Key /></el-icon>
            <span>{{ $t('navigation.licenseRenewal') }}</span>
          </el-menu-item>
        </template>
      </el-menu>
      
      <el-main>
        <!-- Subscription expired warning banner -->
        <el-alert
          v-if="authStore.isSubscriptionExpired"
          :title="$t('pages.license.licenseExpired')"
          type="warning"
          :description="$t('messages.info.subscriptionExpiredWarning')"
          show-icon
          :closable="false"
          class="expired-warning-banner"
        />
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useSubscriptionStore } from '../stores/subscription'
import { SwitchButton, Ticket, Connection, Position, Setting, Monitor, Menu, Key, Box } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted, watch, ref, computed, onUnmounted } from 'vue'
import ThemeToggle from '../components/ThemeToggle.vue'
import LanguageSwitcher from '../components/LanguageSwitcher.vue'

const router = useRouter()
const authStore = useAuthStore()
const subscriptionStore = useSubscriptionStore()

// Mobile responsive state
const windowWidth = ref(window.innerWidth)
const mobileMenuOpen = ref(false)

const isMobile = computed(() => windowWidth.value < 768)

const updateWindowWidth = () => {
  windowWidth.value = window.innerWidth
  // Close mobile menu when switching to desktop
  if (!isMobile.value) {
    mobileMenuOpen.value = false
  }
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const handleMobileMenuSelect = () => {
  mobileMenuOpen.value = false
}

// Add resize listener
onMounted(() => {
  window.addEventListener('resize', updateWindowWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateWindowWidth)
})

// Watch for changes in admin status
watch(() => subscriptionStore.isAdmin, (isAdmin) => {
  // If user is not admin and tries to access admin pages, redirect to dashboard
  const currentPath = router.currentRoute.value.path
  if (!isAdmin && (
    currentPath.includes('subscription-management') || 
    currentPath.includes('server-management') ||
    currentPath.includes('package-management')
  )) {
    router.push('/dashboard')
    ElMessage.warning('You do not have permission to access this page')
  }
})

const handleLogout = () => {
  authStore.logout()
  subscriptionStore.clearData()
  ElMessage.success('Logged out successfully')
  router.push('/')
}

const navigateToHome = () => {
  router.push('/dashboard')
}

// Fetch subscription data when the dashboard mounts
onMounted(async () => {
  if (authStore.isAuthenticated) {
    await subscriptionStore.fetchData(true)
  }
})

// Watch for auth state changes
watch(() => authStore.isAuthenticated, (isAuthenticated) => {
  if (isAuthenticated) {
    subscriptionStore.fetchData()
  } else {
    subscriptionStore.clearData()
  }
})
</script>

<style scoped>
.dashboard {
  height: 100vh;
  background-color: var(--theme-bg-primary);
}

.el-header {
  background-color: var(--theme-header-bg);
  border-bottom: 1px solid var(--theme-border-base);
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px;
  border-radius: var(--border-radius-base);
  transition: var(--theme-transition);
}

.logo-container:hover {
  background-color: var(--theme-fill-light);
  transform: scale(1.05);
}

.logo-container:active {
  transform: scale(0.98);
}

.app-logo {
  width: 32px;
  height: 32px;
  object-fit: contain;
  transition: var(--theme-transition);
}

.header-content h2 {
  color: var(--theme-text-primary);
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar {
  width: 220px;
  height: 100%;
  border-right: 1px solid var(--theme-border-base);
  background-color: var(--theme-sidebar-bg);
}

.el-main {
  padding: 20px;
  background-color: var(--theme-bg-secondary);
}

.expired-warning-banner {
  margin-bottom: 20px;
}

.el-menu-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 12px;
}

.el-icon {
  margin-right: 8px;
}

/* Dark theme specific adjustments */
:global(.dark) .sidebar :deep(.el-menu) {
  background-color: var(--theme-sidebar-bg);
  border-right: 1px solid var(--theme-border-base);
}

:global(.dark) .sidebar :deep(.el-menu-item) {
  color: var(--theme-text-regular);
}

:global(.dark) .sidebar :deep(.el-menu-item:hover) {
  background-color: var(--theme-fill-light);
}

:global(.dark) .sidebar :deep(.el-menu-item.is-active) {
  background-color: var(--theme-primary);
  color: #ffffff;
}

/* Dark theme logo adjustments */
:global(.dark) .logo-container:hover {
  background-color: var(--theme-fill-dark);
}

:global(.dark) .app-logo {
  filter: brightness(1.1) contrast(1.1);
}

/* Mobile Navigation Styles */
.mobile-menu-toggle {
  display: none;
  font-size: 20px;
  padding: 8px;
  color: var(--theme-text-primary);
}

.mobile-menu-toggle:hover {
  background-color: var(--theme-fill-light);
}

.mobile-nav-drawer {
  --el-drawer-bg-color: var(--theme-sidebar-bg);
}

.mobile-nav-header {
  padding: 20px 16px;
  border-bottom: 1px solid var(--theme-border-base);
  background-color: var(--theme-header-bg);
}

.mobile-nav-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-logo {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.nav-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-text-primary);
}

.mobile-nav-menu {
  border: none;
  background-color: transparent;
}

.mobile-nav-menu .el-menu-item {
  padding: 16px 20px;
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.mobile-nav-menu .el-menu-item:hover {
  background-color: var(--theme-fill-light);
}

.mobile-nav-menu .el-menu-item.is-active {
  background-color: var(--theme-primary-light-9);
  color: var(--theme-primary);
}

/* Responsive design */
@media (max-width: 768px) {
  .header-left {
    gap: 12px;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .header-title {
    font-size: 16px;
    display: none;
  }

  .app-logo {
    width: 28px;
    height: 28px;
  }

  .header-actions {
    gap: 8px;
  }

  .logout-btn span {
    display: none;
  }

  .sidebar {
    display: none;
  }

  .el-main {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .el-header {
    padding: 0 16px;
  }

  .header-left {
    gap: 8px;
  }

  .header-title {
    display: none;
  }

  .app-logo {
    width: 24px;
    height: 24px;
  }

  .el-main {
    padding: 12px;
  }

  .mobile-nav-drawer {
    --el-drawer-size: 260px;
  }
}
</style>