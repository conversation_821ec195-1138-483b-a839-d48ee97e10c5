{"dialogs": {"userDetail": {"title": "User Details", "basicInfo": "Basic Information", "historicalSpeed": "Historical Speed Data", "realtimeSpeed": "Real-time Line Speeds", "trafficUsage": "Traffic Usage", "trafficConsumption": "Daily Traffic Consumption", "uploadSpeed": "Upload Speed", "downloadSpeed": "Download Speed", "totalSpeed": "Total Speed", "clientIPCount": "Client IP Count", "connectionCount": "Connection Count", "clientIPs": "Client IPs", "connections": "Connections", "noData": "No historical data available for the selected time range", "validUntil": "<PERSON>id <PERSON>", "nextReset": "Next Reset", "bandwidth": "Bandwidth", "unlimited": "Unlimited", "portDetails": "Port Forwarding Details", "showPorts": "Show Port Details", "hidePorts": "Hide Port Details", "noPorts": "No port forwarding configured", "failedToLoadPorts": "Failed to load port forwarding data"}, "userSpeedDetail": {"title": "My Port Speed Statistics", "historicalData": "My Historical Speed Data", "realtimeSpeed": "Real-time Line Speeds", "uploadSpeed": "Upload Speed", "downloadSpeed": "Download Speed", "totalSpeed": "Total Speed", "trafficConsumption": "Daily Traffic Consumption", "noData": "No historical data for the selected time range", "selectLines": "Select Lines", "selectAll": "Select All", "linePrefix": "Line", "showingLines": "Showing {count} lines with speed data, {total} total lines available", "failedToLoadData": "Failed to load data", "failedToLoadHistorical": "Failed to load historical speed data", "failedToLoadRealtime": "Failed to load real-time speed data", "failedToGetSubscription": "Unable to get user subscription information", "failedToFetchSubscription": "Failed to fetch subscription information"}}, "common": {"loading": "Loading...", "logout": "Logout", "confirm": "Confirm", "cancel": "Cancel", "ok": "OK", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "refresh": "Refresh", "close": "Close", "back": "Back", "next": "Next", "submit": "Submit", "reset": "Reset", "language": "Language", "warning": "Warning", "to": "to", "create": "Create", "days": "days", "none": "None"}, "dashboard": {"title": "Port Forwarding Management", "welcome": "Welcome", "overview": "Overview", "statistics": "Statistics"}, "navigation": {"dashboard": "Dashboard", "servers": "Server Management", "ports": "Port Management", "endpoints": "Forward Endpoints", "subscription": "Subscription", "subscriptionManagement": "Subscription Management", "licenseRenewal": "License Renewal"}, "login": {"title": "<PERSON><PERSON>", "username": "Username", "password": "Password", "loginButton": "<PERSON><PERSON>", "loginFailed": "<PERSON><PERSON> failed", "loginSuccessful": "Login successful", "pleaseEnterUsername": "Please enter username", "pleaseEnterPassword": "Please enter password"}, "servers": {"management": "Server Management", "status": "Status", "online": "Online", "offline": "Offline", "connecting": "Connecting", "name": "Server Name", "address": "Address", "port": "Port", "actions": "Actions", "uptime": "Uptime", "serverOffline": "Server is offline"}, "subscription": {"title": "Subscription", "management": "Subscription Management", "status": "Status", "expiry": "Expiry Date", "renewal": "Renewal", "active": "Active", "expired": "Expired", "license": "License"}, "timeRange": {"1h": "1 Hour", "3h": "3 Hours", "6h": "6 Hours", "24h": "24 Hours", "7d": "7 Days"}, "forms": {"labels": {"id": "ID", "name": "Name", "email": "Email", "tokenId": "Token ID", "serverName": "Server Name", "ipAddress": "IP Address", "interfaceName": "Interface Name", "port": "Port", "portRange": "Port Range", "startPort": "Start Port", "endPort": "End Port", "protocol": "Protocol", "status": "Status", "actions": "Actions", "trafficScale": "Traffic Consumption", "allowForward": "Allow Forward", "allowIpv6": "Allow IPv6", "allowLatencyTest": "Allow Latency Test", "maxIps": "Max IPs", "maxConnectionsPerIp": "Max Connections per IP", "forwardEndpoints": "Forward Endpoints", "balanceStrategy": "Balance Strategy", "protocolFilters": "Protocol Filters", "renewalCode": "Renewal Code", "applicationTime": "Application Time", "codeId": "Code ID", "extendedDays": "Extended Days", "ingressAddress": "Ingress Address", "servePort": "Serve Port", "entryPoint": "Entry Point", "target": "Target", "targetSelectMode": "Target Select Mode", "testMethod": "Test Method", "line": "Line", "ipDomain": "IP / Domain", "networkTopology": "Network Topology", "priority": "Priority Order", "totServers": "Tot Servers", "totServerSelectMode": "Tot Server Select Mode", "totServerTestMethod": "Tot Server Test Method", "useForwardAsTransport": "Use Forward endpoints as Transport", "displayName": "Display Name", "advancedOptions": "Advanced Options", "priorityOrder": "Priority Order"}, "help": {"targetSelectMode": "Target select mode determines how to choose among multiple target addresses:<br/>• Best Latency: Select target with lowest latency<br/>• Fallback: Try targets in order, switching to next when previous fails<br/>• Domain Follow: Select target based on domain name<br/>• Round Robin: Use all targets in rotation<br/>• Random: Randomly select targets", "testMethod": "Test method for detecting target address connectivity and latency:<br/>• TCP Ping: Test by establishing TCP connection (recommended)<br/>• ICMP: Test using ICMP protocol ping", "forwardEndpoints": "Forward endpoints are relay servers that traffic will be sent to first, then forwarded to the final destination. Multiple endpoints can be selected for load balancing or redundancy.", "balanceStrategy": "Balance strategy determines how to distribute traffic among multiple forward endpoints or Tot servers:<br/>• Best Latency: Select server with lowest latency<br/>• Fallback: Switch to backup server when primary fails<br/>• Domain Follow: Select server based on domain name<br/>• Round Robin: Use all servers in rotation<br/>• Random: Randomly select servers", "totServers": "Tot (TCP over TCP) servers are used to aggregate multiple bandwidth lines. Multiple Tot servers can achieve bandwidth stacking for improved transfer speed and stability.", "totServerTestMethod": "Tot server test method for detecting Tot server connectivity:<br/>• TCP Ping: Test by establishing TCP connection (recommended)<br/>• ICMP: Test using ICMP protocol ping"}, "placeholders": {"enterName": "Enter name", "enterServerName": "Enter server name", "enterIpAddress": "Enter IP address", "enterInterfaceName": "Enter interface name", "enterStartPort": "Start port", "enterEndPort": "End port", "enterRenewalCode": "Enter your renewal code", "enterDisplayName": "Please enter display name", "enterIpOrDomain": "Enter IP address or domain name", "enterServePort": "Enter serve port (1-65535)", "selectProtocol": "Select protocol", "selectPackage": "Please select a package", "selectForwardEndpoints": "Select forward endpoints", "selectBalanceStrategy": "Select balance strategy", "selectLines": "Select lines", "leaveEmptyUnlimited": "Leave empty for unlimited", "searchById": "Search by exact ID", "searchByName": "Search by name (regex supported)", "searchByAddress": "Search ingress address (regex supported)", "selectTotServers": "Select tot servers", "selectTestMethod": "Select test method", "selectVersions": "Select versions", "selectStatus": "Select status", "searchAndSelectLines": "Search and select lines"}, "options": {"bestLatency": "Best Latency", "fallback": "Fallback", "domainFollow": "Domain Follow", "balanceRoundRobin": "Balance (Round Robin)", "balanceRandom": "Balance (Random)", "roundRobin": "Round Robin", "random": "Random", "tcpPing": "TCP Ping", "icmp": "ICMP", "http": "Http", "socks5": "Socks5", "bitTorrent": "BitTorrent", "tls": "Tls", "unknown": "Unknown"}, "validation": {"required": "This field is required", "pleaseEnterName": "Please enter name", "pleaseEnterServerName": "Please enter server name", "pleaseEnterDisplayName": "Please enter display name", "pleaseEnterIpAddress": "Please enter IP address", "pleaseEnterInterfaceName": "Please enter interface name", "pleaseEnterStartPort": "Please enter start port", "pleaseEnterEndPort": "Please enter end port", "pleaseEnterRenewalCode": "Please enter a renewal code", "pleaseInputName": "Please input name", "pleaseInputIngressAddress": "Please input ingress address", "pleaseSelectProtocol": "Please select protocol", "pleaseSelectLine": "Please select line", "pleaseEnterTargetAddress": "Please enter target address", "pleaseSelectTargetSelectMode": "Please select target select mode", "pleaseSelectTestMethod": "Please select test method", "pleaseSelectBalanceStrategy": "Please select a balance strategy", "portMustBeNumber": "Port must be a number", "selectTotTestMethod": "Please select ToT server test method", "selectTotBalanceStrategy": "Please select ToT server balance strategy", "renewalCodeMinLength": "Renewal code must be at least 5 characters", "portRange": "Port must be between 1 and 65535", "failedToTestLatency": "Failed to test latency"}, "advancedOptions": "Advanced Options"}, "actions": {"add": "Add", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "ok": "OK", "submit": "Submit", "refresh": "Refresh", "close": "Close", "back": "Back", "next": "Next", "reset": "Reset", "search": "Search", "hideSearch": "Hide Search", "showSearch": "Show Search", "hideSearchFilters": "Hide search filters", "showSearchFilters": "Show search filters", "clear": "Clear", "addServer": "Add Server", "addEndpoint": "Add Forward Endpoint", "addPort": "Add Port Forward", "saveChanges": "Save Changes", "copyInstallScript": "Copy Install Script", "copyInstallationCommand": "Copy Installation Command", "applyRenewalCode": "Apply Renewal Code", "resume": "Resume", "suspend": "Suspend"}, "status": {"online": "Online", "offline": "Offline", "active": "Active", "expired": "Expired", "expiringSoon": "Expiring Soon"}, "messages": {"confirm": {"deleteServer": "Are you sure you want to delete this server?", "deleteEndpoint": "Are you sure you want to delete this forward endpoint?", "deletePort": "Are you sure you want to delete port \"{name}\" ({address}:{port})?", "deleteSubscription": "This will permanently delete the subscription. Continue?", "resetTraffic": "Are you sure to reset traffic for this subscription? This will clear all traffic usage data.", "suspendPort": "Are you sure you want to suspend port \"{name}\" ({address}:{port})?", "resumePort": "Are you sure you want to resume port \"{name}\" ({address}:{port})?", "warning": "Warning", "suspendPortTitle": "Suspend Port", "resumePortTitle": "Resume Port"}, "success": {"serverAdded": "Server added successfully", "serverDeleted": "Server deleted successfully", "serverModified": "Server modified successfully", "commandCopied": "Installation command copied to clipboard", "endpointCreated": "Forward endpoint created successfully", "endpointUpdated": "Forward endpoint updated successfully", "endpointDeleted": "Forward endpoint deleted successfully", "portCreated": "Port created successfully", "portDeleted": "Port deleted successfully", "portUpdated": "Port updated successfully", "installScriptCopied": "Install script copied to clipboard", "installCommandCopied": "Installation command copied to clipboard", "licenseRefreshed": "License status refreshed", "renewalApplied": "Renewal code applied successfully!", "subscriptionAdded": "Subscription added successfully", "subscriptionDeleted": "Subscription deleted successfully", "subscriptionUpdated": "Subscription updated successfully", "subscriptionExtended": "Subscription extended successfully", "trafficReset": "Traffic reset successfully", "tokenCopied": "Token ID {tokenId} copied to clipboard for {email} (ID: {subscriptionId})"}, "error": {"failedToFetchEndpoints": "Failed to fetch endpoints", "failedToFetchData": "Failed to fetch data", "failedToCreatePort": "Failed to create port", "failedToCopyInstallScript": "Failed to copy install script", "failedToRefreshLicense": "Failed to refresh license status", "failedToApplyRenewal": "Failed to apply renewal code", "failedToSubmitForm": "Failed to submit form", "failedToDeletePort": "Failed to delete port", "failedToAddServer": "Failed to add server", "failedToDeleteServer": "Failed to delete server", "failedToModifyServer": "Failed to modify server", "failedToCopyCommand": "Failed to copy command to clipboard", "unknownError": "Unknown error", "missingServerData": "Missing server public key or interface name", "invalidServerData": "Unable to edit server: Invalid server data", "missingServerId": "Unable to edit server: Server ID is missing", "failedToLoadServerList": "Failed to load server list", "failedToLoadCompleteServerList": "Failed to load complete server list for search", "failedToAddSubscription": "Failed to add subscription", "failedToUpdateSubscription": "Failed to update subscription", "failedToDeleteSubscription": "Failed to delete subscription", "failedToExtendSubscription": "Failed to extend subscription", "failedToResetTraffic": "Failed to reset traffic", "failedToCopyToken": "Failed to copy token ID to clipboard", "failedToLoadSubscriptions": "Failed to load subscriptions"}, "info": {"noPortsInUse": "No ports in use", "totalItems": "Total {count} items", "itemsPerPage": "{count} / page", "loadingTableData": "Loading table data...", "loadingCardContent": "Loading card content...", "loadingDialogContent": "Loading dialog content...", "loadingPageContent": "Loading page content...", "subscriptionExpiredFeature": "Subscription expired - please renew to use this feature", "subscriptionExpiredWarning": "Your subscription has expired. Please renew your subscription to continue using the service.", "maxUniqueIps": "Maximum number of unique IP addresses that can connect", "maxConnectionsDescription": "Maximum number of connections per IP address", "protocolFilterDescription": "Select protocols to filter/block. Leave empty to allow all protocols."}}, "pages": {"servers": {"title": "Server Management", "addServerDialog": "Add New Server", "editServerDialog": "Edit Server", "version": "Version", "ports": "Ports", "portDetails": "Port Details", "name": "Name", "ipAddress": "IP Address", "status": "Status", "unknown": "Unknown", "online": "Online", "offline": "Offline", "actions": "Actions", "serverPrefix": "Server", "noPortsInUse": "No ports in use", "portPrefix": "Port"}, "endpoints": {"title": "Forward Endpoints", "management": "Forward Endpoint Management", "addEndpointDialog": "Add Forward Endpoint", "editEndpointDialog": "Edit Forward Endpoint"}, "ports": {"title": "Port Management", "addPortDialog": "Add Port Forward", "editPortDialog": "Edit Port Forward", "modifyPortDialog": "Modify Port Forward"}, "license": {"title": "License Renewal", "renewalHistory": "Renewal History", "currentLicenseStatus": "Current License Status", "applyRenewalCode": "Apply Renewal Code", "licenseExpires": "License Expires", "daysRemaining": "Days Remaining", "lastRenewal": "Last Renewal", "licenseId": "License ID", "licenseExpired": "License Expired", "licenseExpiredDescription": "Your software license has expired. Please apply a renewal code to continue using the service.", "licenseExpiringSoon": "License Expiring Soon", "licenseExpiringSoonDescription": "Your license will expire in {days} days. Consider renewing soon to avoid service interruption.", "renewalCodeHint": "Enter the renewal code provided by your software vendor", "confirmRenewal": "Are you sure you want to apply this renewal code? This action cannot be undone.", "confirmRenewalTitle": "Confirm <PERSON>wal", "applyCode": "Apply Code", "refreshStatus": "Refresh Status"}, "subscription": {"title": "Subscription Information", "speedDetails": "Speed Details", "validUntil": "<PERSON>id <PERSON>", "nextReset": "Next Reset", "bandwidth": "Bandwidth", "trafficUsage": "Traffic Usage", "unlimited": "Unlimited", "noDataAvailable": "No subscription data available", "serverStatus": "Server Status", "refresh": "Refresh"}, "subscriptionManagement": {"title": "Subscription Management", "addSubscription": "Add Subscription", "hideSearchFilters": "Hide search filters", "showSearchFilters": "Show search filters", "hideSearch": "Hide Search", "showSearch": "Show Search", "searchAndFilterOptions": "Search and filter options", "id": "ID", "tokenId": "Token ID", "email": "Email", "validUntil": "<PERSON>id <PERSON>", "nextReset": "Next Reset", "lines": "Lines", "search": "Search", "clear": "Clear", "searchByExactId": "Search by exact ID", "searchByExactTokenId": "Search by exact Token ID", "searchByEmail": "Search by email (partial match)", "startDate": "Start date", "endDate": "End date", "to": "to", "selectLines": "Select lines", "traffic": "Traffic", "actions": "Actions", "clickToCopy": "Click to copy: ", "lineCount": "Line Count", "maxIps": "Max IPs", "maxConnections": "Max Connections", "forwardEndpoint": "Forward Endpoint", "enabled": "Enabled", "disabled": "Disabled", "view": "View", "edit": "Edit", "extend": "Extend", "resetTraffic": "Reset Traffic", "delete": "Delete", "addUser": "Add User", "editUser": "Edit User", "emailAddress": "Email Address", "bandwidth": "Bandwidth", "trafficGB": "Traffic (GB)", "activateImmediately": "Activate Immediately", "maxPortsPerServer": "Max Ports per Server", "billingType": "Billing Type", "cycleBilling": "Cycle Billing", "onetimeBilling": "One-time Billing", "cycleDays": "Cycle Days", "cyclePrice": "Cycle Price", "onetimePrice": "One-time Price", "totalDays": "Total Days", "allowForwardEndpoint": "Allow Forward Endpoint", "allowIpNum": "Max IPs", "allowConnNum": "Max Connections per IP", "leaveEmptyForUnlimited": "Leave empty for unlimited", "cancel": "Cancel", "add": "Add", "update": "Update", "pleaseEnterEmailAddress": "Please enter email address", "pleaseEnterValidEmailAddress": "Please enter valid email address", "pleaseEnterMaxPortsPerServer": "Please enter max ports per server", "pleaseSelectAtLeastOneLine": "Please select at least one line", "confirmDelete": "Are you sure you want to delete this subscription?", "warning": "Warning", "confirmExtend": "Are you sure you want to extend this subscription by {days} days?", "confirmResetTraffic": "Are you sure you want to reset the traffic for this user?", "userAdded": "User added successfully", "userUpdated": "User updated successfully", "userDeleted": "User deleted successfully", "subscriptionExtended": "Subscription extended successfully", "trafficReset": "Traffic reset successfully", "failedToAddUser": "Failed to add user", "failedToUpdateUser": "Failed to update user", "failedToDeleteUser": "Failed to delete user", "failedToExtendSubscription": "Failed to extend subscription", "failedToResetTraffic": "Failed to reset traffic", "failedToLoadSubscriptions": "Failed to load subscriptions", "failedToLoadServers": "Failed to load servers", "tokenIdCopied": "Token ID copied to clipboard", "failedToCopyTokenId": "Failed to copy token ID", "editSubscription": "Edit Subscription", "cycle": "Cycle", "onetime": "One Time", "cycleDaysLabel": "Cycle Days", "cyclePriceLabel": "Cycle Price", "totalDaysLabel": "Total Days", "onetimePriceLabel": "One Time Price", "linesLabel": "Lines", "searchAndSelectLines": "Search and select lines", "maxIpsLabel": "Max IPs", "maxConnectionsPerIpLabel": "Max Connections per IP", "activateImmediatelyLabel": "Activate Immediately", "allowForwardEndpointLabel": "Allow Forward Endpoint", "enterTrafficLimitGB": "Enter traffic limit in GB", "enterEmailAddress": "Enter email address", "packageLines": "Package Lines", "additionalLines": "Additional Lines", "includedInPackage": "Included in Package", "selectAdditionalLines": "Select additional lines", "multiplier": "Multiplier", "trafficMultiplier": "Traffic Multiplier", "cycleBillingLabel": "Cycle Billing", "onetimeBillingLabel": "One-time Billing", "usingPackageConfig": "Using Package Configuration", "packageConfigDescription": "Package selected. Billing and line configuration will be automatically obtained from the package.", "fieldManagedByPackage": "This field is managed by the package. Please edit the package configuration to modify it.", "editPackageConfig": "Edit Package Configuration"}, "packageManagement": {"title": "Package Management", "createPackage": "Create Package", "editPackage": "Edit Package", "packageName": "Package Name", "displayName": "Display Name", "description": "Description", "totalTraffic": "Total Traffic", "bandwidth": "Bandwidth Limit", "maxPortsPerServer": "Max Ports per Server", "allowForwardEndpoint": "Allow Forward Endpoint", "allowIpNum": "Allow IP Number", "allowConnNum": "Connections per IP Limit", "isActive": "Package Status", "isDefault": "Default Package", "packageLines": "Package Lines", "lineCount": "Line Count", "status": "Status", "active": "Active", "inactive": "Inactive", "default": "<PERSON><PERSON><PERSON>", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update", "cancel": "Cancel", "save": "Save", "selectLine": "Select Line", "bandwidthLimit": "Bandwidth Limit (Mbps)", "trafficScale": "Traffic Scale", "lineTraffic": "Line Traffic (GB)", "addLine": "Add Line", "removeLine": "Remove Line", "pleaseEnterPackageName": "Please enter package name", "pleaseEnterDisplayName": "Please enter display name", "pleaseSelectAtLeastOneLine": "Please select at least one line", "packageNameExists": "Package name already exists", "trafficGB": "Traffic (GB)", "leaveEmptyForUnlimited": "Leave empty for unlimited", "leaveEmptyForUnlimitedPackage": "Leave empty to not use package restrictions, system default configuration will be used", "enterTrafficLimitGB": "Enter traffic limit in GB", "maxIpsLabel": "Max IPs", "maxConnectionsPerIpLabel": "Max Connections per IP", "maxIpsDescription": "Maximum number of unique IP addresses that can connect", "maxConnectionsPerIpDescription": "Maximum number of connections per IP address", "leaveEmptyUnlimited": "Leave empty for unlimited", "packageCreatedSuccessfully": "Package created successfully", "packageUpdatedSuccessfully": "Package updated successfully", "packageDeletedSuccessfully": "Package deleted successfully", "confirmDeletePackage": "Are you sure you want to delete this package?", "cannotDeletePackageInUse": "Cannot delete package that is in use", "failedToCreatePackage": "Failed to create package", "failedToUpdatePackage": "Failed to update package", "failedToDeletePackage": "Failed to delete package", "failedToLoadPackages": "Failed to load package list", "searchByName": "Search by name", "filterByStatus": "Filter by status", "allStatuses": "All Statuses", "onlyActive": "Only Active", "onlyInactive": "Only Inactive", "onlyDefault": "Only De<PERSON>ult", "allLinesSelected": "All lines are selected", "search": "Search", "clear": "Clear", "selectPackage": "Select Package"}}, "latencyTest": {"title": "Dialog Loading Test", "result": "Latency Test Result", "error": "Latency Test Error"}, "theme": {"light": "Light Theme", "dark": "Dark Theme", "auto": "Auto Theme"}, "brandName": "Firefly World", "search": {"filterOptionsLabel": "Search and filter options"}}