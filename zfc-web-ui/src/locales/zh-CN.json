{"dialogs": {"userDetail": {"title": "用户详情", "basicInfo": "基本信息", "historicalSpeed": "历史速度数据", "realtimeSpeed": "实时线路速度", "trafficUsage": "流量使用情况", "trafficConsumption": "每日流量消耗", "uploadSpeed": "上传速度", "downloadSpeed": "下载速度", "totalSpeed": "总速度", "clientIPCount": "客户端IP数量", "connectionCount": "连接数量", "clientIPs": "客户端IP", "connections": "连接数", "noData": "所选时间段内没有历史数据", "validUntil": "有效期至", "nextReset": "下次重置", "bandwidth": "带宽", "unlimited": "无限制", "portDetails": "端口转发详情", "showPorts": "显示端口详情", "hidePorts": "隐藏端口详情", "noPorts": "未配置端口转发", "failedToLoadPorts": "加载端口转发数据失败"}, "userSpeedDetail": {"title": "我的端口速度统计", "historicalData": "我的历史速度数据", "realtimeSpeed": "实时线路速度", "uploadSpeed": "上传速度", "downloadSpeed": "下载速度", "totalSpeed": "总速度", "trafficConsumption": "每日流量消耗", "noData": "所选时间段内没有历史数据", "selectLines": "选择线路", "selectAll": "全选", "linePrefix": "线路", "showingLines": "显示了 {count} 条有速度的线路，共有 {total} 条线路可用", "failedToLoadData": "加载数据失败", "failedToLoadHistorical": "加载历史速度数据失败", "failedToLoadRealtime": "加载实时速度数据失败", "failedToGetSubscription": "无法获取用户订阅信息", "failedToFetchSubscription": "获取订阅信息失败"}}, "common": {"loading": "加载中...", "logout": "退出登录", "confirm": "确认", "cancel": "取消", "ok": "确定", "save": "保存", "delete": "删除", "edit": "编辑", "add": "添加", "search": "搜索", "refresh": "刷新", "close": "关闭", "back": "返回", "next": "下一步", "submit": "提交", "reset": "重置", "language": "语言", "warning": "警告", "to": "到", "create": "创建", "days": "天", "none": "无"}, "dashboard": {"title": "端口转发管理", "welcome": "欢迎", "overview": "概览", "statistics": "统计信息"}, "navigation": {"dashboard": "仪表板", "servers": "服务器管理", "ports": "端口管理", "endpoints": "转发端点", "subscription": "订阅", "subscriptionManagement": "订阅管理", "packageManagement": "套餐管理", "licenseRenewal": "许可证续费"}, "login": {"title": "登录", "username": "用户名", "password": "密码", "loginButton": "登录", "loginFailed": "登录失败", "loginSuccessful": "登录成功", "pleaseEnterUsername": "请输入用户名", "pleaseEnterPassword": "请输入密码"}, "servers": {"management": "服务器管理", "status": "状态", "online": "在线", "offline": "离线", "connecting": "连接中", "name": "服务器名称", "address": "地址", "port": "端口", "actions": "操作", "uptime": "在线时间", "serverOffline": "服务器离线"}, "subscription": {"title": "订阅", "management": "订阅管理", "status": "状态", "expiry": "到期日期", "renewal": "续费", "active": "有效", "expired": "已过期", "license": "许可证"}, "timeRange": {"1h": "1 小时", "3h": "3 小时", "6h": "6 小时", "24h": "24 小时", "7d": "7 天"}, "forms": {"labels": {"id": "ID", "name": "名称", "email": "邮箱", "tokenId": "令牌ID", "serverName": "服务器名称", "ipAddress": "IP地址", "interfaceName": "网卡名称", "port": "端口", "portRange": "端口范围", "startPort": "起始端口", "endPort": "结束端口", "protocol": "协议", "status": "状态", "actions": "操作", "trafficScale": "流量消耗", "allowForward": "允许转发", "allowIpv6": "允许IPv6", "allowLatencyTest": "允许延迟测试", "maxIps": "最大IP数", "maxConnectionsPerIp": "每IP最大连接数", "forwardEndpoints": "转发端点", "balanceStrategy": "均衡策略", "protocolFilters": "协议过滤器", "renewalCode": "续费代码", "applicationTime": "申请时间", "codeId": "代码ID", "extendedDays": "延长天数", "ingressAddress": "入口地址", "servePort": "服务端口", "entryPoint": "入口点", "target": "目标", "targetSelectMode": "目标选择模式", "testMethod": "测试方法", "line": "线路", "ipDomain": "IP / 域名", "networkTopology": "网络拓扑", "priority": "优先级顺序", "totServers": "Tot 服务器", "totServerSelectMode": "Tot 服务器选择模式", "totServerTestMethod": "Tot 服务器测试方法", "useForwardAsTransport": "使用转发端点作为传输", "displayName": "显示名称", "advancedOptions": "高级选项", "priorityOrder": "优先级顺序"}, "help": {"targetSelectMode": "目标选择模式决定如何在多个目标地址中进行选择：<br/>• 最佳延迟：选择延迟最低的目标<br/>• 备用：按顺序尝试，前一个失败时切换到下一个<br/>• 域名跟随：根据域名选择相应的目标<br/>• 均衡(轮询)：轮流使用所有目标<br/>• 均衡(随机)：随机选择目标", "testMethod": "测试方法用于检测目标地址的连通性和延迟：<br/>• TCP Ping：通过建立TCP连接测试（推荐）<br/>• ICMP：使用ICMP协议ping测试", "forwardEndpoints": "转发端点是中转服务器，流量将先发送到转发端点，再由转发端点转发到最终目标。可以选择多个端点进行负载均衡或冗余。", "balanceStrategy": "均衡策略决定如何在多个转发端点或Tot服务器之间分配流量：<br/>• 最佳延迟：选择延迟最低的服务器<br/>• 备用：主服务器故障时切换到备用服务器<br/>• 域名跟随：根据域名选择服务器<br/>• 轮询：轮流使用所有服务器<br/>• 随机：随机选择服务器", "totServers": "Tot（TCP over TCP）服务器用于聚合多条带宽线路，通过多个Tot服务器可以实现带宽叠加，提高传输速度和稳定性。", "totServerTestMethod": "Tot服务器测试方法用于检测Tot服务器的连通性：<br/>• TCP Ping：通过建立TCP连接测试（推荐）<br/>• ICMP：使用ICMP协议ping测试"}, "placeholders": {"enterName": "请输入名称", "enterServerName": "请输入服务器名称", "enterIpAddress": "请输入IP地址", "enterInterfaceName": "请输入网卡名称", "enterStartPort": "起始端口", "enterEndPort": "结束端口", "enterRenewalCode": "请输入续费代码", "enterDisplayName": "请输入显示名称", "enterIpOrDomain": "请输入IP地址或域名", "enterServePort": "请输入服务端口 (1-65535)", "selectProtocol": "选择协议", "selectPackage": "请选择套餐", "selectForwardEndpoints": "选择转发端点", "selectBalanceStrategy": "选择均衡策略", "selectLines": "选择线路", "leaveEmptyUnlimited": "留空表示无限制", "searchById": "按确切ID搜索", "searchByName": "按名称搜索 (支持正则)", "searchByAddress": "搜索入口地址 (支持正则)", "selectTotServers": "选择 Tot 服务器", "selectTestMethod": "选择测试方法", "selectVersions": "选择版本", "selectStatus": "选择状态", "searchAndSelectLines": "搜索并选择线路"}, "options": {"bestLatency": "最佳延迟", "fallback": "备用", "domainFollow": "域名跟随", "balanceRoundRobin": "均衡 (轮询)", "balanceRandom": "均衡 (随机)", "roundRobin": "轮询", "random": "随机", "tcpPing": "TCP Ping", "icmp": "ICMP", "http": "Http", "socks5": "Socks5", "bitTorrent": "BitTorrent", "tls": "Tls", "unknown": "未知"}, "validation": {"required": "此字段为必填项", "pleaseEnterName": "请输入名称", "pleaseEnterServerName": "请输入服务器名称", "pleaseEnterDisplayName": "请输入显示名称", "pleaseEnterIpAddress": "请输入IP地址", "pleaseEnterInterfaceName": "请输入网卡名称", "pleaseEnterStartPort": "请输入起始端口", "pleaseEnterEndPort": "请输入结束端口", "pleaseEnterRenewalCode": "请输入续费代码", "pleaseInputName": "请输入名称", "pleaseInputIngressAddress": "请输入入口地址", "pleaseSelectProtocol": "请选择协议", "pleaseSelectLine": "请选择线路", "pleaseEnterTargetAddress": "请输入目标地址", "pleaseSelectTargetSelectMode": "请选择目标选择模式", "pleaseSelectTestMethod": "请选择测试方法", "pleaseSelectBalanceStrategy": "请选择负载均衡策略", "portMustBeNumber": "端口必须是数字", "selectTotTestMethod": "请选择 ToT 服务器测试方法", "selectTotBalanceStrategy": "请选择 ToT 服务器负载均衡策略", "renewalCodeMinLength": "续费代码至少需要5个字符", "portRange": "端口必须在1到65535之间", "failedToTestLatency": "延迟测试失败"}, "advancedOptions": "高级选项"}, "actions": {"add": "添加", "edit": "编辑", "delete": "删除", "save": "保存", "cancel": "取消", "confirm": "确认", "ok": "确定", "submit": "提交", "refresh": "刷新", "close": "关闭", "back": "返回", "next": "下一步", "reset": "重置", "search": "搜索", "hideSearch": "隐藏搜索", "showSearch": "显示搜索", "hideSearchFilters": "隐藏搜索过滤器", "showSearchFilters": "显示搜索过滤器", "clear": "清除", "addServer": "添加服务器", "addEndpoint": "添加转发端点", "addPort": "添加端口转发", "saveChanges": "保存更改", "copyInstallScript": "复制安装脚本", "copyInstallationCommand": "复制安装命令", "applyRenewalCode": "应用续费代码", "resume": "恢复", "suspend": "暂停"}, "status": {"online": "在线", "offline": "离线", "active": "活跃", "expired": "已过期", "expiringSoon": "即将过期"}, "messages": {"confirm": {"deleteServer": "确定要删除此服务器吗？", "deleteEndpoint": "确定要删除此转发端点吗？", "deletePort": "确定要删除端口 \"{name}\" ({address}:{port}) 吗？", "deleteSubscription": "这将永久删除订阅。是否继续？", "resetTraffic": "确定要重置此订阅的流量吗？这将清除所有流量使用数据。", "suspendPort": "确定要暂停端口 \"{name}\" ({address}:{port}) 吗？", "resumePort": "确定要恢复端口 \"{name}\" ({address}:{port}) 吗？", "warning": "警告", "suspendPortTitle": "暂停端口", "resumePortTitle": "恢复端口"}, "success": {"serverAdded": "服务器添加成功", "serverDeleted": "服务器删除成功", "serverModified": "服务器修改成功", "commandCopied": "安装命令已复制到剪贴板", "endpointCreated": "转发端点创建成功", "endpointUpdated": "转发端点更新成功", "endpointDeleted": "转发端点删除成功", "portCreated": "端口创建成功", "portDeleted": "端口删除成功", "portUpdated": "端口更新成功", "installScriptCopied": "安装脚本已复制到剪贴板", "installCommandCopied": "安装命令已复制到剪贴板", "licenseRefreshed": "许可证状态已刷新", "renewalApplied": "续费代码应用成功！", "subscriptionAdded": "订阅添加成功", "subscriptionDeleted": "订阅删除成功", "subscriptionUpdated": "订阅更新成功", "subscriptionExtended": "订阅延期成功", "trafficReset": "流量重置成功", "tokenCopied": "令牌ID {tokenId} 已为 {email} (ID: {subscriptionId}) 复制到剪贴板"}, "error": {"failedToFetchEndpoints": "获取端点失败", "failedToFetchData": "获取数据失败", "failedToCreatePort": "创建端口失败", "failedToCopyInstallScript": "复制安装脚本失败", "failedToRefreshLicense": "刷新许可证状态失败", "failedToApplyRenewal": "应用续费代码失败", "failedToSubmitForm": "提交表单失败", "failedToDeletePort": "删除端口失败", "failedToAddServer": "添加服务器失败", "failedToDeleteServer": "删除服务器失败", "failedToModifyServer": "修改服务器失败", "failedToCopyCommand": "复制命令到剪贴板失败", "unknownError": "未知错误", "missingServerData": "缺少服务器公钥或网卡名称", "invalidServerData": "无法编辑服务器：服务器数据无效", "missingServerId": "无法编辑服务器：服务器ID缺失", "failedToLoadServerList": "加载服务器列表失败", "failedToLoadCompleteServerList": "加载完整服务器搜索列表失败", "failedToAddSubscription": "添加订阅失败", "failedToUpdateSubscription": "更新订阅失败", "failedToDeleteSubscription": "删除订阅失败", "failedToExtendSubscription": "延期订阅失败", "failedToResetTraffic": "重置流量失败", "failedToCopyToken": "复制令牌ID失败", "failedToLoadSubscriptions": "加载订阅失败"}, "info": {"noPortsInUse": "没有在使用的端口", "totalItems": "共 {count} 项", "itemsPerPage": "{count} 条/页", "loadingTableData": "正在加载表格数据...", "loadingCardContent": "正在加载卡片内容...", "loadingDialogContent": "正在加载对话框内容...", "loadingPageContent": "正在加载页面内容...", "subscriptionExpiredFeature": "订阅已过期 - 请续费以使用此功能", "subscriptionExpiredWarning": "您的订阅已过期。请续费以继续使用服务。", "maxUniqueIps": "可连接的唯一IP地址的最大数量", "maxConnectionsDescription": "每个IP地址的最大连接数", "protocolFilterDescription": "选择要过滤/阻止的协议。留空表示允许所有协议。"}}, "pages": {"servers": {"title": "服务器管理", "addServerDialog": "添加新服务器", "editServerDialog": "编辑服务器", "version": "版本", "ports": "端口", "portDetails": "端口详情", "name": "名称", "ipAddress": "IP地址", "status": "状态", "unknown": "未知", "online": "在线", "offline": "离线", "actions": "操作", "serverPrefix": "服务器", "noPortsInUse": "没有在使用的端口", "portPrefix": "端口"}, "endpoints": {"title": "转发端点", "management": "转发端点管理", "addEndpointDialog": "添加转发端点", "editEndpointDialog": "编辑转发端点"}, "ports": {"title": "端口管理", "addPortDialog": "添加端口转发", "editPortDialog": "编辑端口转发", "modifyPortDialog": "修改端口转发"}, "license": {"title": "许可证续费", "renewalHistory": "续费历史", "currentLicenseStatus": "当前许可证状态", "applyRenewalCode": "应用续费代码", "licenseExpires": "许可证到期时间", "daysRemaining": "剩余天数", "lastRenewal": "最后续费时间", "licenseId": "许可证ID", "licenseExpired": "许可证已过期", "licenseExpiredDescription": "您的软件许可证已过期。请应用续费代码以继续使用服务。", "licenseExpiringSoon": "许可证即将过期", "licenseExpiringSoonDescription": "您的许可证将在 {days} 天内过期。建议尽快续费以避免服务中断。", "renewalCodeHint": "输入软件供应商提供的续费代码", "confirmRenewal": "您确定要应用此续费代码吗？此操作无法撤消。", "confirmRenewalTitle": "确认续费", "applyCode": "应用代码", "refreshStatus": "刷新状态"}, "subscription": {"title": "订阅信息", "speedDetails": "速度详情", "validUntil": "有效期至", "nextReset": "下次重置", "bandwidth": "带宽", "trafficUsage": "流量使用情况", "unlimited": "无限制", "noDataAvailable": "没有可用的订阅数据", "serverStatus": "服务器状态", "refresh": "刷新"}, "subscriptionManagement": {"title": "订阅管理", "addSubscription": "添加订阅", "hideSearchFilters": "隐藏搜索过滤器", "showSearchFilters": "显示搜索过滤器", "hideSearch": "隐藏搜索", "showSearch": "显示搜索", "searchAndFilterOptions": "搜索和过滤选项", "id": "ID", "tokenId": "令牌ID", "email": "邮箱", "validUntil": "有效期至", "nextReset": "下次重置", "lines": "线路", "search": "搜索", "clear": "清除", "searchByExactId": "按确切ID搜索", "searchByExactTokenId": "按确切令牌ID搜索", "searchByEmail": "按邮箱搜索（部分匹配）", "startDate": "开始日期", "endDate": "结束日期", "to": "到", "selectLines": "选择线路", "traffic": "流量", "actions": "操作", "clickToCopy": "点击复制：", "lineCount": "线路数量", "maxIps": "最大IP数", "maxConnections": "最大连接数", "forwardEndpoint": "转发端点", "enabled": "已启用", "disabled": "已禁用", "view": "查看", "edit": "编辑", "extend": "延期", "resetTraffic": "重置流量", "delete": "删除", "addUser": "添加用户", "editUser": "编辑用户", "emailAddress": "邮箱地址", "bandwidth": "带宽", "trafficGB": "流量（GB）", "activateImmediately": "立即激活", "maxPortsPerServer": "每服务器最大端口数", "billingType": "计费类型", "cycleBilling": "周期计费", "onetimeBilling": "一次性计费", "cycleDays": "周期天数", "cyclePrice": "周期价格", "onetimePrice": "一次性价格", "totalDays": "总天数", "allowForwardEndpoint": "允许转发端点", "allowIpNum": "最大IP数", "allowConnNum": "每IP最大连接数", "leaveEmptyForUnlimited": "留空表示无限制", "maxUniqueIpsDescription": "可连接的唯一IP地址的最大数量", "maxConnectionsPerIpDescription": "每个IP地址的最大连接数", "cancel": "取消", "add": "添加", "update": "更新", "pleaseEnterEmailAddress": "请输入邮箱地址", "pleaseEnterValidEmailAddress": "请输入有效的邮箱地址", "pleaseEnterMaxPortsPerServer": "请输入每服务器最大端口数", "pleaseSelectAtLeastOneLine": "请至少选择一条线路", "confirmDelete": "您确定要删除此订阅吗？", "warning": "警告", "confirmExtend": "您确定要将此订阅延期 {days} 天吗？", "confirmResetTraffic": "您确定要重置此用户的流量吗？", "userAdded": "用户添加成功", "userUpdated": "用户更新成功", "userDeleted": "用户删除成功", "subscriptionExtended": "订阅延期成功", "trafficReset": "流量重置成功", "failedToAddUser": "添加用户失败", "failedToUpdateUser": "更新用户失败", "failedToDeleteUser": "删除用户失败", "failedToExtendSubscription": "延期订阅失败", "failedToResetTraffic": "重置流量失败", "failedToLoadSubscriptions": "加载订阅失败", "failedToLoadServers": "加载服务器失败", "tokenIdCopied": "令牌ID已复制到剪贴板", "failedToCopyTokenId": "复制令牌ID失败", "editSubscription": "编辑订阅", "cycle": "周期", "onetime": "一次性", "cycleDaysLabel": "周期天数", "cyclePriceLabel": "周期价格", "totalDaysLabel": "总天数", "onetimePriceLabel": "一次性价格", "linesLabel": "线路", "searchAndSelectLines": "搜索并选择线路", "maxIpsLabel": "最大IP数", "maxConnectionsPerIpLabel": "每IP最大连接数", "leaveEmptyUnlimited": "留空表示无限制", "maxIpsDescription": "可连接的唯一IP地址的最大数量", "activateImmediatelyLabel": "立即激活", "allowForwardEndpointLabel": "允许转发端点", "enterTrafficLimitGB": "输入流量限制（GB）", "enterEmailAddress": "输入邮箱地址", "packageLines": "套餐线路", "additionalLines": "额外线路", "includedInPackage": "套餐内", "selectAdditionalLines": "选择额外线路", "multiplier": "倍率", "trafficMultiplier": "流量倍率", "usingPackageConfig": "使用套餐配置", "packageConfigDescription": "已选择套餐，计费方式、线路配置等将从套餐中自动获取", "fieldManagedByPackage": "该字段由套餐管理，如需修改请编辑套餐配置", "editPackageConfig": "编辑套餐配置", "cycleBillingLabel": "周期计费", "onetimeBillingLabel": "一次性计费"}, "packageManagement": {"title": "套餐管理", "createPackage": "创建套餐", "editPackage": "编辑套餐", "packageName": "套餐名称", "displayName": "显示名称", "description": "套餐描述", "totalTraffic": "总流量", "bandwidth": "带宽限制", "maxPortsPerServer": "每服务器最大端口数", "allowForwardEndpoint": "允许转发端点", "allowIpNum": "允许IP数量", "allowConnNum": "每IP连接数限制", "isActive": "套餐状态", "isDefault": "默认套餐", "packageLines": "套餐线路", "lineCount": "线路数量", "status": "状态", "active": "激活", "inactive": "禁用", "default": "默认", "actions": "操作", "view": "查看", "edit": "编辑", "delete": "删除", "create": "创建", "update": "更新", "cancel": "取消", "save": "保存", "selectLine": "选择线路", "bandwidthLimit": "带宽限制(Mbps)", "trafficScale": "流量倍率", "lineTraffic": "线路流量(GB)", "addLine": "添加线路", "removeLine": "移除线路", "pleaseEnterPackageName": "请输入套餐名称", "pleaseEnterDisplayName": "请输入显示名称", "pleaseSelectAtLeastOneLine": "请至少选择一条线路", "packageNameExists": "套餐名称已存在", "trafficGB": "流量(GB)", "leaveEmptyForUnlimited": "留空表示无限制", "leaveEmptyForUnlimitedPackage": "留空表示不使用套餐限制，将使用系统默认配置", "enterTrafficLimitGB": "输入流量限制（GB）", "maxIpsLabel": "最大IP数", "maxConnectionsPerIpLabel": "每IP最大连接数", "maxIpsDescription": "可连接的唯一IP地址的最大数量", "maxConnectionsPerIpDescription": "每个IP地址的最大连接数", "leaveEmptyUnlimited": "留空表示无限制", "packageCreatedSuccessfully": "套餐创建成功", "packageUpdatedSuccessfully": "套餐更新成功", "packageDeletedSuccessfully": "套餐删除成功", "confirmDeletePackage": "确定要删除这个套餐吗？", "cannotDeletePackageInUse": "无法删除正在使用的套餐", "failedToCreatePackage": "创建套餐失败", "failedToUpdatePackage": "更新套餐失败", "failedToDeletePackage": "删除套餐失败", "failedToLoadPackages": "加载套餐列表失败", "searchByName": "按名称搜索", "filterByStatus": "按状态筛选", "allStatuses": "所有状态", "onlyActive": "仅激活", "onlyInactive": "仅禁用", "onlyDefault": "仅默认", "allLinesSelected": "所有线路都已选择", "search": "搜索", "clear": "清除", "selectPackage": "选择套餐"}}, "latencyTest": {"title": "对话框加载测试", "result": "延迟测试结果", "error": "延迟测试错误"}, "theme": {"light": "浅色主题", "dark": "深色主题", "auto": "自动主题"}, "brandName": "莹火虫的世界", "search": {"filterOptionsLabel": "搜索和过滤选项"}}