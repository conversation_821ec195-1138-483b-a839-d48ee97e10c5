use std::{
    net::{IpAddr, SocketAddr},
    sync::Arc,
    time::Duration,
};

use anyhow::{anyhow, bail};
use common::{
    app_message::{LatencyTestMethod, Protocol, TestLatencyInfo, TestLatencyType},
    constant::TCP_CONNECT_TIMEOUT,
    into_socket_addr, TotConfig,
};
use private_tun::{
    address::Address,
    snell_impl_ver::{client_zfc::ConnType, config::ClientConfig, TestLatencyMethod},
};
use tokio::net::TcpStream;

use crate::{
    backend_transport::BackendTransport,
    protocol::{
        hammer::{HammerClientLite, HammerHandlerForWorkerTun},
        tot::{
            session::{tot_test_latency_for_target_list, TotSession},
            TotClientConfigWrapper, TotClientForWorkerTun,
        },
    },
    WorkerAppContext,
};

pub async fn direct_ping_test(ctx: Arc<WorkerAppContext>, ip_addr: IpAddr) -> anyhow::Result<u64> {
    let mut handles = Vec::with_capacity(3);
    let mut config = surge_ping::Config::default();
    if ip_addr.is_ipv4() {
        config.kind = surge_ping::ICMP::V4;
    } else {
        config.kind = surge_ping::ICMP::V6;
    }
    let client = if ip_addr.is_ipv4() {
        ctx.ping_client.v4_client()
    } else {
        ctx.ping_client.v6_client()
    };
    for i in 0..3 {
        let ip_addr = ip_addr.clone();
        let client_clone = client.clone();
        handles.push(tokio::spawn(async move {
            let mut pingger = client_clone
                .pinger(ip_addr, surge_ping::PingIdentifier(i))
                .await;
            pingger.ping(surge_ping::PingSequence(i), &[0; 64]).await
        }));
    }

    let mut results = Vec::with_capacity(3);
    for handle in handles {
        let result = handle.await??;
        results.push(result.1.as_micros() as u64);
    }
    let rtt = results.iter().sum::<u64>() / 3;
    Ok(rtt)
}

pub async fn hammer_backend_latency_test(
    fwd_client: &HammerHandlerForWorkerTun,
    ip_addr: IpAddr,
    port: Option<u16>,
    method: LatencyTestMethod,
) -> anyhow::Result<u64> {
    if port.is_none() && method == LatencyTestMethod::Tcpping {
        anyhow::bail!("port is None");
    }
    let best_latency_server = match fwd_client.get_best_latency_server().await {
        Ok(best_latency_server) => best_latency_server,
        Err(e) => {
            log::error!("hammer_backend_latency_test get best latency server failed: {e}");
            return Err(anyhow!(
                "hammer_backend_latency_test get best latency server failed: {e}"
            ));
        }
    };
    log::info!("hammer_backend_latency_test for target: {ip_addr}:{port:?} method: {method:?} selected best_latency_server: {best_latency_server}");
    let now = std::time::Instant::now();
    let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
    fwd_client
        .client()
        .lock()
        .await
        .push_evt(ConnType::TestLatency {
            rst_tx: rst_tx,
            target: Address::Socket((ip_addr, port.unwrap_or(80)).into()),
            server_name: best_latency_server.clone(),
            method: into_test_latency_method(method),
            piped_stream: None,
            reuse_tcp: true,
        })
        .await
        .map_err(|_| anyhow!("hammer_backend_latency_test push error"))?;
    let latency = tokio::time::timeout(Duration::from_secs(5), rst_rx).await;
    log::info!(
        "hammer_backend_latency_test elasped latency: {:?} remote result: {:?} micros sec backend_server: {best_latency_server} ip: {ip_addr}:{port:?} method: {method:?}",
        now.elapsed(),
        latency
    );
    match latency {
        Ok(Ok(Ok(latency))) => {
            if latency > 6000 {
                log::error!("hammer_backend_latency_test too high latency: {latency:?} recheck server health");
                fwd_client
                    .client()
                    .lock()
                    .await
                    .recheck_server_health()
                    .await;
            }
            Ok(latency as u64)
        }
        _ => {
            log::error!("hammer_backend_latency_test failed recheck server health");
            fwd_client
                .client()
                .lock()
                .await
                .recheck_server_health()
                .await;
            Err(anyhow!("hammer_backend_latency_test failed"))
        }
    }
}

pub async fn tot_backend_latency_test(
    fwd_client: &TotClientForWorkerTun,
    ip_addr: IpAddr,
    port: Option<u16>,
    method: LatencyTestMethod,
) -> anyhow::Result<u64> {
    if port.is_none() && method == LatencyTestMethod::Tcpping {
        anyhow::bail!("port is None");
    }
    let remote = Address::Socket((ip_addr, port.unwrap_or(80)).into());
    let method = into_test_latency_method(method);
    let latency_result = tot_test_latency_for_target_list(
        fwd_client.tot_server_handler(),
        fwd_client.fwd_handler(),
        &[remote.clone()],
        method,
    )
    .await?;
    if latency_result.len() == 0 {
        log::error!(
            "tot backend test latency failed for target: {} no result method: {:?}",
            remote,
            method
        );
        return Err(anyhow::anyhow!(
            "tot backend test latency failed for target: {} no result method: {:?}",
            remote,
            method
        ));
    }
    Ok(latency_result[0].1 as u64)
}

pub async fn direct_tcpping_test(
    out_ip_addr: &Option<IpAddr>,
    ip_addr: IpAddr,
    port: u16,
) -> anyhow::Result<u64> {
    let now = std::time::Instant::now();
    let target_addr = (ip_addr, port).into();
    let stream = if let Some(out_ip_addr) = out_ip_addr {
        log::info!(
            "direct_tcpping_test with out_ip_addr: {out_ip_addr} ip_addr: {ip_addr} port: {port}"
        );
        match out_ip_addr {
            IpAddr::V4(_v4) => {
                let socket = tokio::net::TcpSocket::new_v4()?;
                socket.bind(SocketAddr::new(out_ip_addr.clone(), 0))?;
                tokio::time::timeout(
                    Duration::from_secs(TCP_CONNECT_TIMEOUT),
                    socket.connect(target_addr),
                )
                .await
            }
            IpAddr::V6(_v6) => {
                let socket = tokio::net::TcpSocket::new_v6()?;
                socket.bind(SocketAddr::new(out_ip_addr.clone(), 0))?;
                tokio::time::timeout(
                    Duration::from_secs(TCP_CONNECT_TIMEOUT),
                    socket.connect(target_addr),
                )
                .await
            }
        }
    } else {
        tokio::time::timeout(
            Duration::from_secs(TCP_CONNECT_TIMEOUT),
            TcpStream::connect(target_addr),
        )
        .await
    };
    // 尝试在5秒内建立连接
    match stream {
        Ok(connect_result) => {
            match connect_result {
                Ok(_stream) => {
                    // 连接成功，返回延迟时间
                    Ok(now.elapsed().as_micros() as u64)
                }
                Err(e) => {
                    // 连接失败，返回错误
                    Err(anyhow!("test latency for: {ip_addr}:{port} failed: {e}"))
                }
            }
        }
        Err(_) => {
            // 超时未连接成功，返回超时错误
            Err(anyhow!(
                "test latency for: {ip_addr}:{port} failed: timeout"
            ))
        }
    }
}
pub fn into_test_latency_method(method: LatencyTestMethod) -> TestLatencyMethod {
    match method {
        LatencyTestMethod::Tcpping => private_tun::snell_impl_ver::TestLatencyMethod::Tcpping,
        LatencyTestMethod::Icmp => private_tun::snell_impl_ver::TestLatencyMethod::Icmp,
    }
}
pub async fn fwd_server_to_remote_test(
    ip_addr: IpAddr,
    port: Option<u16>,
    fwd_client: &HammerClientLite,
    fwd_server: Box<str>,
    method: LatencyTestMethod,
) -> anyhow::Result<u64> {
    if port.is_none() && method == LatencyTestMethod::Tcpping {
        anyhow::bail!("port is None");
    }
    let latency = fwd_client
        .test_latency(
            fwd_server.as_ref(),
            ip_addr,
            port,
            into_test_latency_method(method),
        )
        .await?;
    Ok(latency as u64)
}

pub async fn tot_direct_remote_test(
    tot_session: &Arc<TotSession>,
    remote: &Address,
    method: TestLatencyMethod,
) -> anyhow::Result<u64> {
    let latency = tot_session.test_latency(remote, method).await?;
    Ok(latency as u64)
}

pub async fn setup_fwd_server(
    ctx: Arc<WorkerAppContext>,
    fwd_configs: Vec<Protocol>,
) -> anyhow::Result<(Vec<HammerClientLite>, Vec<Arc<TotSession>>)> {
    if fwd_configs.len() == 0 {
        return Ok((vec![], vec![]));
    }
    let hammer_clients = fwd_configs
        .iter()
        .filter_map(|fwd_config| match fwd_config {
            Protocol::Hammer { config } => serde_json::from_str::<ClientConfig>(config)
                .ok()
                .into_iter()
                .filter_map(|x| HammerClientLite::new(x).ok())
                .next(),
            _ => None,
        })
        .collect();
    let tot_session_manager = ctx.tot_manager.get_ctx().tot_session_manager_ref();

    let mut tot_sessions = Vec::new();
    for fwd_config in fwd_configs {
        let tot_config: TotConfig = match fwd_config {
            Protocol::Tot { config } => serde_json::from_str(&config)?,
            _ => {
                continue;
            }
        };
        let tot_config_wrapper = TotClientConfigWrapper::from(tot_config);
        match tot_session_manager
            .get_session_by_config(&tot_config_wrapper)
            .await
        {
            Some(session) => {
                tot_sessions.push(session);
            }
            None => {}
        };
    }
    Ok((hammer_clients, tot_sessions))
}

pub async fn test_latency_msg(
    ctx: Arc<WorkerAppContext>,
    info: &TestLatencyInfo,
    hammer_clients: &Vec<HammerClientLite>,
    tot_sessions: &Vec<Arc<TotSession>>,
) -> Result<u64, anyhow::Error> {
    let latency = match &info.test_type {
        TestLatencyType::FwdServerToRemote { fwd_name, remote } => {
            let Some(client) = hammer_clients
                .iter()
                .find(|c| c.get_server_by_name(&fwd_name).is_some())
            else {
                bail!("fwd_server not found: {}", fwd_name);
            };
            fwd_server_to_remote_test(
                remote.ip(),
                Some(remote.port()),
                client,
                fwd_name.to_string().into_boxed_str(),
                info.test_method,
            )
            .await
        }
        TestLatencyType::DirectRemote { remote } => match &ctx.backend_transport {
            Some(BackendTransport::Hammer(fwd)) => {
                hammer_backend_latency_test(fwd, remote.ip(), Some(remote.port()), info.test_method)
                    .await
            }
            Some(BackendTransport::Tot(fwd)) => {
                tot_backend_latency_test(fwd, remote.ip(), Some(remote.port()), info.test_method)
                    .await
            }
            _ => match info.test_method {
                LatencyTestMethod::Icmp => direct_ping_test(ctx, remote.ip()).await,
                LatencyTestMethod::Tcpping => {
                    direct_tcpping_test(&ctx.out_ip_addr, remote.ip(), remote.port()).await
                }
            },
        },
        TestLatencyType::TotDirectRemote { remote } => {
            let Some(tot_session) = tot_sessions.first() else {
                bail!("tot_session not found");
            };
            tot_direct_remote_test(
                tot_session,
                &Address::Socket(remote.clone()),
                into_test_latency_method(info.test_method),
            )
            .await
        }

        TestLatencyType::ToFwdServer { fwd_name } => {
            let Some(fwd_server_addr) =
                hammer_clients
                    .iter()
                    .find_map(|c| match c.get_server_by_name(&fwd_name) {
                        Some(addr) => Some(addr.server_addr.clone()),
                        None => None,
                    })
            else {
                bail!("fwd_server not found: {}", fwd_name);
            };
            let fwd_server_addr = into_socket_addr(&fwd_server_addr).await?;
            match info.test_method {
                LatencyTestMethod::Icmp => direct_ping_test(ctx, fwd_server_addr.ip()).await,
                LatencyTestMethod::Tcpping => {
                    direct_tcpping_test(
                        &ctx.out_ip_addr,
                        fwd_server_addr.ip(),
                        fwd_server_addr.port(),
                    )
                    .await
                }
            }
        }
    };
    Ok(latency?)
}
