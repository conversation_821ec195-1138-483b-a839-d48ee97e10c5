use crate::{http_client::HttpClient, preludes::FilterType};
use anyhow::{anyhow, Result};
use common::{
    app_message::{
        WorkerFetchGlobalLatestConfigResponse, WorkerVersionInfo, WorkerVersionReportRequest,
    },
    crypto::{parse_secret_key, stringify_public_key_from_secret_key},
};
use http::{header, HeaderValue, Method};
use log::{debug, error, info, warn};
use reqwest::{Body, Request, Url};
#[cfg(target_os = "linux")]
use std::process::Command;
use std::{collections::HashSet, path::PathBuf, sync::Arc};

use tokio::{fs as tokio_fs, sync::Mutex};

#[cfg(not(target_os = "linux"))]
use crate::http_client;
use crate::preludes::Config;

fn create_json_request_wiht_private_key(
    method: Method,
    url: &str,
    body: Option<String>,
    private_key: Option<&str>,
) -> anyhow::Result<Request> {
    let url: Url = url.try_into()?;
    let host = url.host().ok_or(anyhow!("host not existed"))?.to_string();
    let mut request = Request::new(method, url);
    request
        .headers_mut()
        .insert(header::HOST, HeaderValue::from_str(&host)?);
    if let Some(body) = body {
        let headers = request.headers_mut();
        headers.insert(
            header::CONTENT_TYPE,
            HeaderValue::from_static("application/json"),
        );
        let body = Body::from(body);
        headers.insert(
            header::CONTENT_LENGTH,
            body.as_bytes().unwrap().len().into(),
        );
        *request.body_mut() = Some(body);
    }
    if let Some(private_key) = private_key {
        let headers = request.headers_mut();
        let key = parse_secret_key(private_key)?;
        let pubkey_str = stringify_public_key_from_secret_key(&key);
        headers.insert("worker-pubkey", HeaderValue::from_str(&pubkey_str)?);
    }
    Ok(request)
}

pub async fn report_version(
    client: &HttpClient,
    arranger_url: &str,
    private_key: &str,
) -> Result<()> {
    let version_url = format!("{}/api/worker/version_report", arranger_url);
    let request = create_json_request_wiht_private_key(
        Method::POST,
        &version_url,
        Some(serde_json::to_string(&WorkerVersionReportRequest {
            version: env!("CARGO_PKG_VERSION").to_string(),
        })?),
        Some(private_key),
    )?;
    let _ = client.request(request).await?;
    Ok(())
}
#[cfg(target_os = "linux")]
pub async fn check_upgrade(
    client: &HttpClient,
    arranger_url: &str,
    private_key: &str,
) -> Result<Option<WorkerVersionInfo>> {
    let version_url = format!("{}/api/worker/version", arranger_url);

    let Ok(request) =
        create_json_request_wiht_private_key(Method::GET, &version_url, None, Some(private_key))
    else {
        log::error!("create_json_request_wiht_private_key failed");
        return Ok(None);
    };
    let version_info: WorkerVersionInfo = match client.request(request).await?.json().await {
        Ok(version_info) => version_info,
        Err(e) => {
            log::error!("get version_info failed: {:?}", e);
            return Ok(None);
        }
    };
    log::info!("get version_info: {:?}", version_info);
    // 比较版本号，如果有新版本则返回版本信息
    if version_info.version != env!("CARGO_PKG_VERSION") {
        Ok(Some(version_info))
    } else {
        Ok(None)
    }
}
#[cfg(not(target_os = "linux"))]
pub async fn check_upgrade(
    _client: &HttpClient,
    _arranger_url: &str,
    _private_key: &str,
) -> Result<Option<WorkerVersionInfo>> {
    Ok(None)
}
#[cfg(target_os = "linux")]
pub async fn upgrade_from_zfc_to_zf() -> Result<()> {
    use std::fs;
    let current_exe = std::env::current_exe()?;
    if current_exe
        .file_name()
        .unwrap()
        .to_str()
        .unwrap()
        .contains("zfc")
    {
        // walk through the parent directory and rename all file name with zfc to zf
        let mut entries = tokio_fs::read_dir(current_exe.parent().unwrap()).await?;
        while let Some(entry) = entries.next_entry().await? {
            if entry.path().is_symlink() {
                tokio_fs::remove_file(&entry.path()).await?;
                continue;
            }
            let path = entry.path();
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                let new_file_name = file_name.replace("zfc", "zf");
                let rename = path.with_file_name(new_file_name);
                tokio_fs::rename(&path, &rename).await?;
            }
        }

        // create a new symlink to the new version
        // 创建或更新软链接
        let symlink = current_exe.parent().unwrap().join("zf-worker.new");
        if symlink.exists() {
            tokio_fs::remove_file(&symlink).await?;
        }
        tokio_fs::symlink(&current_exe, &symlink).await?;

        // rename directory name
        let current_dir = current_exe.parent().unwrap();
        let new_dir = current_dir.with_file_name(
            current_dir
                .file_name()
                .unwrap()
                .to_str()
                .unwrap()
                .replace("zfc", "zf"),
        );
        tokio_fs::rename(current_dir, &new_dir).await?;

        // rename service name
        let service_path = format!("/etc/systemd/system/zfc.service");
        // modify the service file
        let service_content = fs::read_to_string(&service_path)?;
        let new_service_content = service_content.replace("zfc", "zf");
        tokio_fs::write(&service_path, new_service_content).await?;
        // rename the service name
        tokio_fs::rename(&service_path, &service_path.replace("zfc", "zf")).await?;
        tokio::process::Command::new("systemctl")
            .args(["daemon-reload"])
            .spawn()?;
        tokio::process::Command::new("systemctl")
            .args(["restart", "zf"])
            .spawn()?;
    }
    Ok(())
}
#[cfg(target_os = "linux")]
pub async fn download_and_upgrade(
    client: &HttpClient,
    version_info: &WorkerVersionInfo,
    private_key: &str,
) -> Result<bool> {
    use common::calculate_hash;
    use std::fs;
    let download_url = version_info
        .download_url
        .as_ref()
        .ok_or_else(|| anyhow!("No download URL provided"))?;

    // 获取当前执行文件路径
    let current_exe = std::env::current_exe()?;
    let current_dir = current_exe
        .parent()
        .ok_or_else(|| anyhow!("Cannot get parent directory"))?;

    // 使用新版本号作为文件名
    let new_exe = current_dir.join(format!("zf-worker-{}", version_info.version));

    // 下载新版本
    info!(
        "Downloading new version to {:?} download_url:{}",
        new_exe, download_url
    );
    let request =
        create_json_request_wiht_private_key(Method::GET, download_url, None, Some(private_key))?;
    let response = client.request(request).await?;
    let bytes = response.bytes().await?;
    let hash = calculate_hash(version_info.version.clone(), bytes.as_ref()).await?;
    if let Some(hash_val) = version_info.hash.as_ref() {
        if hash_val != &hash {
            return Err(anyhow!("Hash mismatch skip upgrade"));
        }
    }
    tokio_fs::write(&new_exe, bytes).await?;

    // 设置执行权限
    #[cfg(unix)]
    {
        use std::os::unix::fs::PermissionsExt;
        let mut perms = fs::metadata(&new_exe)?.permissions();
        perms.set_mode(0o755);
        fs::set_permissions(&new_exe, perms)?;
    }

    // 创建或更新软链接
    let symlink = current_dir.join("zf-worker.new");
    if symlink.exists() {
        tokio_fs::remove_file(&symlink).await?;
    }
    tokio_fs::symlink(&new_exe, &symlink).await?;

    #[cfg(target_os = "linux")]
    {
        // 获取服务名称
        let service_name = get_systemd_service_name()?;
        info!("Detected service name: {}", service_name);

        // 使用 systemctl 重启服务
        info!("Restarting service with systemctl");
        match Command::new("systemctl")
            .args(["restart", &service_name])
            .status()
        {
            Ok(status) if status.success() => {
                info!("Service restart command sent successfully");
                Ok(true)
            }
            Ok(status) => {
                warn!("Service restart failed with status: {}", status);
                Err(anyhow!("Failed to restart service: {}", status))
            }
            Err(e) => {
                warn!("Failed to execute systemctl: {}", e);
                Err(anyhow!("Failed to execute systemctl: {}", e))
            }
        }
    }
    #[cfg(not(target_os = "linux"))]
    {
        Ok(true)
    }
}

#[cfg(not(target_os = "linux"))]
pub async fn download_and_upgrade(
    _client: &HttpClient,
    _version_info: &WorkerVersionInfo,
    _private_key: &str,
) -> Result<bool> {
    Ok(false)
}

/// 获取当前进程所属的 systemd 服务名称
#[cfg(target_os = "linux")]
fn get_systemd_service_name() -> Result<String> {
    use std::fs;
    // 通过 /proc/self/cgroup 获取服务名称
    let cgroup_content = fs::read_to_string("/proc/self/cgroup")?;
    for line in cgroup_content.lines() {
        if line.contains("system") {
            if let Some(service) = line.split('/').last() {
                if service.ends_with(".service") {
                    return Ok(service.to_string());
                }
            }
        }
    }

    // 如果无法从 cgroup 获取，尝试从环境变量获取
    if let Ok(_unit) = std::env::var("INVOCATION_ID") {
        return Ok(format!("zf.service")); // 使用默认服务名
    }

    Err(anyhow!("Could not determine systemd service name"))
}

pub async fn cleanup_old_backups() -> Result<()> {
    #[cfg(target_os = "linux")]
    match upgrade_from_zfc_to_zf().await {
        Ok(_) => {}
        Err(e) => {
            warn!("Failed to upgrade from zfc to zf: {}", e);
        }
    }
    let current_exe = std::env::current_exe()?;
    let current_dir = current_exe
        .parent()
        .ok_or_else(|| anyhow!("Cannot get parent directory"))?;

    // 读取当前目录下所有的 worker 版本
    let mut entries = tokio_fs::read_dir(current_dir).await?;
    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();
        if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
            // 只清理旧版本的 worker 文件，保留当前正在使用的和最新的备份
            if file_name.starts_with("zf-worker-")
                && file_name != format!("zf-worker-{}", env!("CARGO_PKG_VERSION"))
            {
                info!("Cleaning up old version: {:?}", path);
                if let Err(e) = tokio_fs::remove_file(&path).await {
                    warn!("Failed to remove old version {:?}: {}", path, e);
                }
            }
        }
    }

    Ok(())
}

pub async fn get_latest_global_config(
    client: &HttpClient,
    arranger_url: &str,
    private_key: &str,
) -> Result<WorkerFetchGlobalLatestConfigResponse> {
    let url = format!("{}/api/worker/fetch_global_latest_config", arranger_url);

    let latest_config: WorkerFetchGlobalLatestConfigResponse = client
        .request(create_json_request_wiht_private_key(
            Method::GET,
            &url,
            None,
            Some(private_key),
        )?)
        .await?
        .json()
        .await?;

    // 比较版本号，如果有新版本则返回版本信息
    Ok(latest_config)
}

pub async fn update_outbound_proxy_addr(
    client: HttpClient,
    worker_id: u8,
    arranger_url: String,
    priv_key: String,
    config_path: PathBuf,
    current_proxy_config: Option<String>,
    file_lock: Arc<Mutex<()>>,
    global_exit_tx: Arc<tokio::sync::mpsc::Sender<()>>,
) -> Result<()> {
    let mut current_proxy_config = current_proxy_config;
    loop {
        match get_latest_global_config(&client, &arranger_url, &priv_key).await {
            Ok(latest_config) => {
                let use_forward_as_tun = latest_config.use_forward_as_tun.unwrap_or_default();
                if !use_forward_as_tun {
                    debug!("worker_id:{} use proxy as tun is disabled", worker_id);
                    tokio::time::sleep(std::time::Duration::from_secs(300)).await;
                    continue;
                }
                if latest_config.proxy_config != current_proxy_config {
                    current_proxy_config = latest_config.proxy_config.clone();
                    info!(
                        "worker_id:{} config updated, new proxy config: {:?}",
                        worker_id, current_proxy_config
                    );
                    // wirte to config file
                    let do_replace = async {
                        use tokio::io::AsyncWriteExt;
                        let _file_lock = file_lock.lock().await;
                        let old_content = tokio::fs::read_to_string(&config_path).await?;
                        let mut config: Config = serde_json::from_str(&old_content)?;
                        for backend in config.backends.iter_mut() {
                            if backend.mgmt_priv_key == priv_key {
                                backend.outbound_proxy_addr = current_proxy_config.clone();
                            }
                        }
                        tokio::fs::remove_file(&config_path).await?;
                        let mut file = tokio::fs::File::create_new(&config_path).await?;
                        file.write_all(serde_json::to_string(&config)?.as_bytes())
                            .await?;
                        Ok::<(), anyhow::Error>(())
                    };
                    if let Err(e) = do_replace.await {
                        error!("worker_id:{} Failed to replace config: {}", worker_id, e);
                    }
                    let _ = global_exit_tx.send(()).await;
                }
            }
            Err(e) => warn!(
                "worker_id:{} Failed to check for upgrades: {}",
                worker_id, e
            ),
        }
        tokio::time::sleep(std::time::Duration::from_secs(300)).await;
    }
}

pub async fn update_traffic_filter(
    client: HttpClient,
    worker_id: u8,
    arranger_url: String,
    priv_key: String,
    config_path: PathBuf,
    current_traffic_filter: Option<Vec<FilterType>>,
    file_lock: Arc<Mutex<()>>,
    global_exit_tx: Arc<tokio::sync::mpsc::Sender<()>>,
) -> Result<()> {
    let mut current_traffic_filter =
        HashSet::from_iter(current_traffic_filter.into_iter().flatten());
    loop {
        match get_latest_global_config(&client, &arranger_url, &priv_key).await {
            Ok(latest_config) => {
                let latest_filter_types =
                    if let Some(ref filter_strings) = latest_config.traffic_filter {
                        let mut filters = HashSet::new();
                        for filter_str in filter_strings {
                            match filter_str.as_str() {
                                "Http" => filters.insert(FilterType::Http),
                                "Socks5" => filters.insert(FilterType::Socks5),
                                "BitTorrent" => filters.insert(FilterType::BitTorrent),
                                "Tls" => filters.insert(FilterType::Tls),
                                _ => continue,
                            };
                        }
                        filters
                    } else {
                        HashSet::new()
                    };
                if latest_filter_types != current_traffic_filter {
                    current_traffic_filter = latest_filter_types.clone();
                    info!(
                        "worker_id:{} config updated, new traffic filter: {:?}",
                        worker_id, current_traffic_filter
                    );
                    // wirte to config file
                    let do_replace = async {
                        use tokio::io::AsyncWriteExt;
                        let _file_lock = file_lock.lock().await;
                        let old_content = tokio::fs::read_to_string(&config_path).await?;
                        let mut config: Config = serde_json::from_str(&old_content)?;
                        for backend in config.backends.iter_mut() {
                            if backend.mgmt_priv_key == priv_key {
                                if current_traffic_filter.len() > 0 {
                                    backend.traffic_filter = Some(current_traffic_filter.iter().cloned().collect());
                                } else {
                                    backend.traffic_filter = None;
                                }
                            }
                        }
                        tokio::fs::remove_file(&config_path).await?;
                        let mut file = tokio::fs::File::create_new(&config_path).await?;
                        file.write_all(serde_json::to_string(&config)?.as_bytes())
                            .await?;
                        Ok::<(), anyhow::Error>(())
                    };
                    if let Err(e) = do_replace.await {
                        error!("worker_id:{} Failed to replace config: {}", worker_id, e);
                    }
                    let _ = global_exit_tx.send(()).await;
                }
            }
            Err(e) => warn!(
                "worker_id:{} Failed to check for upgrades: {}",
                worker_id, e
            ),
        }
        tokio::time::sleep(std::time::Duration::from_secs(300)).await;
    }
}
