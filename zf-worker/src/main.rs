use crate::controller::start_controller;
use crate::http_client::HttpClient;
use crate::preludes::*;
use anyhow::{anyhow, Context};
use backend_transport::BackendTransport;
use base64::Engine;
use collector_factory::CollectorFactory;
use common::dotenv::dotenv;
use dashmap::DashMap;
use env_logger::Env;
#[cfg(feature = "audit_log")]
use log_reporter::LogClient;
use platform::{get_port_to_flow_map, platform_init, PortToFlowMap};
use private_tun::create_runtime;
use private_tun::runtime_provider::RuntimeProvider;
use private_tun::snell_impl_ver::config::ClientConfig;
use protocol::hammer::{
    setup_for_worker_proxy, HammerCtx, HammerHandlerForWorkerProxy, HammerProtocol,
};
use protocol::tot::{TotCtx, TotProtocol};
use protocol::ProtocolManager;
use reqwest::Url;
use stats::StatsCollector;
use std::net::{IpAddr, SocketAddr};
use std::sync::{Arc, Mutex as StdMutex};
use tokio::signal;
use tokio::sync::Semaphore;
use tokio::task::JoinHandle;
use types::TargetData;
mod backend_transport;
mod collector_factory;
mod controller;
// mod env;
mod conn_limitter;
mod constant;
mod http_client;
mod latency;
#[cfg(feature = "audit_log")]
mod log_reporter;
mod platform;
mod preludes;
mod protocol;
mod server;
mod stats;
mod types;
mod udp;
mod upgrade;
mod zero_copy_wrapper;

async fn start_one_controller(
    idx: u8,
    global_exit_tx: Arc<mpsc::Sender<()>>,
    port_to_flow_map: Arc<PortToFlowMap>,
    config: &BackendConfig,
    common_config: &Config,
    check_upgrade_tx: Arc<mpsc::Sender<()>>,
    hammer_proxyer: Option<Arc<HammerHandlerForWorkerProxy>>,
    hammer_ctx: HammerCtx,
    tot_ctx: TotCtx,
    ping_client: Arc<PingClient>,
    stats: Option<Arc<StdMutex<StatsCollector>>>,
    io_runtime: Arc<RuntimeProvider>,
    client: HttpClient,
    conn_sem: Arc<Semaphore>,
) -> anyhow::Result<JoinHandle<anyhow::Result<()>>> {
    if let Some(upgrade_url) = common_config.mgmt_upgrade_url.clone() {
        let mgmt_priv_key = config.mgmt_priv_key.clone();
        let client_clone = client.clone();
        tokio::spawn(async move {
            loop {
                if let Err(e) =
                    upgrade::report_version(&client_clone, &upgrade_url, &mgmt_priv_key).await
                {
                    error!("Failed to report version: {}", e);
                    tokio::time::sleep(Duration::from_secs(10)).await;
                } else {
                    break;
                }
            }
        });
    }
    let bind_addr: Option<SocketAddr> = config
        .bind_addr
        .map(|x| (x, 0).into())
        .or_else(|| common_config.bind_addr.map(|x| (x, 0).into()));
    let out_ip_addr: Option<IpAddr> = config
        .out_ip_addr
        .or_else(|| common_config.out_ip_addr.map(|x| x));
    let remote_pubkey = parse_public_key(common_config.mgmt_arranger_public_key.as_str())?;
    // eBPF program loaded, now we should init the app itself.
    let backend: Option<BackendTransport> = BackendTransport::new_from_str(
        &config.outbound_proxy_addr,
        &config.outbound_proxy_auth,
        hammer_proxyer.clone(),
        &hammer_ctx,
        &tot_ctx,
    )
    .await;
    info!("backend: {backend:?}");

    let local_privkey = parse_secret_key(config.mgmt_priv_key.as_str())?;

    let port_config_map = Arc::new(DashMap::new());
    let port_traffic_map = Arc::new(DashMap::new());
    let forward_endpoint_traffic_map = Arc::new(DashMap::new());
    let (port_traffic_tx, mut port_traffic_rx) = tokio::sync::mpsc::channel::<LocalStatData>(256);
    let forward_endpoint_stat_map = forward_endpoint_traffic_map.clone();
    let port_traffic_map_clone = port_traffic_map.clone();
    let _forward_report_handle = tokio::spawn(async move {
        while let Some(stat) = port_traffic_rx.recv().await {
            if let Some(forward_endpoint) = stat.forward_endpoint {
                let mut entry = forward_endpoint_stat_map
                    .entry((stat.port, stat.client_ip, forward_endpoint))
                    .or_insert(0 as u64);
                *entry += stat.traffic;
            } else {
                let mut entry = port_traffic_map_clone
                    .entry((stat.port, stat.client_ip))
                    .or_insert(0);
                *entry += stat.traffic;
            }
        }
    });
    let hammer_manager = Arc::new(ProtocolManager::<HammerProtocol>::new(
        None,
        hammer_ctx.clone(),
    ));
    let collector_factory = Arc::new(CollectorFactory::new(
        port_traffic_tx,
        tokio::runtime::Handle::current(),
    ));
    #[cfg(feature = "audit_log")]
    let log_url = common_config
        .log_server_url
        .as_ref()
        .map(|x| x.clone())
        .unwrap_or_else(|| format!("https://log.luny60.top"));
    let ctx = WorkerAppContext {
        idx,
        iface: config.iface.clone(),
        tc_path: common_config.tc_path.clone(),
        mgmt_ws_endpoint: common_config.mgmt_ws_endpoint.clone(),
        port_to_flow_map,
        port_traffic_map,
        remote_pubkey,
        local_privkey,
        port_config_map,
        backend_transport: backend,
        mgmt_socks_proxy: common_config.mgmt_socks_proxy.clone(),
        bind_addr,
        out_ip_addr,
        global_exit_tx: global_exit_tx.clone(),
        stats,
        forward_endpoint_traffic_map,
        check_upgrade_tx,
        worker_proxyer: hammer_proxyer,
        hammer_manager,
        io_runtime,
        tot_manager: Arc::new(ProtocolManager::<TotProtocol>::new(None, tot_ctx)),
        ping_client,
        collector_factory,
        #[cfg(feature = "audit_log")]
        log_client: Arc::new(LogClient::new(
            client,
            log_url,
            &format!("zf-worker-{}", config.mgmt_priv_key),
        )),
        traffic_filter: config.traffic_filter.as_ref().map(|x| Arc::new(x.clone())),
        conn_sem,
    };
    let ctx = Arc::new(ctx);

    // let _ = setup_map(ctx.clone()).await?;
    let flow_handle = tokio::spawn(start_controller(ctx));
    Ok(flow_handle)
}

#[cfg(feature = "mem_prof")]
use leak_detect_allocator::LeakTracer;
#[cfg(feature = "mem_prof")]
#[global_allocator]
static LEAK_TRACER: LeakTracer<30> = LeakTracer::<30>::new();

async fn setup_hammer_proxy(
    mgmt_hammer_proxy: Option<&str>,
    mgmt_ws_endpoint: &str,
    _hammer_ctx: &HammerCtx,
) -> anyhow::Result<Option<Arc<HammerHandlerForWorkerProxy>>> {
    let hammer_proxyer = if let Some(hammer_config) = mgmt_hammer_proxy {
        if hammer_config.trim().is_empty() {
            None
        } else {
            let base64_hammer_config =
                base64::engine::general_purpose::STANDARD.decode(hammer_config)?;
            if let Ok(client_config) = serde_json::from_slice::<ClientConfig>(&base64_hammer_config)
            {
                let url =
                    Url::parse(mgmt_ws_endpoint).context("mgmt_ws_endpoint is not a valid url")?;
                let target_addr = match url.host() {
                    Some(host) => host.to_string(),
                    None => return Err(anyhow!("host is not set")),
                };
                let port = url
                    .port_or_known_default()
                    .expect("port cant't be resolved");
                let socket_addr: SocketAddr = match target_addr.parse::<IpAddr>() {
                    Ok(addr) => (addr, port).into(),
                    Err(_e) => match tokio::time::timeout(
                        Duration::from_secs(2),
                        tokio::net::lookup_host((target_addr.clone(), port)),
                    )
                    .await
                    {
                        Ok(Ok(addrs)) => addrs.into_iter().next().unwrap(),
                        Ok(Err(e)) => {
                            return Err(anyhow!("lookup host:{} failed: {}", target_addr, e))
                        }
                        Err(_e) => return Err(anyhow!("lookup host:{} timeout", target_addr)),
                    },
                };
                Some(Arc::new(
                    setup_for_worker_proxy(
                        client_config,
                        TargetData {
                            addr_list: vec![socket_addr],
                            mode: None,
                            latency_test_method: None,
                            sub_id: 0,
                            allow_ip_num: None,
                            allow_conn_num: None,
                        },
                    )
                    .await?,
                ))
            } else {
                None
            }
        }
    } else {
        None
    };
    Ok(hammer_proxyer)
}

async fn async_main(config: Config, opt: Opt) -> Result<()> {
    use futures_util::future::join_all;
    use protocol::Protocol;
    let io_runtime = Arc::new(RuntimeProvider::new(
        false,
        config.default_runtime.unwrap_or(false),
    )?);
    #[cfg(feature = "metric")]
    {
        use private_tun::snell_impl_ver::metrics::setup_runtime_reporter;
        setup_runtime_reporter(&io_runtime.get_rt(), "zf-worker-io-rt");
        setup_runtime_reporter(&tokio::runtime::Handle::current(), "zf-worker-main-rt");
    }
    platform_init(&config).await?;
    // 启动自动升级检查
    let (check_upgrade_tx, mut check_upgrade_rx) = tokio::sync::mpsc::channel(1);
    // 每小时检查一次
    let upgrade_check_interval = Duration::from_secs(3600);
    // using the first backend's private key to check upgrade
    let private_key = config
        .backends
        .iter()
        .next()
        .map(|x| x.mgmt_priv_key.clone())
        .expect("no local priv key");
    let conn_sem = Arc::new(Semaphore::new(config.conn_retry_sem_permits.unwrap_or(100)));

    let hammer_ctx = HammerProtocol::init_ctx(&HammerProtocol, io_runtime.clone());
    let tot_ctx = TotCtx::new(hammer_ctx.clone());
    let hammer_proxyer = match setup_hammer_proxy(
        config.mgmt_hammer_proxy.as_ref().map(|x| x.as_str()),
        &config.mgmt_ws_endpoint,
        &hammer_ctx,
    )
    .await
    {
        Ok(hammer_proxyer) => hammer_proxyer,
        Err(e) => {
            error!("setup_hammer_proxy error: {e}");
            return Err(e);
        }
    };

    let global_backend = if hammer_proxyer.is_some() {
        BackendTransport::new_from_str(
            &Some("use_proxy".to_string()),
            &None,
            hammer_proxyer.clone(),
            &hammer_ctx,
            &tot_ctx,
        )
        .await
    } else if let Some(_socks_proxy) = config.mgmt_socks_proxy.as_ref() {
        BackendTransport::new_from_str(
            &config.mgmt_socks_proxy,
            &config.mgmt_socks_proxy_auth,
            hammer_proxyer.clone(),
            &hammer_ctx,
            &tot_ctx,
        )
        .await
    } else {
        None
    };
    log::info!("global_backend: {global_backend:?}");
    let client = http_client::HttpClient::new(global_backend)?;

    let (global_exit_tx, mut global_exit_rx) = mpsc::channel(1);
    let global_exit_tx = Arc::new(global_exit_tx);
    let file_lock = Arc::new(Mutex::new(()));
    if let Some(upgrade_url) = config.mgmt_upgrade_url.clone() {
        let upgrade_url_for_global_config = upgrade_url.clone();
        let private_key_for_global_config = private_key.clone();
        let client_clone = client.clone();
        tokio::spawn(async move {
            loop {
                match upgrade::check_upgrade(&client_clone, &upgrade_url, &private_key).await {
                    Ok(Some(version_info)) => {
                        info!("New version {} available", version_info.version);
                        match upgrade::download_and_upgrade(
                            &client_clone,
                            &version_info,
                            &private_key,
                        )
                        .await
                        {
                            Ok(true) => info!("Upgrade successful"),
                            Ok(false) => warn!("Upgrade skipped"),
                            Err(e) => warn!("Upgrade failed: {}", e),
                        }
                    }
                    Ok(None) => debug!("No upgrade available"),
                    Err(e) => warn!("Failed to check for upgrades: {}", e),
                }
                tokio::select! {
                    _ = check_upgrade_rx.recv() => {
                    }
                    _ = tokio::time::sleep(upgrade_check_interval) => {
                    }
                };
            }
        });
        // check global config current only for update hammer proxy config
        if let Some(current_proxy_config) = config.mgmt_hammer_proxy.as_ref() {
            if !current_proxy_config.trim().is_empty() {
                let mut current_proxy_config = config.mgmt_hammer_proxy.clone();
                let config_path = opt.config_path.clone();
                let file_lock_clone = file_lock.clone();
                let global_exit_tx_clone = global_exit_tx.clone();
                let client_clone = client.clone();
                tokio::spawn(async move {
                    loop {
                        match upgrade::get_latest_global_config(
                            &client_clone,
                            &upgrade_url_for_global_config,
                            &private_key_for_global_config,
                        )
                        .await
                        {
                            Ok(latest_config) => {
                                if latest_config.proxy_config != current_proxy_config {
                                    current_proxy_config = latest_config.proxy_config.clone();
                                    info!(
                                        "Global config updated, new proxy config: {:?}",
                                        current_proxy_config
                                    );
                                    // wirte to config file
                                    let do_replace = async {
                                        use tokio::io::AsyncWriteExt;
                                        let _file_lock = file_lock_clone.lock().await;
                                        let old_content =
                                            tokio::fs::read_to_string(&config_path).await?;
                                        let mut config: Config =
                                            serde_json::from_str(&old_content)?;
                                        config.mgmt_hammer_proxy = current_proxy_config.clone();
                                        tokio::fs::remove_file(&config_path).await?;
                                        let mut file =
                                            tokio::fs::File::create_new(&config_path).await?;
                                        file.write_all(serde_json::to_string(&config)?.as_bytes())
                                            .await?;
                                        Ok::<(), anyhow::Error>(())
                                    };
                                    if let Err(e) = do_replace.await {
                                        error!("Failed to replace config: {}", e);
                                    }
                                    let _ = global_exit_tx_clone.send(()).await;
                                }
                            }
                            Err(e) => warn!("Failed to check for upgrades: {}", e),
                        }
                        tokio::time::sleep(std::time::Duration::from_secs(300)).await;
                    }
                });
            }
        }
    }

    // 清理旧的备份文件
    if let Err(e) = upgrade::cleanup_old_backups().await {
        warn!("Failed to cleanup old backups: {}", e);
    }

    let port_to_flow_map = Arc::new(get_port_to_flow_map()?);
    let mut tasks = vec![];

    let check_upgrade_tx = Arc::new(check_upgrade_tx);
    let ping_client = Arc::new(PingClient::new());
    let stats_collector = match config.stats.as_ref() {
        Some(stats_config) => {
            if stats_config.enable {
                Some(Arc::new(StdMutex::new(StatsCollector::new(
                    Duration::from_secs(stats_config.report_interval.map_or(3, |x| x as u64)),
                ))))
            } else {
                None
            }
        }
        _ => None,
    };
    for (idx, cfg) in config.backends.iter().enumerate() {
        if let Some(upgrade_url) = config.mgmt_upgrade_url.clone() {
            let need_update = {
                // 如果是配置的use_proxy，或者socks5代理，这种属于固定配置，则不更新
                cfg.outbound_proxy_addr
                    .as_ref()
                    .filter(|x| {
                        *x == "use_proxy"
                            || *x == "direct"
                            || x.as_str().trim().parse::<SocketAddr>().is_ok()
                    })
                    .is_none()
            };
            if need_update {
                tokio::spawn(upgrade::update_outbound_proxy_addr(
                    client.clone(),
                    idx as u8,
                    upgrade_url.clone(),
                    cfg.mgmt_priv_key.clone(),
                    opt.config_path.clone(),
                    cfg.outbound_proxy_addr.clone(),
                    file_lock.clone(),
                    global_exit_tx.clone(),
                ));
            }

            tokio::spawn(upgrade::update_traffic_filter(
                client.clone(),
                idx as u8,
                upgrade_url,
                cfg.mgmt_priv_key.clone(),
                opt.config_path.clone(),
                cfg.traffic_filter.clone(),
                file_lock.clone(),
                global_exit_tx.clone(),
            ));
        }
        tasks.push(
            match start_one_controller(
                idx as u8,
                global_exit_tx.clone(),
                port_to_flow_map.clone(),
                cfg,
                &config,
                check_upgrade_tx.clone(),
                hammer_proxyer.clone(),
                hammer_ctx.clone(),
                tot_ctx.clone(),
                ping_client.clone(),
                stats_collector.clone(),
                io_runtime.clone(),
                client.clone(),
                conn_sem.clone(),
            )
            .await
            {
                Ok(handle) => handle,
                Err(e) => {
                    error!("start_one_controller error: {e}");
                    continue;
                }
            },
        );
        info!("setup config: {cfg:?} success");
    }
    drop(config);
    drop(opt);
    // register process exit handler
    let (handler_tx, mut handler_rx) = mpsc::channel(1);
    tokio::spawn(async move {
        let mut sigterm =
            tokio::signal::unix::signal(tokio::signal::unix::SignalKind::terminate()).unwrap();
        let mut sigint =
            tokio::signal::unix::signal(tokio::signal::unix::SignalKind::interrupt()).unwrap();
        tokio::select! {
            _ = sigterm.recv() => {},
            _ = sigint.recv() => {},
        }
        log::info!("Received exit signal");
        let _ = handler_tx.send(());
    });
    #[cfg(feature = "mem_prof")]
    tokio::spawn(async move {
        use std::io::Write;
        tokio::time::sleep(Duration::from_secs(120)).await;
        LEAK_TRACER.init();
        let mut sample_count = 0;
        loop {
            tokio::time::sleep(Duration::from_secs(120)).await;
            // let mut out = String::new();
            let mut count = 0;
            let mut count_size = 0;
            let mut file = std::fs::File::create(format!("foo_{sample_count}.txt")).unwrap();
            LEAK_TRACER.now_leaks(|addr, size, frames| {
                count += 1;
                // let mut it = frames.iter();
                // first is the alloc size
                count_size += size;
                // out += &format!("leak memory address: {:#x}, size: {}\r\n", addr, size);
                file.write_all(
                    format!("leak memory address: {:#x}, size: {}\r\n", addr, size).as_bytes(),
                )
                .unwrap();
                for f in frames {
                    // Resolve this instruction pointer to a symbol name
                    let sym_name = match LEAK_TRACER.get_symbol_name(*f) {
                        Some(s) => rustc_demangle::demangle(s.as_str()).to_string(),
                        None => "".to_owned(),
                    };
                    file.write_all(format!("\t{:#x} {}\r\n", f, sym_name).as_bytes())
                        .unwrap();
                }
                true // continue until end
            });
            sample_count += 1;
            file.write_all(
                format!("\r\ntotal address:{}, bytes:{}\r\n", count, count_size).as_bytes(),
            )
            .unwrap();
        }
    });
    tokio::select! {
      rets = join_all(tasks.into_iter()) => {
        for ret in rets {
          match ret {
            Ok(ret) => {
              if let Err(e) = ret {
                error!("flow_handle: {e}")
              }
            }
            Err(e) => error!("flow_handle: {e}")
          }
        }
      }
      ret = signal::ctrl_c() => {
        if let Err(e) = ret {
          error!("ctrl_c: {e}")
        }
      },
      _ = global_exit_rx.recv() => {
        info!("exit by self check");
      }
      _ = handler_rx.recv() => {
        info!("exit by signal");
      }
    }

    info!("🐦");
    Ok(())
}

#[cfg(feature = "metric")]
async fn setup_metrics_for_worker() -> Result<()> {
    use private_tun::snell_impl_ver::metrics::{setup_metrics, MeasureItem};
    use private_tun::snell_impl_ver::relayer::{get_task_monitor_recv, get_task_monitor_send};
    use server::get_task_monitor_tcp_realm;
    let items = vec![
        MeasureItem::new("realm_copy".to_string(), get_task_monitor_tcp_realm()),
        MeasureItem::new("zf-relayer-recv".to_string(), get_task_monitor_recv()),
        MeasureItem::new("zf-relayer-send".to_string(), get_task_monitor_send()),
    ];
    setup_metrics(items).await
}
fn main() -> Result<()> {
    dotenv().ok();
    env_logger::Builder::from_env(Env::default().default_filter_or("info")).init();
    let opt = Opt::parse();
    let config: Config = serde_json::from_str(&std::fs::read_to_string(&opt.config_path)?)?;
    let rt = create_runtime(
        config.force_single_rt.unwrap_or(true),
        config.default_runtime.unwrap_or(false),
    )?;

    #[cfg(feature = "metric")]
    rt.block_on(setup_metrics_for_worker())?;
    match rt.block_on(async_main(config, opt)) {
        Ok(()) => Ok(()),
        Err(e) => {
            error!("async_main error: {e}");
            Err(e)
        }
    }
}
