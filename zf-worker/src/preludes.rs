pub use anyhow::Result;
// use aya::maps::{HashMap, MapData};
pub use clap::Parser;
pub use common::crypto::*;
pub use common::*;
pub use common::{
    app_message::*,
    crypto::k256::{<PERSON><PERSON><PERSON>, <PERSON>Key},
};
use dashmap::DashMap;
pub use log::{debug, error, info, warn};
use private_tun::runtime_provider::RuntimeProvider;
use private_tun::snell_impl_ver::traffic_filter::{
    BitTorrentFilter, Filter, HttpMethodFilter, Socks5CommandFilter, TlsFilter,
};
use std::collections::HashSet;
use std::net::{IpAddr, SocketAddr};
use std::path::PathBuf;
pub use std::sync::{Arc, Mutex as StdMutex};
use tokio::sync::Semaphore;
pub use tokio::sync::{mpsc, Mutex};
use tokio::task::Join<PERSON>and<PERSON>;
pub use tokio::time::{sleep, Duration};
use tokio_util::sync::CancellationToken;

// #[allow(warnings)]
// use tobaru::{tcp::TargetData as TcpTargetData, udp::UdpTargetData};

use crate::backend_transport::BackendTransport;
use crate::collector_factory::CollectorFactory;
#[cfg(feature = "audit_log")]
use crate::log_reporter::LogClient;
use crate::platform::PortToFlowMap;
use crate::protocol::hammer::{HammerHandlerForWorkerProxy, HammerProtocol};
use crate::protocol::tot::TotProtocol;
use crate::protocol::ProtocolManager;
use crate::stats::StatsCollector;

#[derive(Debug, Parser, Clone)]
pub struct Opt {
    #[clap(short, long, env, default_value = "config.json")]
    pub config_path: PathBuf,
}

pub type GlobalPortToFlowMap = Arc<PortToFlowMap>;
pub type HammerProtocolManager = Arc<ProtocolManager<HammerProtocol>>;
pub type TotProtocolManager = Arc<ProtocolManager<TotProtocol>>;

#[derive(Debug, Clone)]
pub struct LocalStatData {
    pub port: u16,
    pub client_ip: Option<SocketAddr>,
    pub forward_endpoint: Option<Arc<Box<str>>>,
    pub traffic: u64,
}

pub struct LocalServer {
    pub tcp_listen_handle: JoinHandle<Result<()>>,
    pub udp_listen_handle: JoinHandle<Result<()>>,
    pub config: PortConfig,
    pub cancel: CancellationToken,
}
pub struct WorkerAppContext {
    // pub opt: Opt,
    pub idx: u8,
    pub iface: String,
    #[allow(unused)]
    pub tc_path: String,
    pub mgmt_ws_endpoint: String,
    pub mgmt_socks_proxy: Option<String>,
    pub port_to_flow_map: GlobalPortToFlowMap,
    pub local_privkey: SecretKey,
    pub remote_pubkey: PublicKey,
    pub port_config_map: Arc<DashMap<u16, Arc<LocalServer>>>,
    pub backend_transport: Option<BackendTransport>,
    pub bind_addr: Option<SocketAddr>,
    pub out_ip_addr: Option<IpAddr>,
    pub global_exit_tx: Arc<mpsc::Sender<()>>,
    pub stats: Option<Arc<StdMutex<StatsCollector>>>,
    pub hammer_manager: HammerProtocolManager,
    pub tot_manager: TotProtocolManager,
    pub forward_endpoint_traffic_map: Arc<DashMap<(u16, Option<SocketAddr>, Arc<Box<str>>), u64>>, // (port, client_ip, forward_endpoint_name) -> traffic
    pub port_traffic_map: Arc<DashMap<(u16, Option<SocketAddr>), u64>>, // (port, client_ip) -> traffic
    pub check_upgrade_tx: Arc<mpsc::Sender<()>>,
    pub worker_proxyer: Option<Arc<HammerHandlerForWorkerProxy>>,
    pub ping_client: Arc<PingClient>,
    pub collector_factory: Arc<CollectorFactory>,
    #[cfg(feature = "audit_log")]
    pub log_client: Arc<LogClient>,
    pub traffic_filter: Option<Arc<Vec<FilterType>>>,
    pub io_runtime: Arc<RuntimeProvider>,
    pub conn_sem: Arc<Semaphore>, // global semaphore to limit the number of connections
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct StatsConfig {
    pub enable: bool,
    pub report_interval: Option<u32>, // unit: second
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct Config {
    pub tc_path: String,
    pub mgmt_arranger_public_key: String,
    pub mgmt_ws_endpoint: String,
    pub mgmt_upgrade_url: Option<String>,
    pub mgmt_socks_proxy: Option<String>,
    pub mgmt_socks_proxy_auth: Option<String>,
    pub mgmt_hammer_proxy: Option<String>,
    pub backends: Vec<BackendConfig>,
    pub bind_addr: Option<IpAddr>,
    pub out_ip_addr: Option<IpAddr>,
    pub stats: Option<StatsConfig>,
    pub log_server_url: Option<String>,
    pub force_single_rt: Option<bool>,
    pub default_runtime: Option<bool>,
    pub conn_retry_sem_permits: Option<usize>,
}

impl Config {
    #[allow(unused)]
    pub fn unique_iface(&self) -> HashSet<String> {
        self.backends
            .iter()
            .map(|x| x.iface.trim().to_owned())
            .collect()
    }
}

#[derive(Debug, serde::Serialize, serde::Deserialize, Clone, Copy, PartialEq, Eq, Hash)]
pub enum FilterType {
    Http,
    Socks5,
    BitTorrent,
    Tls,
}

impl Into<Box<dyn Filter + Send + Sync + 'static>> for &FilterType {
    fn into(self) -> Box<dyn Filter + Send + Sync + 'static> {
        match self {
            FilterType::Http => Box::new(HttpMethodFilter::any()),
            FilterType::Socks5 => Box::new(Socks5CommandFilter::any()),
            FilterType::BitTorrent => Box::new(BitTorrentFilter),
            FilterType::Tls => Box::new(TlsFilter),
        }
    }
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct BackendConfig {
    pub iface: String,
    pub mgmt_priv_key: String,
    pub port_start: u16,
    pub port_end: u16,
    pub bind_addr: Option<IpAddr>,
    pub out_ip_addr: Option<IpAddr>,
    pub outbound_proxy_addr: Option<String>,
    pub outbound_proxy_auth: Option<String>,
    pub forward_server_port_range: Option<(u16, u16)>, // [start, end]
    pub traffic_filter: Option<Vec<FilterType>>,
}
