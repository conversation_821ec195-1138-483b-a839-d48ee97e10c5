use std::{
    fmt::{Debug, Display},
    hash::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>},
    net::{IpAdd<PERSON>, SocketAddr},
    sync::Arc,
};

use anyhow::Context as AnyhowContext;
use common::{
    app_message::{LatencyTestMethod, Mode},
    TotConfig as TotConfigInner,
};
use futures::future::{try_join, BoxFuture};
use io_adaptor::{TotBackendIO, TotInputIO, TotStream};
use private_tun::{
    address::Address,
    runtime_provider::RuntimeProvider,
    self_proxy::MemDuplex,
    snell_impl_ver::{
        client_run::ProxyReportError, client_zfc::ConnType,
        config::ClientConfig as HammerClientConfig,
    },
    traffic_collector::RealtimeTrafficCollector,
};
use serde::{Deserialize, Serialize};
use session::{setup_fwd_tot_server, stop_fwd_tot_server, Tot<PERSON>ession, TotSessionManager};
use smallvec::smallvec;
use tcp_over_multi_tcp_client::run_client;
use tokio::{
    io::DuplexStream,
    net::TcpStream,
    sync::{RwLock, Semaphore},
};
use tokio_util::sync::CancellationToken;

use crate::{
    collector_factory::CollectorFactory,
    conn_limitter::ConnLimitter,
    preludes::{FilterType, PingClient},
    protocol::{hammer::create_server_selector_hammer_full_link, ProtocolManager},
    types::TargetData,
};

#[cfg(feature = "audit_log")]
use crate::log_reporter::LogClient;

use super::{
    hammer::{start_hammer_client, HammerClient, HammerCtx, ServerSelectorForTotal},
    CustomPipedStream, PipedStreamCreator, Protocol, ProtocolHandler, ProtocolHandlerFactory,
};
pub mod io_adaptor;
pub mod latency_tester;
pub mod session;
pub struct TotProtocol {
    pub ctx: HammerCtx,
}

pub struct TotClient {
    pub fwd_handler: Arc<HammerHandlerForTot>,
    pub tot_server_handler: Arc<HammerClient>,
    pub config: Arc<TotClientConfigWrapper>,
    pub ctx: TotCtx,
}

pub struct TotHandler {
    stream_tx: tokio::sync::mpsc::Sender<TotStream>,
    session: Option<Arc<TotSession>>,
    bind_port: u16,
    // for udp
    udp_handler: Arc<Box<dyn ProtocolHandler>>,
}

impl Drop for TotHandler {
    fn drop(&mut self) {
        if let Some(session) = self.session.take() {
            tokio::spawn(async move {
                match stop_fwd_tot_server(
                    &session.hammer_handler,
                    session.fwd_hammer_handler.clone(),
                    &session.target_list,
                    session.mode,
                    session.latency_test_method,
                    &*session.mapper.read().await,
                )
                .await
                {
                    Ok(_) => (),
                    Err(e) => log::error!("stop_fwd_tot_server failed: {}", e),
                }
            });
        }
    }
}

#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub struct TotId {
    pub hash: u64,
}

#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub struct TotIdAndTarget {
    pub id: TotId,
    pub target_list: Vec<SocketAddr>,
    pub mode: Option<Mode>,
    pub latency_test_method: Option<LatencyTestMethod>,
}

#[derive(Debug, Clone, Hash, Serialize, Deserialize)]
pub enum TotForwarderConfig {
    Direct(Address),
    Hammer(HammerClientConfig),
}

#[derive(Debug)]
pub struct TotClientConfigWrapper(TotConfigInner);

impl From<TotConfigInner> for TotClientConfigWrapper {
    fn from(value: TotConfigInner) -> Self {
        Self(value)
    }
}

impl TryFrom<&common::app_message::Protocol> for TotClientConfigWrapper {
    type Error = anyhow::Error;
    fn try_from(value: &common::app_message::Protocol) -> Result<Self, Self::Error> {
        let config = match value {
            common::app_message::Protocol::Tot { config } => config,
            _ => return Err(anyhow::anyhow!("invalid protocol")),
        };
        let config = serde_json::from_str::<TotConfigInner>(config)?;
        Ok(Self(config))
    }
}

impl From<(&TotClientConfigWrapper, &TargetData)> for TotIdAndTarget {
    fn from((config, target): (&TotClientConfigWrapper, &TargetData)) -> Self {
        let id = TotId::from(config);
        Self {
            id,
            target_list: target.addr_list.clone(),
            mode: target.mode.clone(),
            latency_test_method: target.latency_test_method.clone(),
        }
    }
}

impl From<&TotClientConfigWrapper> for TotId {
    fn from(config: &TotClientConfigWrapper) -> Self {
        let mut hasher = DefaultHasher::new();
        config.0.hash(&mut hasher);
        Self {
            hash: hasher.finish(),
        }
    }
}

impl Display for TotIdAndTarget {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "tot:{}-{:?}-{:?}-{:?}",
            self.id, self.target_list, self.mode, self.latency_test_method
        )
    }
}

impl Display for TotId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.hash)
    }
}

#[derive(Clone)]
pub struct TotCtx {
    hammer_ctx: HammerCtx,
    tot_session_manager: Arc<TotSessionManager>,
}

impl TotCtx {
    pub fn new(hammer_ctx: HammerCtx) -> Self {
        Self {
            hammer_ctx,
            tot_session_manager: Arc::new(TotSessionManager::new()),
        }
    }
    pub fn tot_session_manager_ref(&self) -> &TotSessionManager {
        &self.tot_session_manager
    }
}

#[async_trait::async_trait]
impl Protocol for TotProtocol {
    type Client = Arc<TotClient>;
    type Config = TotClientConfigWrapper;
    type Id = TotId;
    type Ctx = TotCtx;
    const NEED_PORT: bool = false;
    fn init_ctx(&self, _runtime_provider: Arc<RuntimeProvider>) -> Self::Ctx {
        let tot_session_manager = TotSessionManager::new();
        Self::Ctx {
            hammer_ctx: self.ctx.clone(),
            tot_session_manager: Arc::new(tot_session_manager),
        }
    }
    async fn start(
        ctx: &Self::Ctx,
        _manager: &ProtocolManager<Self>,
        _id: &Self::Id,
        config: &Self::Config,
    ) -> anyhow::Result<Self::Client> {
        let hammer_handler =
            HammerHandlerForTot::create(&ctx.hammer_ctx, config.0.fwd_config.clone());
        let server_hammer_client = start_hammer_client(
            ctx.hammer_ctx.rt_provider.clone(),
            config.0.tot_server_list.clone(),
            "for_tot_server".to_string(),
            false,
        );
        let (hammer_handler, server_hammer_client) =
            try_join(hammer_handler, server_hammer_client).await?;

        Ok(Arc::new(TotClient {
            fwd_handler: Arc::new(hammer_handler),
            tot_server_handler: Arc::new(server_hammer_client),
            config: Arc::new(TotClientConfigWrapper(config.0.clone())),
            ctx: ctx.clone(),
        }))
    }
    async fn stop(ctx: &Self::Ctx, client: Self::Client) -> anyhow::Result<()> {
        // let tot_session_manager = ctx.tot_session_manager.clone();
        let tot_hammer_client = client.tot_server_handler.clone();
        let _ = ctx
            .tot_session_manager
            .stop_related_session(&tot_hammer_client, &client.fwd_handler)
            .await;
        client.tot_server_handler.cancel_token.cancel();
        client.fwd_handler.client.cancel_token.cancel();
        Ok(())
    }
    async fn clear_used_port(
        _ctx: &Self::Ctx,
        _manager: &ProtocolManager<Self>,
        _config: &Self::Config,
    ) -> Option<u16> {
        None
    }
    fn config_from_client<'a>(_ctx: &Self::Ctx, client: &'a Self::Client) -> &'a Self::Config {
        &client.config
    }
}

#[async_trait::async_trait]
impl ProtocolHandlerFactory for TotClient {
    type Protocol = TotProtocol;
    async fn create_protocol_handler(
        &self,
        bind_port: u16,
        target: TargetData,
        collector_factory: Arc<CollectorFactory>,
        ping_client: Arc<PingClient>,
        port_cancel: CancellationToken,
        _piped_stream_creator: Option<PipedStreamCreator>,
        rt_provider: Arc<RuntimeProvider>,
        conn_sem: Arc<Semaphore>,
    ) -> anyhow::Result<Arc<Box<dyn ProtocolHandler>>> {
        log::debug!(
            "tot create protocol handler, target:{:?}, port: {}",
            target,
            bind_port
        );
        let (accept_tx, accept_rx) = tokio::sync::mpsc::channel(32);
        let input_io = TotInputIO {
            accept_rx,
            cancel: port_cancel.clone(),
        };

        let target_list = target
            .addr_list
            .iter()
            .map(|addr| Address::Socket(addr.clone()))
            .collect::<Vec<_>>();

        let tot_id: TotIdAndTarget = (self.config.as_ref(), &target).into();
        let session = match self
            .ctx
            .tot_session_manager
            .get_session_by_id(&tot_id)
            .await
        {
            Some(session) => {
                log::info!("tot session already exists for port: {}", bind_port);
                session
            }
            None => {
                log::debug!(
                    "tot session not exists, create new session for port: {}",
                    bind_port
                );
                let fwd_to_server_mapper = match setup_fwd_tot_server(
                    &self.tot_server_handler,
                    self.fwd_handler.clone(),
                    &target_list,
                    target.mode.unwrap_or_default(),
                    target.latency_test_method.unwrap_or_default(),
                )
                .await
                .context("setup_fwd_tot_server failed")
                {
                    Ok(mapper) => mapper,
                    Err(e) => {
                        log::error!("setup_fwd_tot_server failed: {} for port: {}", e, bind_port);
                        return Err(e);
                    }
                };
                log::debug!(
                    "setup_fwd_tot_server, target_list: {:?}, mode: {:?}, latency_test_method: {:?} success for port: {}",
                    target_list,
                    target.mode.unwrap_or_default(),
                    target.latency_test_method.unwrap_or_default(),
                    bind_port
                );
                let mapper = Arc::new(RwLock::new(fwd_to_server_mapper));
                let session = Arc::new(TotSession::new(
                    mapper,
                    self.tot_server_handler.clone(),
                    self.fwd_handler.clone(),
                    target_list.clone(),
                    target.mode.unwrap_or_default(),
                    target.latency_test_method.unwrap_or_default(),
                ));

                self.ctx
                    .tot_session_manager
                    .insert_session(tot_id, session.clone())
                    .await;
                session
            }
        };
        let collector_factory_clone = collector_factory.clone();
        let backend_io = match TotBackendIO::create(
            session.mapper.clone(),
            ping_client.clone(),
            self.fwd_handler.clone(),
            self.tot_server_handler.clone(),
            self.config.0.tot_server_select_mode,
            self.config.0.tot_server_test_method,
            target.latency_test_method.unwrap_or_default(),
            self.ctx.tot_session_manager.get_tot_port_bind_trigger(),
            port_cancel.clone(),
            bind_port,
            collector_factory_clone,
            rt_provider.clone(),
            conn_sem.clone(),
        )
        .await
        .context("TotBackendIO::create failed")
        {
            Ok(backend_io) => backend_io,
            Err(e) => {
                log::error!("TotBackendIO::create failed: {} for port: {}", e, bind_port);
                return Err(e);
            }
        };
        let total_fwd_list = self
            .fwd_handler
            .client
            .servers_iter()
            .map(|x| x.name.clone())
            .collect::<Vec<_>>();
        let tot_config = self.config.0.tot_config.clone();
        let port_cancel_clone = port_cancel.clone();
        rt_provider.get_rt().spawn(async move {
            if let Err(e) = run_client(
                input_io,
                backend_io,
                tot_config,
                total_fwd_list,
                port_cancel_clone,
            )
            .await
            {
                log::error!("tot run client error: {} port: {}", e, bind_port);
            }
            log::info!("tot client exited for port: {}", bind_port);
            Ok::<(), anyhow::Error>(())
        });
        // for udp now using fwd_handler to handle
        let fwd_hammer_handler = self.fwd_handler.clone();
        // let selector = backend_io.get_tot_server_selector();
        let port_cancel_clone = port_cancel.clone();
        let collector_factory_clone = collector_factory.clone();
        let bind_port_clone = bind_port;

        let tot_server_list = self
            .tot_server_handler
            .servers_iter()
            .map(|x| x.address.clone())
            .collect::<Vec<_>>();
        let tot_server_selector = match create_server_selector_hammer_full_link(
            tot_server_list,
            Some(self.config.0.tot_server_select_mode),
            port_cancel.clone(),
            &self.fwd_handler.client,
            self.config.0.tot_server_test_method,
            None,
            Some(rt_provider.get_rt().as_ref().clone()),
            conn_sem.clone(),
        )
        .await
        {
            Ok(selector) => selector,
            Err(e) => {
                log::error!(
                    "create_server_selector_hammer_full_link failed: {} for port: {}",
                    e,
                    bind_port
                );
                return Err(e);
            }
        };
        let piped_stream_creator = Box::new(move |client_ip| {
            Box::pin(create_piped_stream_for_udp(
                fwd_hammer_handler.clone(),
                tot_server_selector.clone(),
                collector_factory_clone.clone(),
                bind_port_clone,
                port_cancel_clone.clone(),
                client_ip,
            )) as BoxFuture<'static, anyhow::Result<CustomPipedStream>>
        });
        let tot_server_handler = self.tot_server_handler.as_ref();
        let udp_handler = match tot_server_handler
            .create_protocol_handler(
                bind_port,
                target, // remote target list
                collector_factory.clone(),
                ping_client.clone(),
                port_cancel.clone(),
                Some(Arc::new(piped_stream_creator)), // using fwd as piped stream
                rt_provider,
                conn_sem,
            )
            .await
        {
            Ok(handler) => handler,
            Err(e) => {
                log::error!(
                    "create tot protocol handler failed: {} for port: {}",
                    e,
                    bind_port
                );
                return Err(e);
            }
        };
        log::info!(
            "create tot protocol handler success for port: {}",
            bind_port
        );
        Ok(Arc::new(Box::new(TotHandler {
            stream_tx: accept_tx,
            session: Some(session),
            udp_handler,
            bind_port,
        })))
    }
}
#[async_trait::async_trait]
impl ProtocolHandler for TotHandler {
    async fn proc_tcp_stream(
        &self,
        stream: TcpStream,
        cancel: CancellationToken,
        _filters: Option<Arc<Vec<FilterType>>>, // todo support traffic filter
        _out_ip_addr: Option<IpAddr>,           // todo support dynamic out ip addr
        _sem: Arc<Semaphore>,
    ) -> anyhow::Result<()> {
        log::debug!(
            "tot proc tcp stream from port: {} peer: {}",
            self.bind_port,
            stream.peer_addr()?
        );
        self.stream_tx
            .send(TotStream::new_from_tcp_stream(stream, cancel))
            .await?;
        Ok(())
    }
    async fn proc_udp_bind(
        &self,
        bind_addr: Option<SocketAddr>,
        out_addr: Option<IpAddr>,
        cancel: CancellationToken,
        #[cfg(feature = "audit_log")] log_client: Arc<LogClient>,
        sub_id: i32,
        conn_limitter: Option<ConnLimitter>,
    ) -> anyhow::Result<()> {
        //current using direct udp later will support multi udp stream aggregation
        log::info!("create tot udp bind on port: {:?}", bind_addr);
        match self
            .udp_handler
            .proc_udp_bind(
                bind_addr,
                out_addr,
                cancel,
                #[cfg(feature = "audit_log")]
                log_client,
                sub_id,
                conn_limitter,
            )
            .await
        {
            Ok(_) => {
                log::info!("tot udp bind on: {:?} success exit", bind_addr);
            }
            Err(e) => {
                log::error!("tot udp bind on: {:?} failed: {e}", bind_addr);
            }
        }
        Ok(())
    }
}
pub struct HammerHandlerForTot {
    client: HammerClient,
}

impl HammerHandlerForTot {
    pub async fn create(
        ctx: &HammerCtx,
        client_config: HammerClientConfig,
    ) -> anyhow::Result<Self> {
        let client = start_hammer_client(
            ctx.rt_provider.clone(),
            client_config,
            "for_tot".to_string(),
            true,
        )
        .await?;
        Ok(Self { client })
    }
    pub async fn proc_tot_proxy_stream(
        self: Arc<Self>,
        dup: DuplexStream,
        target: Address,
        server_name: Option<Arc<Box<str>>>,
        traffic_collector: Option<Arc<RealtimeTrafficCollector>>,
    ) -> anyhow::Result<()> {
        let cancel = self.client.cancel_token.clone();
        let duplex = MemDuplex::new(dup, cancel);
        let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
        self.client
            .push_evt(ConnType::Duplex {
                stream: duplex,
                target: target.clone(),
                rst_tx,
                server_name: server_name.clone(),
                traffic_collector,
                one_rtt: false,
                reuse_tcp: self.client.config.reuse_tcp.unwrap_or(true),
                piped_stream: None,
                remote_stream_connector: None,
            })
            .await?;
        let rst = rst_rx.await?;
        log::debug!(
            "hammer for tot server: {:?} target: {} proxy: rst: {:?}",
            server_name,
            target,
            rst
        );
        if let Some(e) = rst.error {
            match e {
                ProxyReportError::Other(e) => {
                    return Err(e);
                }
                ProxyReportError::IoError(e) => {
                    return Err(e.into());
                }
                e => {
                    return Err(anyhow::anyhow!("unknown error: {:?}", e));
                }
            }
        }
        Ok(())
    }

    pub async fn push_evt(&self, evt: ConnType) -> anyhow::Result<()> {
        self.client.push_evt(evt).await
    }
}

async fn create_piped_stream_for_udp(
    fwd_hammer_handler: Arc<HammerHandlerForTot>,
    tot_server_selector: ServerSelectorForTotal,
    collector_factory: Arc<CollectorFactory>,
    bind_port: u16,
    cancel: CancellationToken,
    client_ip: Option<SocketAddr>,
) -> anyhow::Result<CustomPipedStream> {
    let (dup_a, dup_b) = tokio::io::duplex(8192);
    let target_info = tot_server_selector.get_target_info(None)?;
    let fwd_server_name = target_info.hammer_server.clone();
    let target_addr = target_info.target.clone();
    log::debug!(
        "create piped stream for udp fwd_server_name: {}, tot_server: {}",
        fwd_server_name,
        target_addr
    );
    let collector = collector_factory.create_collector_group(
        client_ip,
        smallvec![Some(fwd_server_name.clone()),],
        bind_port,
    );
    log::debug!(
        "create piped stream for udp port: {} collector group: {}",
        bind_port,
        collector.idx()
    );
    tokio::spawn(async move {
        let handle = collector; // explicit move handle
        match fwd_hammer_handler
            .proc_tot_proxy_stream(
                dup_b,
                target_addr,
                Some(fwd_server_name),
                Some(handle.collector.clone()), // outer forwarder will collect
            )
            .await
        {
            Ok(_) => {
                // log::info!("tot udp proxy pipe bind on: {:?} success exit", bind_port);
            }
            Err(e) => {
                log::error!("tot udp proxy pipe bind on: {:?} failed: {e}", bind_port);
            }
        }
    });
    Ok(CustomPipedStream {
        io: MemDuplex::new(dup_a, cancel),
    })
}

async fn start_tot_for_worker_tun(
    ctx: &TotCtx,
    config: &TotConfigInner,
) -> anyhow::Result<Arc<TotClient>> {
    let hammer_handler = HammerHandlerForTot::create(&ctx.hammer_ctx, config.fwd_config.clone());
    let server_hammer_client = start_hammer_client(
        ctx.hammer_ctx.rt_provider.clone(),
        config.tot_server_list.clone(),
        "for_tot_server".to_string(),
        false,
    );
    let (hammer_handler, server_hammer_client) =
        try_join(hammer_handler, server_hammer_client).await?;

    Ok(Arc::new(TotClient {
        fwd_handler: Arc::new(hammer_handler),
        tot_server_handler: Arc::new(server_hammer_client),
        config: Arc::new(TotClientConfigWrapper(config.clone())),
        ctx: ctx.clone(),
    }))
}

#[derive(Clone)]
pub struct TotClientForWorkerTun {
    client: Arc<TotClient>,
}

impl Debug for TotClientForWorkerTun {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "TotClientForWorkerTun")
    }
}

impl TotClientForWorkerTun {
    pub async fn new(ctx: &TotCtx, config: &TotConfigInner) -> anyhow::Result<Self> {
        let client = start_tot_for_worker_tun(ctx, config).await?;
        Ok(Self { client })
    }

    pub fn fwd_handler_client(&self) -> &HammerClient {
        &self.client.fwd_handler.client
    }

    pub fn fwd_handler(&self) -> Arc<HammerHandlerForTot> {
        self.client.fwd_handler.clone()
    }

    pub fn tot_server_handler(&self) -> &HammerClient {
        &self.client.tot_server_handler
    }

    pub async fn create_handler(
        &self,
        mut target: TargetData,
        port: u16,
        collector_factory: Arc<CollectorFactory>,
        ping_client: Arc<PingClient>,
        port_cancel: CancellationToken,
        rt_provider: Arc<RuntimeProvider>,
        conn_sem: Arc<Semaphore>,
    ) -> anyhow::Result<Arc<Box<dyn ProtocolHandler>>> {
        let client = &self.client;
        // override mode, if only one address, then use tun's select mode
        if target.addr_list.len() == 1 {
            if let Some(mode) = client.tot_server_handler.get_select_mode() {
                target.mode = Some(mode);
            }
        }

        client
            .create_protocol_handler(
                port,
                target,
                collector_factory,
                ping_client,
                port_cancel,
                None,
                rt_provider,
                conn_sem,
            )
            .await
    }
}
