use common::{get_bind_addr_for_port, normalize_ip};
use lru_time_cache::{Entry, LruCache};
use private_tun::snell_impl_ver::{
    config::UDP_BUFFER_SIZE,
    udp_ext::{BufAllocor, UdpExt, UnsafeRawBufRef},
    udp_stream::create_optimized_socket,
};
use smallvec::smallvec;
use socks5_impl::{client::SocksDatagram, protocol::UserKey};
use std::{
    io::Cursor,
    net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddr},
    ops::{Deref, DerefMut},
    sync::Arc,
    time::Instant,
};
use tokio::{io::AsyncReadExt, net::TcpStream, select, sync::mpsc::Sender};
use tokio_util::sync::CancellationToken;

use crate::{
    collector_factory::{CollectorFactory, Handle},
    conn_limitter::{Conn<PERSON><PERSON>it<PERSON><PERSON><PERSON>, ConnLimitter, Guard},
    server_provider::ServerSelector,
};

#[cfg(feature = "audit_log")]
use crate::log_reporter::LogClient;

const UDP_ALLOC_BUF_SIZE: usize = 2000;

#[cfg(feature = "mmsg")]
const STATIC_BUF_SIZE: usize = 5;
#[cfg(not(feature = "mmsg"))]
const STATIC_BUF_SIZE: usize = 1;

#[async_trait::async_trait]
pub trait UdpSender: Send + Sync + 'static {
    const NEED_BUF_RESERVED: bool = false;
    // 发送数据到remote
    async fn send_to_remote(
        &self,
        bufs: &mut [(ReservedOrNot, Option<SocketAddr>)],
        to: SocketAddr,
    ) -> anyhow::Result<()>;
}

#[async_trait::async_trait]
pub trait UdpReceiver: Send + Sync + 'static {
    // 接受remote的数据
    async fn recv_from_remote(&mut self) -> anyhow::Result<Vec<(ReservedOrNot, SocketAddr)>>;
}

#[async_trait::async_trait]
pub trait UdpHandler {
    type Sender: UdpSender;
    type Receiver: UdpReceiver;
    async fn new_client(
        &self,
        from_addr: SocketAddr,
        remote: SocketAddr,
        cancel_token: CancellationToken,
    ) -> anyhow::Result<(Self::Sender, Self::Receiver)>;
}

pub struct UdpSliceIter<'a, T, A: Eq> {
    inner: &'a mut [(T, A)],
    idx: usize,
}
impl<'a, T, A: Eq> UdpSliceIter<'a, T, A> {
    pub fn new(inner: &'a mut [(T, A)]) -> Self {
        Self { inner, idx: 0 }
    }
}

impl<'a, T, A: Eq> Iterator for UdpSliceIter<'a, T, A> {
    type Item = &'a mut [(T, A)];

    fn next(&mut self) -> Option<Self::Item> {
        if self.idx >= self.inner.len() {
            None
        } else {
            let start = self.idx;
            let key = &self.inner[self.idx].1;
            let mut end = self.idx;
            while end < self.inner.len() && &self.inner[end].1 == key {
                end += 1;
            }
            self.idx = end;
            let slice = &mut self.inner[start..end];
            Some(unsafe { std::mem::transmute(slice) })
        }
    }
}

struct ClientData<S> {
    session: S,
    remote: SocketAddr,
    cancel_token: CancellationToken,
    ts: Instant,
    collector_handle: Handle,
}
impl<S> Drop for ClientData<S> {
    fn drop(&mut self) {
        self.cancel_token.cancel();
    }
}

async fn proc_udp_data<H: UdpHandler>(
    all_clients: &mut LruCache<SocketAddr, ClientData<H::Sender>>,
    selector: &ServerSelector,
    buf: &mut [(ReservedOrNot, Option<SocketAddr>)],
    from_addr: SocketAddr,
    bind: SocketAddr,
    socket: &Arc<UdpExt>,
    end_evt_tx: &Sender<(SocketAddr, Instant)>,
    handler: &H,
    collector_factory: &Arc<CollectorFactory>,
    #[cfg(feature = "audit_log")] log_client: &Arc<LogClient>,
    cancel_global: &CancellationToken,
    sub_id: i32,
) -> anyhow::Result<()> {
    let entry = match all_clients.entry(from_addr.clone()) {
        Entry::Occupied(e) => e.into_mut(),
        Entry::Vacant(e) => {
            let remote = selector.get_target_info(None)?;
            log::info!(
                "new udp conn from: {from_addr}, bind: {bind} remote: {remote} sub_id: {sub_id:?}"
            );
            #[cfg(feature = "audit_log")]
            let _ = log_client.info(
                format!("new udp conn from: {from_addr}, bind: {bind} remote: {remote} sub_id: {sub_id:?}"),
                vec![from_addr.to_string(), bind.to_string(), remote.to_string(), format!("{:?}", sub_id)],
            );
            let remote = remote.socket_addr().clone();
            let remote_ip = normalize_ip(remote.ip());
            let cancel_token = CancellationToken::new();
            let (sess_sender, mut sess_receiver) = tokio::time::timeout(
                std::time::Duration::from_secs(3),
                handler.new_client(from_addr, remote.clone(), cancel_token.clone()),
            )
            .await??;
            let cancel_token_clone = cancel_token.clone();
            let end_evt_tx_clone = end_evt_tx.clone();
            let traffic_handle = collector_factory.clone().create_collector_group(
                Some(from_addr.clone()),
                smallvec![None],
                bind.port(),
            );
            let traffic_handle_clone = traffic_handle.collector.clone();
            let socket_clone = socket.clone();
            let remote_clone = remote.clone();
            let global_cancel_clone = cancel_global.clone();
            tokio::spawn(async move {
                loop {
                    select! {
                        biased;
                        r = tokio::time::timeout(std::time::Duration::from_secs(300), sess_receiver.recv_from_remote()) => {
                            let r = match r {
                                Ok(r) => r,
                                Err(_e) => {
                                    log::info!("udp relay proxy timeout from: {from_addr:?} bind: {bind} remote: {remote}");
                                    break;
                                }
                            };
                            let data_arr = match  r {
                                Err(e) => {
                                    log::info!("udp relay proxy err: {e:?} from: {from_addr:?} bind: {bind} remote: {remote}");
                                    break;
                                }
                                Ok(data_arr) => {
                                    if data_arr.is_empty() {
                                        log::info!("udp relay proxy recv from remote is empty from: {from_addr:?} bind: {bind} remote: {remote}");
                                        continue;
                                    }
                                    for (data, from_addr) in data_arr.iter() {
                                        let data = data.as_ref();
                                        let addition_bytes = (data.len() / 1500 + 1) * 44; // 1500 is the max mtu size, 44 is the average ip+tcp+udp+icmp header size
                                        traffic_handle_clone.add_up_bytes(data.len() as u64 + addition_bytes as u64);
                                        let from_addr_ip = normalize_ip(from_addr.ip());
                                        if from_addr_ip != remote_ip || from_addr.port() != remote.port() {
                                            log::info!("udp relay proxy from remote failed from: {from_addr:?} bind: {bind} expect remote: {remote}");
                                            continue;
                                        }
                                    }
                                    data_arr
                                }
                            };
                            match tokio::time::timeout(std::time::Duration::from_secs(5), socket_clone.send_mmsg(data_arr.iter().map(|(data, _)| (data.as_ref(), &from_addr)), data_arr.len())).await {
                                Ok(Ok(_)) => (),
                                Ok(Err(e)) => {
                                    log::error!("udp relay proxy send to client from remote failed: {e:?} bind: {bind} remote: {remote}");
                                    break;
                                }
                                Err(_) => {
                                    log::error!("udp relay proxy send to client from remote timeout bind: {bind}");
                                    break;
                                }
                            }
                        }
                        _ = cancel_token_clone.cancelled() => {
                            log::info!("udp relay proxy cancel from: {from_addr:?} bind: {bind} remote: {remote}");
                            break
                        },
                        _ = global_cancel_clone.cancelled() => {
                            log::info!("udp relay proxy global cancel from: {from_addr:?} bind: {bind} remote: {remote}");
                            break
                        },
                    }
                }
                let _ = end_evt_tx_clone.send((from_addr, Instant::now())).await;
            });

            e.insert(ClientData {
                session: sess_sender,
                cancel_token,
                remote: remote_clone,
                ts: Instant::now(),
                collector_handle: traffic_handle,
            })
        }
    };
    if entry.cancel_token.is_cancelled() {
        all_clients.remove(&from_addr);
    } else {
        log::trace!(
            "udp relay send to remote bind: {bind} data_len: {}",
            buf.len()
        );
        let addition_bytes = (buf.len() / 1500 + 1) * 44; // 1500 is the max mtu size, 44 is the average ip+tcp+udp+icmp header size
        entry
            .collector_handle
            .collector
            .add_up_bytes(buf.len() as u64 + addition_bytes as u64);

        match tokio::time::timeout(
            std::time::Duration::from_secs(5),
            entry.session.send_to_remote(buf, entry.remote),
        )
        .await
        {
            Ok(Ok(_)) => {}
            Ok(Err(e)) => {
                log::error!("udp relay proxy send to remote failed: {e:?} bind: {bind}");
                // to avoid deadlock, send the event must spawn a new task
                let end_evt_tx_clone = end_evt_tx.clone();
                let ts = Instant::now();
                tokio::spawn(async move {
                    let _ = end_evt_tx_clone.send((from_addr, ts)).await;
                });
            }
            Err(_) => {
                log::error!("udp relay proxy send to remote timeout bind: {bind}");
                // to avoid deadlock, send the event must spawn a new task
                let end_evt_tx_clone = end_evt_tx.clone();
                let ts = Instant::now();
                tokio::spawn(async move {
                    let _ = end_evt_tx_clone.send((from_addr, ts)).await;
                });
            }
        }
    }
    Ok(())
}

pub enum ReservedOrNot {
    Reserved(UnsafeRawBufRef, usize),
    NotReserved(UnsafeRawBufRef),
}

impl AsMut<[u8]> for ReservedOrNot {
    fn as_mut(&mut self) -> &mut [u8] {
        match self {
            ReservedOrNot::Reserved(buf, reserved_len) => &mut buf.as_mut()[*reserved_len..],
            ReservedOrNot::NotReserved(buf) => buf.as_mut(),
        }
    }
}

impl ReservedOrNot {
    pub fn set_payload_len(&mut self, len: usize) {
        match self {
            ReservedOrNot::Reserved(buf, reserved_len) => buf.set_len(len + *reserved_len),
            ReservedOrNot::NotReserved(buf) => buf.set_len(len),
        }
    }

    pub fn advance_from_head(&mut self, len: usize) {
        match self {
            ReservedOrNot::Reserved(_buf, reserved_len) => *reserved_len += len,
            ReservedOrNot::NotReserved(buf) => {
                buf.advance(len);
            }
        }
    }
}

impl Deref for ReservedOrNot {
    type Target = [u8];
    fn deref(&self) -> &Self::Target {
        match self {
            ReservedOrNot::Reserved(buf, reserved_len) => &buf.as_ref()[*reserved_len..],
            ReservedOrNot::NotReserved(buf) => buf.as_ref(),
        }
    }
}

impl DerefMut for ReservedOrNot {
    fn deref_mut(&mut self) -> &mut Self::Target {
        match self {
            ReservedOrNot::Reserved(buf, reserved_len) => &mut buf.as_mut()[*reserved_len..],
            ReservedOrNot::NotReserved(buf) => buf.as_mut(),
        }
    }
}

#[allow(unused)]
pub enum BindUdp {
    Port(u16),
    SocketAddr(SocketAddr),
}

struct DynBufPool {
    bufs: Vec<[u8; UDP_ALLOC_BUF_SIZE]>,
    should_reserve_more: bool,
}

impl DynBufPool {
    pub fn new_default() -> Self {
        // defualt is 1 to save memory
        Self {
            bufs: vec![[0; UDP_ALLOC_BUF_SIZE]; 1],
            should_reserve_more: false,
        }
    }
    // set the flag to true to reserve more memory when next get bufs
    pub fn set_reserve_more_flag(&mut self, should_reserve_more: bool) {
        self.should_reserve_more = should_reserve_more;
    }
    fn delay_reserve_more(&mut self, count: usize) {
        if !self.should_reserve_more {
            return;
        }
        if self.bufs.capacity() < count {
            self.bufs.reserve(count - self.bufs.len());
            unsafe {
                self.bufs.set_len(count);
            }
        }
    }
    pub fn get_bufs_mut(&mut self) -> &mut [[u8; UDP_ALLOC_BUF_SIZE]] {
        self.delay_reserve_more(STATIC_BUF_SIZE);
        &mut self.bufs
    }
}

pub async fn proc_udp_bind<H: UdpHandler>(
    bind_addr: BindUdp,
    selector: &ServerSelector,
    cancel: CancellationToken,
    handler: H,
    collector_factory: Arc<CollectorFactory>,
    #[cfg(feature = "audit_log")] log_client: Arc<LogClient>,
    sub_id: i32,
) -> anyhow::Result<()> {
    let bind = match bind_addr {
        BindUdp::Port(port) => get_bind_addr_for_port(port),
        BindUdp::SocketAddr(addr) => addr,
    };

    let mut all_clients = LruCache::<SocketAddr, ClientData<H::Sender>>::with_expiry_duration(
        std::time::Duration::from_secs(310),
    );
    log::info!("start udp socks proxy bind: {bind}");
    let mut try_times = 0;
    let socket = loop {
        match create_optimized_socket(bind, unsafe { UDP_BUFFER_SIZE }, &None) {
            Ok(socket) => {
                break socket;
            }
            Err(e) => {
                log::error!(
                    "bind udp on port: {} failed: {e} wait for try again",
                    bind.port()
                );
                tokio::time::sleep(std::time::Duration::from_secs(5)).await;
                try_times += 1;
                if try_times > 10 {
                    return Err(anyhow::anyhow!(
                        "bind udp on port: {} failed: {e} wait for try again",
                        bind.port()
                    ));
                }
            }
        }
    };
    let socket_ref = socket2::SockRef::from(&socket);
    let _ = socket_ref.set_reuse_port(true);
    let socket: Arc<UdpExt> = Arc::new(socket.try_into()?);
    let reserved_len = if H::Sender::NEED_BUF_RESERVED { 24 } else { 0 };
    let mut buf_pool = DynBufPool::new_default();
    // todo: if long time not recv data, must clear all_clients
    let (rmv_evt_tx, mut rmv_evt_rx) = tokio::sync::mpsc::channel::<(SocketAddr, Instant)>(8);
    loop {
        let mut buf_alloc = StaticBufAlloctor::new(buf_pool.get_bufs_mut(), reserved_len);
        select! {
            biased;
            data = socket.recv_with_buf_alloc(&mut buf_alloc) => {
                if let Ok(_) = data {
                    let mut bufs = buf_alloc.into_bufs();
                    let iter = UdpSliceIter::new(&mut bufs[..]);
                    for slice in iter {
                        let from_addr = slice[0].1;
                        let Some(from_addr) = from_addr else {
                            continue;
                        };
                        match proc_udp_data(&mut all_clients, &selector, slice, from_addr, bind.clone(),
                            &socket, &rmv_evt_tx, &handler, &collector_factory,
                            #[cfg(feature = "audit_log")]
                            &log_client,
                            &cancel, sub_id
                            ).await {
                                Ok(_) => (),
                                Err(e) => {
                                    log::error!("port: {} proc_udp_data failed: {e:?} from_addr: {from_addr:?}", bind.port());
                                }
                            }
                    }
                    buf_pool.set_reserve_more_flag(true);
                } else {
                    log::error!("port: {} recv_from failed", bind.port());
                    break;
                }
            }
            to_remove = rmv_evt_rx.recv() => {
                let Some((to_remove, ts)) = to_remove else {
                    continue;
                };
                if let Some(item) = all_clients.get(&to_remove) {
                    log::info!("port: {} udp socks proxy rmv: {:?}", bind.port(), to_remove);
                    if item.ts < ts {
                        all_clients.remove(&to_remove);
                    } else {
                        log::warn!("port: {} udp try to remove but ts not match: {:?} {:?}", bind.port(), item.ts, to_remove);
                    }
                }
            }
            _ = cancel.cancelled() => {
                log::info!("port: {} udp socks proxy cancel", bind.port());
                break;
            }
        }
    }
    Ok(())
}

pub struct Socks5UdpSender {
    socket: Arc<UdpExt>,
}

pub struct Socks5UdpReceiver {
    from_addr: SocketAddr,
    socket: Arc<UdpExt>,
    stream: TcpStream,
    cancel_token: CancellationToken,
    _guard: Option<Guard>,
    bufs: DynBufPool,
}

#[async_trait::async_trait]
impl UdpSender for Socks5UdpSender {
    const NEED_BUF_RESERVED: bool = true;
    async fn send_to_remote(
        &self,
        bufs: &mut [(ReservedOrNot, Option<SocketAddr>)],
        to: SocketAddr,
    ) -> anyhow::Result<()> {
        for (buf, _to) in bufs.iter_mut() {
            let header = socks5_proto::UdpHeader::new(0, socks5_proto::Address::SocketAddress(to));
            let header_len = header.serialized_len();
            let (mut header_buf, reserved_len) = match buf {
                ReservedOrNot::Reserved(buf, reserved_len) => (
                    &mut buf.as_mut()[*reserved_len - header_len..],
                    reserved_len,
                ),
                ReservedOrNot::NotReserved(_buf) => unsafe { core::hint::unreachable_unchecked() },
            };
            header.write_to_buf(&mut header_buf);
            *reserved_len -= header_len;
        }
        let peer_addr = self.socket.peer_addr()?;
        self.socket
            .send_mmsg(
                bufs.iter().map(|(buf, _)| (buf.as_ref(), &peer_addr)),
                bufs.len(),
            )
            .await?;
        Ok(())
    }
}

pub struct StaticBufAlloctor<'a> {
    ref_bufs: &'a mut [[u8; UDP_ALLOC_BUF_SIZE]],
    reserved_len: usize,
    rst: smallvec::SmallVec<[(ReservedOrNot, Option<SocketAddr>); STATIC_BUF_SIZE]>,
}

impl<'a> StaticBufAlloctor<'a> {
    pub fn new(ref_bufs: &'a mut [[u8; UDP_ALLOC_BUF_SIZE]], reserved_len: usize) -> Self {
        Self {
            ref_bufs,
            reserved_len,
            rst: smallvec::smallvec![],
        }
    }
    pub fn into_bufs(
        self,
    ) -> smallvec::SmallVec<[(ReservedOrNot, Option<SocketAddr>); STATIC_BUF_SIZE]> {
        self.rst
    }
}

impl<'a> BufAllocor for StaticBufAlloctor<'a> {
    type Buf = UnsafeRawBufRef;
    fn alloc(&mut self, idx: usize) -> Option<Self::Buf> {
        if idx >= self.ref_bufs.len() {
            return None;
        }
        let buf = &self.ref_bufs[idx];
        let buf_len = buf.len();
        let r = UnsafeRawBufRef::new(buf as *const [u8] as *mut u8, buf_len - self.reserved_len);
        let item = if self.reserved_len > 0 {
            ReservedOrNot::Reserved(
                UnsafeRawBufRef::new(buf as *const [u8] as *mut u8, buf_len - self.reserved_len),
                self.reserved_len,
            )
        } else {
            ReservedOrNot::NotReserved(UnsafeRawBufRef::new(buf as *const [u8] as *mut u8, buf_len))
        };
        self.rst.push((item, None));
        Some(r)
    }
    fn set_recved_count(&mut self, count: usize) {
        self.rst.truncate(count);
    }
    fn post_proc(&mut self, idx: usize, bytes_recved: usize, from_addr: SocketAddr) {
        if let Some(rst) = self.rst.get_mut(idx) {
            rst.0.set_payload_len(bytes_recved);
            rst.1 = Some(from_addr);
        }
    }
}

#[async_trait::async_trait]
impl UdpReceiver for Socks5UdpReceiver {
    async fn recv_from_remote(&mut self) -> anyhow::Result<Vec<(ReservedOrNot, SocketAddr)>> {
        let mut tcp_tmp_buf = [0; 1];
        let mut buf_alloc = StaticBufAlloctor::new(self.bufs.get_bufs_mut(), 0);
        select! {
            biased;
            r =  self.socket.recv_with_buf_alloc(&mut buf_alloc) => {
                let _ = r?;
                let mut rst = Vec::new();
                for (mut buf, from_addr) in buf_alloc.into_bufs() {
                    let Some(_from_addr) = from_addr else {
                        continue;
                    };
                    let origin_buf = buf.as_mut();
                    let mut cursor = Cursor::new(origin_buf);
                    let header = socks5_proto::UdpHeader::read_from(&mut cursor).await?;
                    let header_len = header.serialized_len();
                    let addr = match header.address {
                        socks5_proto::Address::SocketAddress(addr) => {
                            addr
                        }
                        _ => continue,
                    };
                    buf.advance_from_head(header_len);
                    rst.push((buf, addr));
                }
                self.bufs.set_reserve_more_flag(true);
                Ok(rst)
            },
            _ = self.stream.read(&mut tcp_tmp_buf) => {
                log::info!("socks5 udp proxy tcp streamis end, from_addr: {}", self.from_addr);
                self.cancel_token.cancel();
                Err(anyhow::anyhow!("socks5 udp proxy tcp stream is end, from_addr: {}", self.from_addr))
            }
            _ = self.cancel_token.cancelled()=> {
                log::info!("socks5 udp proxy is end, from_addr: {}", self.from_addr);
                Err(anyhow::anyhow!("socks5 udp proxy is end, from_addr: {}", self.from_addr))
            }
        }
    }
}

pub struct Socks5UdpHandler {
    server: SocketAddr,
    auth: Option<UserKey>,
    conn_limitter: Option<ConnLimitter>,
}
impl Socks5UdpHandler {
    pub fn new(
        server: SocketAddr,
        auth: Option<UserKey>,
        conn_limitter: Option<ConnLimitter>,
    ) -> Self {
        Self {
            server,
            auth,
            conn_limitter,
        }
    }
}

#[async_trait::async_trait]
impl UdpHandler for Socks5UdpHandler {
    type Sender = Socks5UdpSender;
    type Receiver = Socks5UdpReceiver;
    async fn new_client(
        &self,
        from_addr: SocketAddr,
        _remote: SocketAddr,
        cancel_token: CancellationToken,
    ) -> anyhow::Result<(Self::Sender, Self::Receiver)> {
        let _guard = if let Some(conn_limitter) = &self.conn_limitter {
            match conn_limitter.add_conn(from_addr) {
                ConnLimitResult::Ok(guard) => Some(guard),
                e => {
                    return Err(anyhow::anyhow!(
                        "conn limit failed: from_addr: {from_addr:?} e: {:?}",
                        e
                    ))
                }
            }
        } else {
            None
        };
        let proxy = TcpStream::connect(self.server.clone()).await?;
        let _ = proxy.set_nodelay(true);
        let udp_bind_addr = match self.server.ip() {
            IpAddr::V4(_) => SocketAddr::new(IpAddr::V4(Ipv4Addr::UNSPECIFIED), 0),
            IpAddr::V6(_) => SocketAddr::new(IpAddr::V6(Ipv6Addr::UNSPECIFIED), 0),
        };
        let client = create_optimized_socket(udp_bind_addr, unsafe { UDP_BUFFER_SIZE }, &None)?;
        let session = SocksDatagram::udp_associate(proxy, client, self.auth.clone()).await?;
        let (stream, socket) = session.into_inner();
        let socket: Arc<UdpExt> = Arc::new(socket.try_into()?);
        Ok((
            Socks5UdpSender {
                socket: socket.clone(),
            },
            Socks5UdpReceiver {
                from_addr,
                socket,
                stream,
                cancel_token,
                bufs: DynBufPool::new_default(),
                _guard,
            },
        ))
    }
}

pub struct DirectUdpSender {
    socket: Arc<UdpExt>,
}

pub struct DirectUdpReceiver {
    socket: Arc<UdpExt>,
    cancel_token: CancellationToken,
    bufs: DynBufPool,
    _guard: Option<Guard>,
}

#[async_trait::async_trait]
impl UdpSender for DirectUdpSender {
    async fn send_to_remote(
        &self,
        bufs: &mut [(ReservedOrNot, Option<SocketAddr>)],
        to_remote: SocketAddr,
    ) -> anyhow::Result<()> {
        self.socket
            .send_mmsg(
                bufs.iter()
                    .filter_map(|(buf, to)| to.as_ref().map(|_to| (buf.as_ref(), &to_remote))),
                bufs.len(),
            )
            .await?;
        Ok(())
    }
}

#[async_trait::async_trait]
impl UdpReceiver for DirectUdpReceiver {
    async fn recv_from_remote(&mut self) -> anyhow::Result<Vec<(ReservedOrNot, SocketAddr)>> {
        let mut buf_alloc = StaticBufAlloctor::new(self.bufs.get_bufs_mut(), 0);
        select! {
            biased;
            r = self.socket.recv_with_buf_alloc(&mut buf_alloc) => {
                r?;
                let bufs = buf_alloc.into_bufs();
                self.bufs.set_reserve_more_flag(true);
                Ok(bufs.into_iter().filter_map(|(buf, from_addr)| from_addr.map(|addr| (buf, addr))).collect())
            }
            _ = self.cancel_token.cancelled() => {
                Err(anyhow::anyhow!("direct udp proxy is end"))
            }
        }
    }
}

pub struct DirectUdpHandler {
    bind: IpAddr,
    conn_limitter: Option<ConnLimitter>,
}
impl DirectUdpHandler {
    pub fn new(bind: IpAddr, conn_limitter: Option<ConnLimitter>) -> Self {
        Self {
            bind,
            conn_limitter,
        }
    }
}
#[async_trait::async_trait]
impl UdpHandler for DirectUdpHandler {
    type Sender = DirectUdpSender;
    type Receiver = DirectUdpReceiver;
    async fn new_client(
        &self,
        from_addr: SocketAddr,
        remote: SocketAddr,
        cancel_token: CancellationToken,
    ) -> anyhow::Result<(Self::Sender, Self::Receiver)> {
        let _guard = if let Some(conn_limitter) = &self.conn_limitter {
            match conn_limitter.add_conn(from_addr) {
                ConnLimitResult::Ok(guard) => Some(guard),
                _ => None,
            }
        } else {
            None
        };
        let bind_addr = if self.bind.is_unspecified() {
            SocketAddr::new(self.bind, 0)
        } else {
            match (remote.ip(), self.bind) {
                (IpAddr::V4(_), IpAddr::V4(_)) | (IpAddr::V6(_), IpAddr::V6(_)) => {
                    SocketAddr::new(self.bind, 0)
                }
                // using remote ip family as fallback
                _ => match remote.ip() {
                    IpAddr::V4(_) => SocketAddr::new(IpAddr::V4(Ipv4Addr::UNSPECIFIED), 0),
                    IpAddr::V6(_) => SocketAddr::new(IpAddr::V6(Ipv6Addr::UNSPECIFIED), 0),
                },
            }
        };
        let socket = create_optimized_socket(bind_addr, unsafe { UDP_BUFFER_SIZE }, &None)?;
        socket.connect(remote).await?;
        let socket: Arc<UdpExt> = Arc::new(socket.try_into()?);
        Ok((
            DirectUdpSender {
                socket: socket.clone(),
            },
            DirectUdpReceiver {
                socket,
                cancel_token,
                bufs: DynBufPool::new_default(),
                _guard,
            },
        ))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_udp_slice_iter() {
        let mut inner = [(1, 1), (1, 1), (1, 3), (2, 1), (2, 2), (2, 2)];
        let iter = UdpSliceIter::new(&mut inner);
        let mut rst = Vec::new();
        for slice in iter {
            println!("{:?}", slice);
            rst.push(slice);
        }
        assert_eq!(rst.len(), 4);
        assert_eq!(rst[0].len(), 2);
        assert_eq!(rst[1].len(), 1);
        assert_eq!(rst[2].len(), 1);
        assert_eq!(rst[3].len(), 2);
    }
}
