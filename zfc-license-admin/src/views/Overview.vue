<template>
  <div class="overview-page">
    <div class="page-header">
      <h2>Dashboard Overview</h2>
      <p>Monitor license system status and key metrics</p>
    </div>
    
    <div class="stats-grid" v-loading="dashboardStore.isLoading">
      <div class="stat-card active">
        <div class="stat-icon">
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardStore.stats.activeInstances }}</div>
          <div class="stat-label">Active Instances</div>
        </div>
      </div>
      
      <div class="stat-card expired">
        <div class="stat-icon">
          <el-icon><CircleClose /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardStore.stats.expiredInstances }}</div>
          <div class="stat-label">Expired Instances</div>
        </div>
      </div>
      
      <div class="stat-card warning">
        <div class="stat-icon">
          <el-icon><Warning /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardStore.stats.nearExpirationInstances }}</div>
          <div class="stat-label">Near Expiration</div>
          <div class="stat-subtitle">(7 days)</div>
        </div>
      </div>
      
      <div class="stat-card codes">
        <div class="stat-icon">
          <el-icon><Ticket /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ dashboardStore.stats.availableRenewalCodes }}</div>
          <div class="stat-label">Available Codes</div>
        </div>
      </div>
    </div>
    
    <div class="quick-actions">
      <h3>Quick Actions</h3>
      <div class="action-buttons">
        <el-button
          type="primary"
          size="large"
          @click="$router.push('/instances')"
          :icon="Monitor"
        >
          Manage Instances
        </el-button>
        <el-button
          type="success"
          size="large"
          @click="$router.push('/renewal-codes')"
          :icon="Ticket"
        >
          Manage Renewal Codes
        </el-button>
      </div>
    </div>
    
    <el-alert
      v-if="dashboardStore.error"
      :title="dashboardStore.error"
      type="error"
      :closable="false"
      style="margin-top: 24px"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'
import { Monitor, Ticket } from '@element-plus/icons-vue'

const dashboardStore = useDashboardStore()

onMounted(() => {
  dashboardStore.fetchStats()
})
</script>

<style scoped>
.overview-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 32px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
}

.stat-card.active .stat-icon {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
}

.stat-card.expired .stat-icon {
  background: linear-gradient(135deg, #f56c6c, #f89898);
  color: white;
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
  color: white;
}

.stat-card.codes .stat-icon {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 16px;
  color: #606266;
  font-weight: 500;
}

.stat-subtitle {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.quick-actions {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-actions h3 {
  margin: 0 0 24px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  height: 48px;
  padding: 0 24px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  min-width: 200px;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }
  
  .stat-value {
    font-size: 28px;
  }
  
  .quick-actions {
    padding: 24px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons .el-button {
    min-width: auto;
    width: 100%;
  }
}
</style>