<template>
  <el-container class="dashboard-container">
    <el-header class="dashboard-header">
      <div class="header-left">
        <h1>ZFC License Admin</h1>
      </div>
      <div class="header-right">
        <el-button type="danger" @click="handleLogout" :icon="SwitchButton">
          Logout
        </el-button>
      </div>
    </el-header>
    
    <el-container>
      <el-aside class="dashboard-sidebar" width="240px">
        <el-menu
          :default-active="$route.path"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/">
            <el-icon><Odometer /></el-icon>
            <span>Overview</span>
          </el-menu-item>
          <el-menu-item index="/instances">
            <el-icon><Monitor /></el-icon>
            <span>Instances</span>
          </el-menu-item>
          <el-menu-item index="/renewal-codes">
            <el-icon><Ticket /></el-icon>
            <span>Renewal Codes</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <el-main class="dashboard-main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage, ElMessageBox } from 'element-plus'
import { SwitchButton } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      'Are you sure you want to logout?',
      'Confirm Logout',
      {
        confirmButtonText: 'Logout',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
    
    authStore.logout()
    ElMessage.success('Logged out successfully')
    router.push('/login')
  } catch {
    // User cancelled
  }
}
</script>

<style scoped>
.dashboard-container {
  height: 100vh;
}

.dashboard-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left h1 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.dashboard-sidebar {
  background-color: #f8f9fa;
  border-right: 1px solid #e4e7ed;
}

.sidebar-menu {
  border: none;
  background-color: transparent;
  padding: 16px 0;
}

:deep(.sidebar-menu .el-menu-item) {
  margin: 4px 12px;
  border-radius: 8px;
  height: 48px;
  line-height: 48px;
}

:deep(.sidebar-menu .el-menu-item:hover) {
  background-color: rgba(64, 158, 255, 0.1);
}

:deep(.sidebar-menu .el-menu-item.is-active) {
  background-color: #409eff;
  color: white;
}

.dashboard-main {
  background-color: #f5f6fa;
  padding: 24px;
}

@media (max-width: 768px) {
  .dashboard-sidebar {
    width: 200px !important;
  }
  
  .dashboard-main {
    padding: 16px;
  }
  
  .header-left h1 {
    font-size: 18px;
  }
}
</style>