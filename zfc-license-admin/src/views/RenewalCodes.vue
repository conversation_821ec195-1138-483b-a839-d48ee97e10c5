<template>
  <div class="renewal-codes-page">
    <div class="page-header">
      <div class="header-content">
        <h2>Renewal Codes Management</h2>
        <p>Generate and manage license renewal codes</p>
      </div>
      <el-button type="primary" @click="showGenerateDialog = true" :icon="Plus">
        Generate New Codes
      </el-button>
    </div>
    
    <div class="filters-section">
      <el-radio-group v-model="statusFilter" @change="handleFilterChange">
        <el-radio-button label="">All</el-radio-button>
        <el-radio-button label="available">Available</el-radio-button>
        <el-radio-button label="used">Used</el-radio-button>
      </el-radio-group>
    </div>
    
    <div class="table-container">
      <el-table
        :data="renewalCodesStore.codes"
        v-loading="renewalCodesStore.isLoading"
        style="width: 100%"
        stripe
        :default-sort="{ prop: 'createdAt', order: 'descending' }"
      >
        <el-table-column prop="code" label="Renewal Code" min-width="200">
          <template #default="{ row }">
            <div class="code-cell">
              <code class="renewal-code">{{ row.code }}</code>
              <el-button
                type="text"
                :icon="CopyDocument"
                @click="copyToClipboard(row.code)"
                title="Copy to clipboard"
              />
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="duration" label="Duration (days)" min-width="120" sortable>
          <template #default="{ row }">
            <el-tag type="info">{{ row.duration }} days</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="Status" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.usedAt ? 'danger' : 'success'">
              {{ row.usedAt ? 'Used' : 'Available' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="Used By / Used At" min-width="200">
          <template #default="{ row }">
            <div v-if="row.usedAt">
              <div class="used-by">{{ row.usedBy || 'Unknown' }}</div>
              <div class="used-at">{{ formatDate(row.usedAt) }}</div>
            </div>
            <span v-else class="text-muted">Not used</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="Created At" min-width="180" sortable>
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="Actions" min-width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="!row.usedAt"
              type="danger"
              size="small"
              @click="handleDeleteCode(row)"
              :icon="Delete"
            >
              Delete
            </el-button>
            <span v-else class="text-muted">No actions</span>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="renewalCodesStore.pagination.page"
          v-model:page-size="renewalCodesStore.pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="renewalCodesStore.pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- Generate Codes Dialog -->
    <GenerateCodesDialog 
      v-model="showGenerateDialog"
      @generated="handleCodesGenerated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRenewalCodesStore } from '@/stores/renewalCodes'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, CopyDocument, Delete } from '@element-plus/icons-vue'
import type { RenewalCode } from '@/types'
import GenerateCodesDialog from '@/components/GenerateCodesDialog.vue'

const renewalCodesStore = useRenewalCodesStore()

const showGenerateDialog = ref(false)
const statusFilter = ref<'available' | 'used' | ''>('')

onMounted(() => {
  renewalCodesStore.fetchCodes()
})

const handleFilterChange = () => {
  renewalCodesStore.setStatusFilter(statusFilter.value || undefined)
  renewalCodesStore.fetchCodes()
}

const handleSizeChange = (size: number) => {
  renewalCodesStore.pagination.pageSize = size
  renewalCodesStore.fetchCodes()
}

const handleCurrentChange = (page: number) => {
  renewalCodesStore.setPage(page)
  renewalCodesStore.fetchCodes()
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('Copied to clipboard')
  } catch {
    ElMessage.error('Failed to copy to clipboard')
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString()
}

const handleDeleteCode = async (code: RenewalCode) => {
  try {
    await ElMessageBox.confirm(
      `Are you sure you want to delete renewal code "${code.code}"? This action cannot be undone.`,
      'Delete Renewal Code',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'error',
      }
    )
    
    await renewalCodesStore.deleteCode(code.id)
    ElMessage.success('Renewal code deleted successfully')
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || 'Failed to delete renewal code')
    }
  }
}

const handleCodesGenerated = () => {
  // Don't force close the dialog - let GenerateCodesDialog handle its own closing
  // The dialog will show success message and close itself when user clicks "Close"
  renewalCodesStore.fetchCodes()
}
</script>

<style scoped>
.renewal-codes-page {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.header-content h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.filters-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.code-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.renewal-code {
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  font-family: monospace;
  font-weight: 600;
  color: #409eff;
}

.used-by {
  font-weight: 500;
  color: #303133;
}

.used-at {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.text-muted {
  color: #909399;
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filters-section {
    padding: 16px;
  }
  
  :deep(.el-table) {
    font-size: 14px;
  }
  
  .pagination-container {
    padding: 16px;
  }
  
  :deep(.el-pagination) {
    justify-content: center;
  }
  
  :deep(.el-radio-group) {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}
</style>