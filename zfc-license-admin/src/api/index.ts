import axios from 'axios'
import type { 
  ControllerInstance, 
  RenewalCode, 
  DashboardStats, 
  ApiResponse, 
  PaginatedResponse,
  CreateInstanceRequest,
  RenewInstanceRequest,
  GenerateCodesRequest,
  BatchCreateInstancesRequest
} from '@/types'

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8080',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add bearer token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('admin_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      console.log('JWT token expired or invalid, logging out...')
      
      // Clear token from localStorage
      localStorage.removeItem('admin_token')
      
      // Import auth store dynamically to avoid circular dependency
      import('@/stores/auth').then(({ useAuthStore }) => {
        const authStore = useAuthStore()
        authStore.logout()
      })
      
      // Redirect to login page
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  // Check if admin user exists
  checkInitialization: (): Promise<ApiResponse<{ is_initialized: boolean }>> => 
    api.get('/admin/init/check'),
  
  // Create first admin user
  createAdminUser: (data: { username: string; password: string }): Promise<ApiResponse<{ id: string }>> => 
    api.post('/admin/init/setup', data),
  
  // Login with username/password
  login: (data: { username: string; password: string }): Promise<ApiResponse<{ token: string }>> => 
    api.post('/admin/login', data),
  
  // Get current admin user info
  getCurrentAdmin: (): Promise<ApiResponse<{ id: string; username: string; createdAt: string; lastLoginAt?: string }>> => 
    api.get('/admin/me'),
}

// Dashboard API
export const dashboardApi = {
  getStats: (): Promise<ApiResponse<DashboardStats>> => 
    api.get('/admin/dashboard/stats'),
}

// Instances API
export const instancesApi = {
  getInstances: (params?: {
    page?: number
    pageSize?: number
    search?: string
  }): Promise<ApiResponse<PaginatedResponse<ControllerInstance>>> => 
    api.get('/admin/instances', { params }),
  
  createInstance: (data: CreateInstanceRequest): Promise<ApiResponse<ControllerInstance>> => 
    api.post('/admin/instances', data),
  
  batchCreateInstances: (data: BatchCreateInstancesRequest): Promise<ApiResponse<ControllerInstance[]>> => 
    api.post('/admin/instances/batch', data),
  
  updateInstance: (id: string, data: Partial<ControllerInstance>): Promise<ApiResponse<ControllerInstance>> => 
    api.put(`/admin/instances/${id}`, data),
  
  deleteInstance: (id: string): Promise<ApiResponse> => 
    api.delete(`/admin/instances/${id}`),
  
  renewInstance: (id: string, data: RenewInstanceRequest): Promise<ApiResponse<ControllerInstance>> => 
    api.post(`/admin/instances/${id}/renew`, data),
  
  forceExpireSession: (id: string): Promise<ApiResponse> => 
    api.post(`/admin/instances/${id}/force-expire`),
}

// Renewal Codes API
export const renewalCodesApi = {
  getCodes: (params?: {
    page?: number
    pageSize?: number
    status?: 'available' | 'used'
  }): Promise<ApiResponse<PaginatedResponse<RenewalCode>>> => 
    api.get('/admin/renewal-codes', { params }),
  
  generateCodes: (data: GenerateCodesRequest): Promise<ApiResponse<RenewalCode[]>> => 
    api.post('/admin/renewal-codes', data),
  
  deleteCode: (id: string): Promise<ApiResponse> => 
    api.delete(`/admin/renewal-codes/${id}`),
}