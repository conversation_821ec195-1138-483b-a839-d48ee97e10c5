export interface ControllerInstance {
  id: string
  name: string
  licenseExpiresAt: string
  apiKey: string
  entitlements: Record<string, any>
  lastSeenAt?: string
  lastSeenIp?: string
  currentSessionId?: string
  createdAt: string
  updatedAt: string
}

export interface RenewalCode {
  id: string
  code: string
  duration: number
  usedAt?: string
  usedBy?: string
  createdAt: string
}

export interface DashboardStats {
  activeInstances: number
  expiredInstances: number
  nearExpirationInstances: number
  availableRenewalCodes: number
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface CreateInstanceRequest {
  name: string
  initialDuration: number
  entitlements: Record<string, any>
}

export interface RenewInstanceRequest {
  renewalCode: string
}

export interface GenerateCodesRequest {
  duration: number
  quantity: number
}

export interface BatchCreateInstancesRequest {
  nameTemplate: string
  initialDuration: number
  entitlements: Record<string, any>
  quantity: number
}