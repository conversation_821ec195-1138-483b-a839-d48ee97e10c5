<template>
  <el-dialog
    v-model="dialogVisible"
    title="Create New Instance"
    width="600px"
    :close-on-click-modal="false"
    @closed="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="150px"
      label-position="top"
    >
      <el-form-item label="Instance Name" prop="name">
        <el-input
          v-model="form.name"
          placeholder="e.g., Customer X - Production"
        />
      </el-form-item>
      
      <el-form-item label="Initial License Duration (days)" prop="initialDuration">
        <el-input-number
          v-model="form.initialDuration"
          :min="1"
          :max="3650"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="Entitlements (JSON)" prop="entitlements">
        <div style="width: 100%">
          <el-input
            v-model="entitlementsText"
            type="textarea"
            :rows="8"
            placeholder='{ "max_workers": 1, "features": ["basic"] }'
          />
          <div class="form-hint">
            Enter a valid JSON object defining the instance entitlements
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isLoading">
          Create Instance
        </el-button>
      </span>
    </template>
    
    <!-- Success Dialog -->
    <el-dialog
      v-model="showSuccessDialog"
      title="Instance Created Successfully"
      width="500px"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div class="success-content">
        <el-icon class="success-icon"><CircleCheck /></el-icon>
        <p>The new instance has been created successfully!</p>
        <div class="instance-credentials">
          <div class="credential-section">
            <label>Instance ID (Send to customer):</label>
            <div class="id-copy-section">
              <el-input
                :model-value="createdInstanceId"
                readonly
                ref="idInputRef"
              />
              <el-button
                type="primary"
                @click="copyInstanceId"
                :icon="CopyDocument"
              >
                Copy
              </el-button>
            </div>
          </div>
          
          <div class="credential-section">
            <label>API Key (Send to customer):</label>
            <div class="id-copy-section">
              <el-input
                :model-value="createdInstanceApiKey"
                readonly
                ref="apiKeyInputRef"
              />
              <el-button
                type="primary"
                @click="copyApiKey"
                :icon="CopyDocument"
              >
                Copy
              </el-button>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button type="primary" @click="closeSuccessDialog">
          Close
        </el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useInstancesStore } from '@/stores/instances'
import { ElMessage } from 'element-plus'
import { CircleCheck, CopyDocument } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'created'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const instancesStore = useInstancesStore()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const idInputRef = ref()
const apiKeyInputRef = ref()
const isLoading = ref(false)
const showSuccessDialog = ref(false)
const createdInstanceId = ref('')
const createdInstanceApiKey = ref('')

const form = ref({
  name: '',
  initialDuration: 30,
  entitlements: { max_workers: 1 }
})

const entitlementsText = ref('{\n  "max_workers": 1\n}')

const rules: FormRules = {
  name: [
    { required: true, message: 'Please enter instance name', trigger: 'blur' },
    { min: 3, message: 'Name must be at least 3 characters', trigger: 'blur' }
  ],
  initialDuration: [
    { required: true, message: 'Please enter initial duration', trigger: 'blur' },
    { type: 'number', min: 1, max: 3650, message: 'Duration must be between 1 and 3650 days', trigger: 'blur' }
  ],
  entitlements: [
    { required: true, message: 'Please enter entitlements', trigger: 'blur' }
  ]
}

watch(entitlementsText, (newValue) => {
  try {
    form.value.entitlements = JSON.parse(newValue)
  } catch {
    // Invalid JSON, will be caught by validation
  }
})

// Reset success dialog state when main dialog opens
watch(dialogVisible, (newValue) => {
  if (newValue) {
    // Dialog is opening, reset success dialog state
    showSuccessDialog.value = false
    createdInstanceId.value = ''
  }
})

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // Validate JSON
        JSON.parse(entitlementsText.value)
      } catch {
        ElMessage.error('Invalid JSON format in entitlements')
        return
      }
      
      isLoading.value = true
      
      try {
        const instance = await instancesStore.createInstance({
          name: form.value.name,
          initialDuration: form.value.initialDuration,
          entitlements: JSON.parse(entitlementsText.value)
        })
        
        createdInstanceId.value = instance?.id || ''
        createdInstanceApiKey.value = instance?.apiKey || ''
        showSuccessDialog.value = true
        emit('created')
      } catch (error: any) {
        ElMessage.error(error.message || 'Failed to create instance')
      } finally {
        isLoading.value = false
      }
    }
  })
}

const copyInstanceId = async () => {
  try {
    await navigator.clipboard.writeText(createdInstanceId.value)
    ElMessage.success('Instance ID copied to clipboard')
  } catch {
    // Fallback for older browsers
    if (idInputRef.value && idInputRef.value.input) {
      idInputRef.value.input.select()
      document.execCommand('copy')
      ElMessage.success('Instance ID copied to clipboard')
    } else {
      ElMessage.error('Failed to copy to clipboard')
    }
  }
}

const copyApiKey = async () => {
  try {
    await navigator.clipboard.writeText(createdInstanceApiKey.value)
    ElMessage.success('API Key copied to clipboard')
  } catch {
    // Fallback for older browsers
    if (apiKeyInputRef.value && apiKeyInputRef.value.input) {
      apiKeyInputRef.value.input.select()
      document.execCommand('copy')
      ElMessage.success('API Key copied to clipboard')
    } else {
      ElMessage.error('Failed to copy to clipboard')
    }
  }
}

const closeSuccessDialog = () => {
  showSuccessDialog.value = false
  createdInstanceId.value = ''
  createdInstanceApiKey.value = ''
  // Close the main dialog after success dialog is closed
  dialogVisible.value = false
}

const resetForm = () => {
  form.value = {
    name: '',
    initialDuration: 30,
    entitlements: { max_workers: 1 }
  }
  entitlementsText.value = '{\n  "max_workers": 1\n}'
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.form-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.success-content {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 16px;
}

.success-content p {
  font-size: 16px;
  color: #303133;
  margin-bottom: 24px;
}

.instance-credentials {
  text-align: left;
}

.credential-section {
  margin-bottom: 16px;
}

.credential-section label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #303133;
}

.id-copy-section {
  display: flex;
  gap: 8px;
  align-items: center;
}

.id-copy-section .el-input {
  flex: 1;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>