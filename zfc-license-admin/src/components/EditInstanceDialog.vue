<template>
  <el-dialog
    v-model="dialogVisible"
    title="Edit Instance"
    width="500px"
    :close-on-click-modal="false"
    @opened="initForm"
    @closed="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      label-position="top"
    >
      <el-form-item label="Instance Name" prop="name">
        <el-input
          v-model="form.name"
          placeholder="Enter instance name"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isLoading">
          Update Instance
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useInstancesStore } from '@/stores/instances'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { ControllerInstance } from '@/types'

interface Props {
  modelValue: boolean
  instance: ControllerInstance | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const instancesStore = useInstancesStore()

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const isLoading = ref(false)

const form = ref({
  name: ''
})

const rules: FormRules = {
  name: [
    { required: true, message: 'Please enter instance name', trigger: 'blur' },
    { min: 3, message: 'Name must be at least 3 characters', trigger: 'blur' }
  ]
}

const initForm = () => {
  if (props.instance) {
    form.value.name = props.instance.name
  }
}

const handleSubmit = async () => {
  if (!formRef.value || !props.instance) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      isLoading.value = true
      
      try {
        await instancesStore.updateInstance(props.instance!.id, {
          name: form.value.name
        })
        
        ElMessage.success('Instance updated successfully')
        emit('updated')
      } catch (error: any) {
        ElMessage.error(error.message || 'Failed to update instance')
      } finally {
        isLoading.value = false
      }
    }
  })
}

const resetForm = () => {
  form.value = {
    name: ''
  }
  
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>