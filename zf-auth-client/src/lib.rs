use anyhow::Result;
use chrono::{DateTime, Utc};
use jsonwebtoken::{decode, Algorithm, Decoding<PERSON>ey, Validation};
use serde::{Deserialize, Serialize};
use std::{
    path::PathBuf,
    process,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc,
    },
    time::Duration,
};
use tokio::{fs, time::interval};

#[derive(Debug, <PERSON>lone)]
pub struct AuthConfig {
    pub instance_id: String,
    pub server_url: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Entitlements {
    #[serde(default = "default_max_workers")]
    pub max_workers: u32,
    
    #[serde(default = "default_max_users_per_worker")]
    pub max_users_per_worker: u32,
    
    #[serde(default)]
    pub feature_udp_forwarding_enabled: bool,
    
    // Allow for additional entitlements via flattened map
    #[serde(flatten)]
    pub additional: serde_json::Map<String, serde_json::Value>,
}

fn default_max_workers() -> u32 { 1 }
fn default_max_users_per_worker() -> u32 { 10 }

#[derive(Debug, thiserror::Error)]
pub enum AuthError {
    #[error("HTTP error: {0}")]
    HttpError(#[from] reqwest::Error),
    
    #[error("Token invalid: {0}")]
    TokenInvalid(String),
    
    #[error("License expired")]
    LicenseExpired,
    
    #[error("License in use by another instance")]
    LicenseInUse,
    
    #[error("Server rejected request: {0}")]
    ServerRejected(String),
    
    #[error("Network error: {0}")]
    NetworkError(String),
    
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("JSON error: {0}")]
    JsonError(#[from] serde_json::Error),
    
    #[error("JWT error: {0}")]
    JwtError(#[from] jsonwebtoken::errors::Error),
}

#[derive(Debug, Serialize, Deserialize)]
struct JwtClaims {
    sub: String,
    sid: String,
    exp: u64,
    iat: u64,
    entitlements: serde_json::Value,
}

#[derive(Debug, Serialize)]
struct HeartbeatRequest {
    instance_id: String,
}

#[derive(Debug, Deserialize)]
struct HeartbeatResponse {
    token: String,
}

#[derive(Debug, Deserialize)]
struct ErrorResponse {
    error: String,
}

#[derive(Debug, Deserialize)]
struct PublicKeyResponse {
    #[serde(rename = "publicKey")]
    pub public_key: String,
    pub algorithm: String,
}

#[derive(Debug, Deserialize)]
struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize)]
struct ApplyRenewalCodeRequest {
    renewal_code_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LicenseInfo {
    pub id: String,
    pub name: String,
    #[serde(rename = "licenseExpiresAt")]
    pub license_expires_at: DateTime<Utc>,
    pub entitlements: serde_json::Value,
    #[serde(rename = "lastSeenIp")]
    pub last_seen_ip: Option<String>,
    #[serde(rename = "lastSeenAt")]
    pub last_seen_at: Option<DateTime<Utc>>,
    #[serde(rename = "currentSessionId")]
    pub current_session_id: Option<String>,
    #[serde(rename = "createdAt")]
    pub created_at: DateTime<Utc>,
    #[serde(rename = "updatedAt")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RenewalHistoryItem {
    pub date: DateTime<Utc>,
    pub code_id: String,
    pub extended_days: i32,
    pub new_expiry_date: DateTime<Utc>,
    pub status: String,
}


pub struct AuthClient {
    config: AuthConfig,
    client: reqwest::Client,
    public_key_cache: Arc<tokio::sync::RwLock<Option<String>>>,
    token_cache_path: PathBuf,
    shutdown: Arc<AtomicBool>,
    instance_id: Option<String>, // Store the actual instance ID from auth server
    api_key: Option<String>, // Store the instance API key
}

impl AuthClient {
    fn new(config: AuthConfig) -> Result<Self, AuthError> {
        // Set up token cache directory
        let cache_dir = dirs::cache_dir()
            .unwrap_or_else(|| PathBuf::from("."))
            .join("zf-auth-client");
        
        let token_cache_path = cache_dir.join(format!("{}.jwt", config.instance_id));

        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(30))
            .build()?;

        Ok(Self {
            config,
            client,
            public_key_cache: Arc::new(tokio::sync::RwLock::new(None)),
            token_cache_path,
            shutdown: Arc::new(AtomicBool::new(false)),
            instance_id: None,
            api_key: None,
        })
    }

    async fn load_cached_token(&self) -> Option<String> {
        match fs::read_to_string(&self.token_cache_path).await {
            Ok(token) => {
                // Verify the token is still valid
                if self.verify_token(&token).await.is_ok() {
                    Some(token)
                } else {
                    log::warn!("Cached token is invalid, will request new one");
                    None
                }
            }
            Err(_) => None,
        }
    }

    async fn save_token_to_cache(&self, token: &str) -> Result<(), AuthError> {
        // Create cache directory if it doesn't exist
        if let Some(parent) = self.token_cache_path.parent() {
            fs::create_dir_all(parent).await?;
        }
        
        fs::write(&self.token_cache_path, token).await?;
        Ok(())
    }

    async fn get_public_key(&self) -> Result<String, AuthError> {
        // Check cache first
        {
            let cache = self.public_key_cache.read().await;
            if let Some(ref key) = *cache {
                return Ok(key.clone());
            }
        }

        // Retry logic for public key fetching
        const MAX_RETRIES: u32 = 3;
        const RETRY_DELAY_MS: u64 = 1000;
        
        let mut last_error = None;
        
        for attempt in 1..=MAX_RETRIES {
            match self.fetch_public_key_once().await {
                Ok(public_key) => {
                    // Cache the public key on successful fetch
                    {
                        let mut cache = self.public_key_cache.write().await;
                        *cache = Some(public_key.clone());
                    }
                    log::info!("Successfully fetched public key on attempt {}", attempt);
                    return Ok(public_key);
                }
                Err(e) => {
                    last_error = Some(e);
                    if attempt < MAX_RETRIES {
                        log::warn!("Failed to fetch public key on attempt {} of {}: {}. Retrying in {}ms...", 
                            attempt, MAX_RETRIES, last_error.as_ref().unwrap(), RETRY_DELAY_MS);
                        tokio::time::sleep(tokio::time::Duration::from_millis(RETRY_DELAY_MS * attempt as u64)).await;
                    }
                }
            }
        }
        
        log::error!("Failed to fetch public key after {} attempts", MAX_RETRIES);
        Err(last_error.unwrap())
    }

    async fn fetch_public_key_once(&self) -> Result<String, AuthError> {
        // Fetch from server with timeout
        let response = self
            .client
            .get(format!("{}/public-key", self.config.server_url))
            .timeout(tokio::time::Duration::from_secs(10))
            .send()
            .await
            .map_err(|e| {
                if e.is_timeout() {
                    AuthError::NetworkError("Public key fetch timeout".to_string())
                } else if e.is_connect() {
                    AuthError::NetworkError(format!("Connection failed: {}", e))
                } else {
                    AuthError::NetworkError(format!("Request failed: {}", e))
                }
            })?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let api_response: ApiResponse<PublicKeyResponse> = response.json().await
                    .map_err(|e| AuthError::ServerRejected(format!("Invalid JSON response: {}", e)))?;
                    
                if let Some(data) = api_response.data {
                    if data.public_key.is_empty() {
                        return Err(AuthError::ServerRejected("Empty public key received".to_string()));
                    }
                    if data.algorithm != "RS256" {
                        log::warn!("Unexpected algorithm: {}, expected RS256", data.algorithm);
                    }
                    Ok(data.public_key)
                } else {
                    Err(AuthError::ServerRejected(
                        api_response.error.unwrap_or_else(|| "No public key data in response".to_string())
                    ))
                }
            }
            reqwest::StatusCode::SERVICE_UNAVAILABLE => {
                Err(AuthError::NetworkError("Server temporarily unavailable".to_string()))
            }
            reqwest::StatusCode::INTERNAL_SERVER_ERROR => {
                Err(AuthError::NetworkError("Server internal error".to_string()))
            }
            _ => {
                let status = response.status();
                let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(format!("HTTP {}: {}", status, error_text)))
            }
        }
    }

    async fn verify_token(&self, token: &str) -> Result<JwtClaims, AuthError> {
        let mut validation = Validation::new(Algorithm::RS256);
        validation.validate_exp = true;
        
        let public_key_pem = self.get_public_key().await?;
        let decoding_key = DecodingKey::from_rsa_pem(public_key_pem.as_bytes())
            .map_err(|e| AuthError::TokenInvalid(format!("Invalid public key: {}", e)))?;
        
        let token_data = decode::<JwtClaims>(token, &decoding_key, &validation)
            .map_err(|e| AuthError::TokenInvalid(format!("Token verification failed: {}", e)))?;
        Ok(token_data.claims)
    }

    async fn perform_heartbeat(&self, existing_token: Option<&str>) -> Result<String, AuthError> {
        let api_key = self.api_key.as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;
        let instance_id = self.instance_id.as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance ID not available".to_string()))?;

        let mut request_builder = self
            .client
            .post(format!("{}/instance/{}/heartbeat", self.config.server_url, instance_id))
            .header("x-api-key", api_key)
            .json(&HeartbeatRequest {
                instance_id: self.config.instance_id.clone(),
            });

        // Add existing token as Bearer authorization if available
        if let Some(token) = existing_token {
            request_builder = request_builder.bearer_auth(token);
        }

        let response = request_builder.send().await?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let heartbeat_response: HeartbeatResponse = response.json().await?;
                Ok(heartbeat_response.token)
            }
            reqwest::StatusCode::FORBIDDEN => {
                let error_response: ErrorResponse = response.json().await?;
                match error_response.error.as_str() {
                    "LICENSE_EXPIRED" => Err(AuthError::LicenseExpired),
                    "INSTANCE_NOT_FOUND" => Err(AuthError::ServerRejected("Instance not found".to_string())),
                    _ => Err(AuthError::ServerRejected(error_response.error)),
                }
            }
            reqwest::StatusCode::CONFLICT => {
                Err(AuthError::LicenseInUse)
            }
            _ => {
                let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    async fn background_heartbeat_task(self: Arc<Self>) {
        let mut interval = interval(Duration::from_secs(3600)); // 1 hour
        let mut consecutive_failures = 0;
        const MAX_CONSECUTIVE_FAILURES: u32 = 3;

        loop {
            interval.tick().await;

            if self.shutdown.load(Ordering::Relaxed) {
                log::info!("Shutting down background heartbeat task");
                break;
            }

            // Load cached token
            let cached_token = self.load_cached_token().await;

            match self.perform_heartbeat(cached_token.as_deref()).await {
                Ok(new_token) => {
                    consecutive_failures = 0;
                    
                    // Save new token to cache
                    if let Err(e) = self.save_token_to_cache(&new_token).await {
                        log::error!("Failed to save token to cache: {}", e);
                    }
                    
                    log::debug!("Heartbeat successful, token refreshed");
                }
                Err(AuthError::LicenseInUse) => {
                    log::error!("License is in use by another instance, terminating");
                    process::exit(1);
                }
                Err(AuthError::LicenseExpired) => {
                    log::error!("License has expired, terminating");
                    process::exit(1);
                }
                Err(e) => {
                    consecutive_failures += 1;
                    log::error!("Heartbeat failed (attempt {} of {}): {}", 
                              consecutive_failures, MAX_CONSECUTIVE_FAILURES, e);
                    
                    if consecutive_failures >= MAX_CONSECUTIVE_FAILURES {
                        log::error!("Too many consecutive heartbeat failures, terminating");
                        process::exit(1);
                    }
                }
            }
        }
    }

    // Set instance ID and API key after authentication
    pub async fn set_instance_credentials(&mut self, instance_id: String, api_key: String) {
        self.instance_id = Some(instance_id);
        self.api_key = Some(api_key);
    }

    // Apply renewal code to the current instance
    pub async fn apply_renewal_code(&self, renewal_code: String) -> Result<(), AuthError> {
        let instance_id = self.instance_id.as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self.api_key.as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let request = ApplyRenewalCodeRequest {
            renewal_code_id: renewal_code,
        };

        let response = self
            .client
            .post(format!("{}/instance/{}/renew", self.config.server_url, instance_id))
            .header("x-api-key", api_key)
            .json(&request)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::OK => {
                log::info!("Renewal code applied successfully");
                Ok(())
            }
            _ => {
                let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    // Get license information for the current instance
    pub async fn get_license_info(&self) -> Result<LicenseInfo, AuthError> {
        let instance_id = self.instance_id.as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self.api_key.as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let response = self
            .client
            .get(format!("{}/instance/{}/status", self.config.server_url, instance_id))
            .header("x-api-key", api_key)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let api_response: ApiResponse<LicenseInfo> = response.json().await?;
                if let Some(data) = api_response.data {
                    Ok(data)
                } else {
                    Err(AuthError::ServerRejected("No data in response".to_string()))
                }
            }
            _ => {
                let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }

    // Get renewal history for the current instance
    pub async fn get_renewal_history(&self) -> Result<Vec<RenewalHistoryItem>, AuthError> {
        let instance_id = self.instance_id.as_ref()
            .ok_or_else(|| AuthError::ServerRejected("Instance not authenticated".to_string()))?;
        let api_key = self.api_key.as_ref()
            .ok_or_else(|| AuthError::ServerRejected("API key not available".to_string()))?;

        let response = self
            .client
            .get(format!("{}/instance/{}/renewal-history", self.config.server_url, instance_id))
            .header("x-api-key", api_key)
            .send()
            .await?;

        match response.status() {
            reqwest::StatusCode::OK => {
                let api_response: ApiResponse<Vec<RenewalHistoryItem>> = response.json().await?;
                if let Some(data) = api_response.data {
                    Ok(data)
                } else {
                    Ok(vec![]) // Return empty vec if no data
                }
            }
            _ => {
                let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
                Err(AuthError::ServerRejected(error_text))
            }
        }
    }
}

pub async fn initialize_auth(config: AuthConfig, instance_id: String, api_key: String) -> Result<(Arc<AuthClient>, Entitlements), AuthError> {
    let mut client = AuthClient::new(config)?;
    
    // Set instance credentials for future API calls
    client.set_instance_credentials(instance_id, api_key).await;
    
    let client = Arc::new(client);

    // Perform initial heartbeat
    let (_token, entitlements) = {
        let cached_token = client.load_cached_token().await;
        let token = client.perform_heartbeat(cached_token.as_deref()).await?;
        
        // Verify and parse the token
        let claims = client.verify_token(&token).await?;
        let entitlements: Entitlements = serde_json::from_value(claims.entitlements)?;
        
        // Save token to cache
        client.save_token_to_cache(&token).await?;
        
        (token, entitlements)
    };
    
    // Spawn background task with the same client instance
    tokio::spawn({
        let client_clone = client.clone();
        async move {
            client_clone.background_heartbeat_task().await;
        }
    });
    
    log::info!("Authentication successful, entitlements: {:?}", entitlements);
    Ok((client, entitlements))
}

// Apply renewal code using the provided client
pub async fn apply_renewal_code(client: &Arc<AuthClient>, renewal_code: String) -> Result<(), AuthError> {
    client.apply_renewal_code(renewal_code).await
}

// Get license information using the provided client
pub async fn get_license_info(client: &Arc<AuthClient>) -> Result<LicenseInfo, AuthError> {
    client.get_license_info().await
}

// Get renewal history using the provided client
pub async fn get_renewal_history(client: &Arc<AuthClient>) -> Result<Vec<RenewalHistoryItem>, AuthError> {
    client.get_renewal_history().await
}

// Utility function to shutdown the background task gracefully
pub fn shutdown_auth(client: &Arc<AuthClient>) {
    client.shutdown.store(true, Ordering::Relaxed);
    log::info!("Auth shutdown requested");
}