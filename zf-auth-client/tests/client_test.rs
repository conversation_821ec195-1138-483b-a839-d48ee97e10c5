use anyhow::Result;
use zf_auth_client::{initialize_auth, AuthConfig};

// Note: These tests require a running auth server
// They are integration tests that verify the full client-server interaction

#[tokio::test]
#[ignore] // Ignored by default since it requires external server
async fn test_client_initialization() -> Result<()> {
    let config = AuthConfig {
        instance_id: "test-client-instance".to_string(),
        server_url: "http://localhost:8080".to_string(),
    };
    
    // This will fail if no server is running or if the instance doesn't exist
    // In a real test environment, you'd set up a test server first
    let instance_id = "test-instance-123".to_string();
    let api_key = "test-api-key-456".to_string();
    let result = initialize_auth(config, instance_id, api_key).await;
    
    match result {
        Ok((_auth_client, entitlements)) => {
            println!("Entitlements received: {:?}", entitlements);
            assert!(entitlements.max_workers > 0);
        }
        Err(e) => {
            println!("Expected error (no test server): {}", e);
            // This is expected in CI/CD where no auth server is running
        }
    }
    
    Ok(())
}

#[tokio::test]
async fn test_entitlements_defaults() {
    use serde_json::json;
    use zf_auth_client::Entitlements;
    
    // Test that defaults are applied correctly
    let json_value = json!({});
    let entitlements: Entitlements = serde_json::from_value(json_value).unwrap();
    
    assert_eq!(entitlements.max_workers, 1);
    assert_eq!(entitlements.max_users_per_worker, 10);
    assert_eq!(entitlements.feature_udp_forwarding_enabled, false);
    
    // Test with partial data
    let json_value = json!({
        "max_workers": 5,
        "custom_feature": true
    });
    let entitlements: Entitlements = serde_json::from_value(json_value).unwrap();
    
    assert_eq!(entitlements.max_workers, 5);
    assert_eq!(entitlements.max_users_per_worker, 10); // default
    assert_eq!(entitlements.feature_udp_forwarding_enabled, false); // default
    assert_eq!(entitlements.additional.len(), 1);
    assert_eq!(entitlements.additional["custom_feature"], json!(true));
}

#[tokio::test]
async fn test_rsa_jwt_format() {
    // Test that we can parse RSA JWT format (unit test)
    // Since we removed hardcoded secrets, we test format expectations
    
    // Test that the client expects RS256 algorithm
    use jsonwebtoken::{Algorithm, Validation};
    
    let validation = Validation::new(Algorithm::RS256);
    assert_eq!(validation.algorithms, vec![Algorithm::RS256]);
    
    // Test public key PEM format expectations
    let sample_public_key = "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----\n";
    assert!(sample_public_key.contains("-----BEGIN PUBLIC KEY-----"));
    assert!(sample_public_key.contains("-----END PUBLIC KEY-----"));
}