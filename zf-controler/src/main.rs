use std::{
    collections::{HashMap, HashSet},
    sync::atomic::AtomicU64,
};

use anyhow::Result;
use cache::Cache;
use clap::Parser;
use common::{
    batch_writer::BatchWriter, dotenv::dotenv, prisma, redis_lock::DistributedLock,
    reset_traffic_task::ResetTrafficContext, tdengine_init::setup_tdengine,
};
use futures_util::future::BoxFuture;
use preludes::*;
use reset_traffic_task::reset_traffic_background;
use retry_queue::RetryQueue;
use tokio::{signal, sync::Mutex};
use zf_auth_client::{initialize_auth, AuthConfig};

pub mod app;
pub mod error;
pub mod latency;
pub mod online_tracker;
pub mod preludes;
mod stats;
pub mod worker;

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();
    let opt = Opt::parse();

    // Initialize licensing if configured
    let (auth_client, entitlements) = if let (
        Some(instance_id),
        Some(auth_server_url),
        Some(api_key),
    ) = (
        &opt.zfc_instance_id,
        &opt.zfc_auth_server_url,
        &opt.zfc_api_key,
    ) {
        info!("Initializing licensing for instance: {}", instance_id);
        let auth_config = AuthConfig {
            instance_id: instance_id.clone(),
            server_url: auth_server_url.clone(),
        };

        match initialize_auth(auth_config, instance_id.clone(), api_key.clone()).await {
            Ok((auth_client, entitlements)) => {
                info!("Licensing initialized successfully");
                (Some(auth_client), Some(entitlements))
            }
            Err(e) => {
                error!("Failed to initialize licensing: {}", e);
                error!("This instance is not licensed to run. Exiting.");
                std::process::exit(1);
            }
        }
    } else {
        warn!("No licensing configuration found (ZFC_INSTANCE_ID, ZFC_AUTH_SERVER_URL, and ZFC_API_KEY not set)");
        warn!("Running in unlicensed mode - this should only be used for development");
        (None, None)
    };

    let mgmt_priv_key = parse_secret_key(&opt.mgmt_priv_key)?;

    let db = Arc::new(prisma::new_client_with_url(&opt.db_path).await?);
    let redis = setup_redis(&opt).await?;
    let tdengine = setup_tdengine(&opt.tdengine_url, &opt.tdengine_db).await?;
    let batch_writer = BatchWriter::new(Arc::new((*tdengine).clone()));
    let db_clone = db.clone();
    let db_clone1 = db.clone();
    let db_clone2 = db.clone();
    let db_clone3 = db.clone();
    let db_clone4 = db.clone();
    let db_clone5 = db.clone();
    let redis_lock = Arc::new(DistributedLock::new(redis.clone(), "traffic_lock", 5000));
    let ctx =
        Arc::new(
            AppContext {
                opt,
                db,
                redis,
                tdengine,
                batch_writer,
                mgmt_priv_key,
                black_worker_list: Arc::new(Mutex::new(HashSet::new())),
                worker_command_tx: Arc::new(Mutex::new(HashMap::new())),
                request_id: Arc::new(AtomicU64::new(0)),
                traffic_retry_queue: Arc::new(RetryQueue::new()),
                // forward_traffic_retry_queue: Arc::new(RetryQueue::new()),
                redis_lock,
                redis_line_lock: Arc::new(Mutex::new(HashMap::new())),
                entitlements,
                auth_client,
                cached_endpoint_list: Arc::new(Cache::new(
                    Box::new(
                        move |end_point_id: &i32| -> BoxFuture<
                            'static,
                            Result<outbound_endpoint::Data, anyhow::Error>,
                        > {
                            let db_clone1 = db_clone.clone();
                            let end_point_id_clone = end_point_id.clone();
                            Box::pin(async move {
                                let end_point_data = db_clone1
                                    .outbound_endpoint()
                                    .find_unique(outbound_endpoint::id::equals(end_point_id_clone))
                                    .exec()
                                    .await?
                                    .ok_or_else(|| anyhow::anyhow!("endpoint not found"))?;
                                Ok(end_point_data)
                            })
                        },
                    )
                        as Box<
                            dyn Fn(
                                    &i32,
                                ) -> BoxFuture<
                                    'static,
                                    Result<outbound_endpoint::Data, anyhow::Error>,
                                > + Send
                                + Sync
                                + 'static,
                        >,
                    std::time::Duration::from_secs(30),
                )),
                cached_forward_endpoint_list: Arc::new(Cache::new(
                    Box::new(
                        move |id: &i32| -> BoxFuture<
                            'static,
                            Result<forward_endpoint::Data, anyhow::Error>,
                        > {
                            let db_clone_fwd = db_clone1.clone();
                            let id_clone = id.clone();
                            Box::pin(async move {
                                let forward_endpoint_data = db_clone_fwd
                                    .forward_endpoint()
                                    .find_unique(forward_endpoint::id::equals(id_clone))
                                    .exec()
                                    .await?
                                    .ok_or_else(|| anyhow::anyhow!("forward endpoint not found"))?;
                                Ok(forward_endpoint_data)
                            })
                        },
                    )
                        as Box<
                            dyn Fn(
                                    &i32,
                                ) -> BoxFuture<
                                    'static,
                                    Result<forward_endpoint::Data, anyhow::Error>,
                                > + Send
                                + Sync
                                + 'static,
                        >,
                    std::time::Duration::from_secs(30),
                )),
                cached_package_list:
                    Arc::new(
                        Cache::new(
                            Box::new(
                                move |package_id: &i32| -> BoxFuture<
                                    'static,
                                    Result<package::Data, anyhow::Error>,
                                > {
                                    let db_clone_pkg = db_clone4.clone();
                                    let package_id = package_id.clone();
                                    Box::pin(async move {
                                        let package_data = db_clone_pkg
                                            .package()
                                            .find_unique(package::id::equals(package_id))
                                            .with(package::package_lines::fetch(vec![]))
                                            .exec()
                                            .await?
                                            .ok_or_else(|| anyhow::anyhow!("package not found"))?;
                                        Ok(package_data)
                                    })
                                },
                            )
                                as Box<
                                    dyn Fn(
                                            &i32,
                                        ) -> BoxFuture<
                                            'static,
                                            Result<package::Data, anyhow::Error>,
                                        > + Send
                                        + Sync
                                        + 'static,
                                >,
                            std::time::Duration::from_secs(30),
                        ),
                    ),
                cached_subscription_list: Arc::new(
                    Cache::new(
                        Box::new(
                            move |subscription_id: &i32| -> BoxFuture<
                                'static,
                                Result<subscription::Data, anyhow::Error>,
                            > {
                                let db_clone_sub = db_clone5.clone();
                                let subscription_id_clone = subscription_id.clone();
                                Box::pin(async move {
                                    let subscription_data = db_clone_sub
                                        .subscription()
                                        .find_unique(subscription::id::equals(
                                            subscription_id_clone,
                                        ))
                                        .exec()
                                        .await?
                                        .ok_or_else(|| anyhow::anyhow!("subscription not found"))?;
                                    Ok(subscription_data)
                                })
                            },
                        )
                            as Box<
                                dyn Fn(
                                        &i32,
                                    ) -> BoxFuture<
                                        'static,
                                        Result<subscription::Data, anyhow::Error>,
                                    > + Send
                                    + Sync
                                    + 'static,
                            >,
                        std::time::Duration::from_secs(30),
                    ),
                ),
                cached_worker_ports_list: Arc::new(Cache::new(
                    Box::new(
                        move |worker_id: &i32| -> BoxFuture<
                            'static,
                            Result<Vec<worker::UpdateTrafficData::Data>, anyhow::Error>,
                        > {
                            let db_clone_ports = db_clone2.clone();
                            let worker_id_clone = worker_id.clone();
                            Box::pin(async move {
                                let ports_data = db_clone_ports
                                    .port()
                                    .find_many(vec![prisma::port::outbound_endpoint_id::equals(
                                        Some(worker_id_clone),
                                    )])
                                    .select(worker::UpdateTrafficData::select())
                                    .exec()
                                    .await?;
                                Ok(ports_data)
                            })
                        },
                    )
                        as Box<
                            dyn Fn(
                                    &i32,
                                ) -> BoxFuture<
                                    'static,
                                    Result<Vec<worker::UpdateTrafficData::Data>, anyhow::Error>,
                                > + Send
                                + Sync
                                + 'static,
                        >,
                    std::time::Duration::from_secs(30),
                )),
                cached_worker_forward_ports_list: Arc::new(Cache::new(
                    Box::new(
                        move |worker_id: &i32| -> BoxFuture<
                            'static,
                            Result<
                                Vec<worker::UpdateForwardEndpointTrafficData::Data>,
                                anyhow::Error,
                            >,
                        > {
                            let db_clone_forward_ports = db_clone3.clone();
                            let worker_id_clone = worker_id.clone();
                            Box::pin(async move {
                                let ports_data = db_clone_forward_ports
                                    .port()
                                    .find_many(vec![prisma::port::outbound_endpoint_id::equals(
                                        Some(worker_id_clone),
                                    )])
                                    .select(worker::UpdateForwardEndpointTrafficData::select())
                                    .exec()
                                    .await?;
                                Ok(ports_data)
                            })
                        },
                    )
                        as Box<
                            dyn Fn(
                                    &i32,
                                ) -> BoxFuture<
                                    'static,
                                    Result<
                                        Vec<worker::UpdateForwardEndpointTrafficData::Data>,
                                        anyhow::Error,
                                    >,
                                > + Send
                                + Sync
                                + 'static,
                        >,
                    std::time::Duration::from_secs(30),
                )),
            },
        );
    let reset_traffic_ctx = Arc::new(ResetTrafficContext::new(
        ctx.db.clone(),
        ctx.redis.clone(),
        ctx.traffic_retry_queue.clone(),
        ctx.redis_lock.clone(),
        ctx.redis_line_lock.clone(),
        true,
    ));
    tokio::spawn(reset_traffic_background(reset_traffic_ctx.clone()));
    let app_handle = tokio::spawn(app::start_app(ctx.clone(), reset_traffic_ctx.clone()));

    tokio::select! {
        ret = app_handle => {
            match ret {
                Ok(ret) => {
                    if let Err(e) = ret {
                        error!("app_handle: {:?}", e)
                    }
                }
                Err(e) => error!("app_handle: {:?}", e)
            }
        }
        ret = signal::ctrl_c() => {
            if let Err(e) = ret {
                error!("ctrl_c: {e}")
            }
        }
    }
    info!("Exiting...");
    Ok(())
}
