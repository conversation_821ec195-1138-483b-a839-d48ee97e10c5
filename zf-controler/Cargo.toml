[package]
name = "zf-controler"
version = "0.1.0"
edition = "2021"
publish = false

[dependencies]
clap = { version = "4.1", features = ["derive", "env"] }
common = { path = "../common" }
anyhow = { version = "1.0.75", features = ["backtrace"] }
env_logger = {version = "0", default-features=false, features=["humantime"]}
log = "0.4"
tokio = { workspace = true}
serde_json = "1.0.107"
serde_yaml = "0.9.25"
serde = "1.0.188"
prisma-client-rust = { git = "https://github.com/<PERSON><PERSON><PERSON>ovich/prisma-client-rust", tag = "0.6.11", no-default-features = true, features = ["postgresql"] }
sql-query-connector = { git = "https://github.com/Brendonovich/prisma-engines", tag = "pcr-0.6.10", features = ["vendored-openssl"] }
redis = { version = "0.23.3", features = ["tokio-comp", "tls", "serde_json", "serde", "tokio-native-tls-comp", "json"] }
bb8 = "0.8.1"
bb8-redis = "0.13.1"
primitive-types = "0.12.1"
warp = "0.3.6"
futures-util = { version = "0.3.28", features = ["futures-sink"] }
k256 = { version = "0.13.1", features = ["default", "ecdh", "ecdsa-core", "serde"] }
taos = { version = "0.12"}
chrono = "0"
thiserror = "2"
async-trait = "0.1"
rand = "0.9"
uuid = { version = "1.8.0", features = ["v4", "fast-rng"] }
itertools = "0.10"
private_tun = { workspace = true }
smallvec = "1"
lru_time_cache = { version = "0.11", default-features=false}
zf-auth-client = { path = "../zf-auth-client" }
reqwest = { version = "0.12", default-features=false, features = ["json", "rustls-tls"] }
[[bin]]
name = "zf-controler"
path = "src/main.rs"
